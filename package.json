{"name": "bcon", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 3409", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@arco-design/web-react": "^2.64.0", "@heroicons/react": "^2.1.5", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/themes": "^3.1.3", "@react-google-maps/api": "^2.20.6", "@tanstack/react-query": "^5.61.3", "apexcharts": "^3.53.0", "axios": "^1.7.7", "chart.js": "^4.4.4", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "currency-symbol-map": "^5.1.0", "dotenv": "^16.4.5", "file-saver": "^2.0.5", "formik": "^2.4.6", "lucide-react": "^0.439.0", "moment": "^2.30.1", "react": "^18.3.1", "react-apexcharts": "^1.4.1", "react-calendar": "^5.0.0", "react-chartjs-2": "^5.2.0", "react-currency-flags": "^0.1.2", "react-dom": "^18.3.1", "react-gauge-chart": "^0.5.1", "react-hot-toast": "^2.4.1", "react-intl": "^6.6.8", "react-number-format": "^5.4.2", "react-onesignal": "^3.2.2", "react-qr-code": "^2.0.15", "react-router-dom": "^6.26.1", "react-select": "^5.8.0", "styled-components": "^6.1.13", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "yup": "^1.4.0"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/node": "^22.5.4", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "vite": "^5.4.1"}}