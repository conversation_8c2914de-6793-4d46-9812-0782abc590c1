# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
/.yarn
node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/.pnp
.pnp.js

# testing
/coverage

# production
/dist.zip
.env
# misc
.env.local
.env.development.local
.env.test.local
.env.production.local

/.yarn