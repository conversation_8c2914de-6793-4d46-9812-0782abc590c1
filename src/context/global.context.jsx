/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react-refresh/only-export-components */
import { ApiServiceAdmin } from "@/service/admin-services";
import { Spinner } from "@radix-ui/themes";
import { useQuery } from "@tanstack/react-query";
import { createContext, useContext } from "react";

const MainContext = createContext(null);

export const MainProvider = ({ children }) => {
  const user_id = localStorage.getItem("user_id");
  const isAuthenticated = user_id ? true : false;

  const { data, refetch, isLoading, isFetching } = useQuery({
    queryKey: ["dashboardAdmin"],
    queryFn: () => ApiServiceAdmin.GetUserQuery(user_id),
    enabled: !!user_id,
  });

  const currentUser = data?.data;

  const user_data = currentUser ? currentUser : null;

  const currencyKeys = user_data && Object.keys(user_data?.transactionVolume);
  const fundRequestLogs = user_data?.userWithdrawalRequest;

  const currencyOptionsDashboard =
    user_data &&
    currencyKeys?.map((itm) => {
      return {
        label: itm,
        value: itm,
        ...user_data?.transactionVolume[itm],
      };
    });

  const menuAccessRoutes = user_data?.userRoleMenuAccess;

  if (isLoading) {
    return (
      <div
        style={{
          display: "grid",
          placeItems: "center",
          height: "100vh",
        }}
      >
        <Spinner
          size={"3"}
          style={{
            width: "60px",
            height: "60px",
          }}
        />
      </div>
    );
  }
  return (
    <MainContext.Provider
      value={{
        menuAccessRoutes,
        isAuthenticated,
        user_data,
        user_id,
        user_role: user_data?.role,
        refetchUserData: refetch,
        userIsLoading: isLoading || isFetching,
        currencyOptionsDashboard,
        fundRequestLogs,
      }}
    >
      {children}
    </MainContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(MainContext);
  if (!context)
    throw new Error("Use Auth must be used within an Auth Provider");
  return context;
};

export default MainContext;
