import { ApiServiceAdmin } from "@/service/admin-services";
import { useMutation } from "@tanstack/react-query";
import { useState } from "react";

type TypeFace =
  | "view_comments"
  | "suspicious"
  | "cancel"
  | "add_comment"
  | "confirm"
  | "hold"
  | "revert_hold"
  | "release"
  | "revert"
  | "pay"
  | "paid"
  | "released";

export default function UseCustomerTableHook(recall: () => void) {
  const [open, setOpen] = useState(false);

  const { mutate: markAsSuspicious, isPending: loadingSuspicious } =
    useMutation({
      mutationFn: ApiServiceAdmin.MarkTransactionAsSuspiciousMutation,
      onSuccess: () => {
        setOpen(false);
        recall();
      },
      onError: () => {
        return;
      },
    });

  const { mutate: cancelTransaction, isPending: loadingCancel } = useMutation({
    mutationFn: ApiServiceAdmin.CancelTransactionMutation,
    onSuccess: () => {
      setOpen(false);
      recall();
    },
    onError: () => {
      return;
    },
  });

  const { mutate: confirmTransaction, isPending: loadingConfirm } = useMutation(
    {
      mutationFn: ApiServiceAdmin.ConfirmTransactionMutation,
      onSuccess: () => {
        setOpen(false);
        recall();
      },
      onError: () => {
        return;
      },
    }
  );

  const { mutate: holdTransaction, isPending: loadingHold } = useMutation({
    mutationFn: ApiServiceAdmin.HoldTransactionMutation,
    onSuccess: () => {
      setOpen(false);
      recall();
    },
    onError: () => {
      return;
    },
  });

  const { mutate: revertHold, isPending: loadingRevertHold } = useMutation({
    mutationFn: ApiServiceAdmin.RevertHoldTransactionMutation,
    onSuccess: () => {
      setOpen(false);
      recall();
    },
    onError: () => {
      return;
    },
  });

  const { mutate: revertTransaction, isPending: loadingRevert } = useMutation({
    mutationFn: ApiServiceAdmin.RevertTransactionMutation,
    onSuccess: () => {
      setOpen(false);
      recall();
    },
    onError: () => {
      return;
    },
  });
  const { mutate: payTransaction, isPending: loadingPay } = useMutation({
    mutationFn: ApiServiceAdmin.PayTransactionMutation,
    onSuccess: () => {
      setOpen(false);
      recall();
    },
    onError: () => {
      return;
    },
  });
  const { mutate: markAsPaid, isPending: loadingPaid } = useMutation({
    mutationFn: ApiServiceAdmin.MarkAsPaidTransactionMutation,
    onSuccess: () => {
      setOpen(false);
      recall();
    },
    onError: () => {
      return;
    },
  });

  const callMenuByType = (type: TypeFace, item, body) => {
    if (type === "suspicious") {
      markAsSuspicious({ id: item, body });
    } else if (type === "revert_hold") {
      revertHold({ id: item, body });
    } else if (type === "revert") {
      revertHold({ id: item, body });
    } else if (type === "pay") {
      payTransaction({ id: item, body });
    } else if (type === "paid") {
      markAsPaid({ id: item, body });
    } else if (type === "hold") {
      holdTransaction({ id: item, body });
    } else if (type === "confirm") {
      confirmTransaction({ id: item, body });
    } else if (type === "cancel") {
      cancelTransaction({ id: item, body });
    } else if (type === "released") {
    } else if (type === "release") {
    }
  };

  return {
    callMenuByType,
    isPending:
      loadingSuspicious ||
      loadingCancel ||
      loadingConfirm ||
      loadingHold ||
      loadingPaid ||
      loadingPay ||
      loadingRevert ||
      loadingRevertHold,
    open,
    setOpen,
  };
}
