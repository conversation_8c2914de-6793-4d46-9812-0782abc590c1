export default function CustomerIcon(props) {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M5.90625 11.25C7.92554 11.25 9.5625 9.61304 9.5625 7.59375C9.5625 5.57446 7.92554 3.9375 5.90625 3.9375C3.88696 3.9375 2.25 5.57446 2.25 7.59375C2.25 9.61304 3.88696 11.25 5.90625 11.25Z"
        stroke="#DEDFDF"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M0.719727 14.062C1.28159 13.1981 2.05032 12.4883 2.95613 11.9968C3.86195 11.5054 4.87615 11.248 5.90668 11.248C6.93721 11.248 7.95141 11.5054 8.85723 11.9968C9.76304 12.4883 10.5318 13.1981 11.0936 14.062"
        stroke="#DEDFDF"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M12.0938 11.25C13.1242 11.2494 14.1385 11.5064 15.0444 11.9976C15.9503 12.4888 16.719 13.1986 17.2807 14.0625"
        stroke="#DEDFDF"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M10.7363 4.19766C11.2365 3.99818 11.7745 3.91163 12.312 3.94419C12.8494 3.97676 13.3731 4.12764 13.8455 4.38605C14.3179 4.64445 14.7274 5.00401 15.0447 5.43903C15.362 5.87405 15.5794 6.37379 15.6812 6.90253C15.783 7.43128 15.7667 7.97599 15.6336 8.49773C15.5004 9.01947 15.2537 9.50536 14.911 9.92069C14.5683 10.336 14.1381 10.6705 13.6512 10.9003C13.1642 11.1301 12.6325 11.2495 12.0941 11.25"
        stroke="#DEDFDF"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
}
