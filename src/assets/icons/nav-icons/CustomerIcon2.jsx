export default function CustomerIcon2(props) {
  return (
    <svg
      width="19"
      height="20"
      viewBox="0 0 19 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M5.90625 11.25C7.92554 11.25 9.5625 9.61304 9.5625 7.59375C9.5625 5.57446 7.92554 3.9375 5.90625 3.9375C3.88696 3.9375 2.25 5.57446 2.25 7.59375C2.25 9.61304 3.88696 11.25 5.90625 11.25Z"
        stroke="#DEDFDF"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M0.719727 14.062C1.28159 13.1981 2.05032 12.4883 2.95613 11.9968C3.86195 11.5054 4.87615 11.248 5.90668 11.248C6.93721 11.248 7.95141 11.5054 8.85723 11.9968C9.76304 12.4883 10.5318 13.1981 11.0936 14.062"
        stroke="#DEDFDF"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M12.0938 11.25C13.1242 11.2494 14.1385 11.5064 15.0444 11.9976C15.9503 12.4888 16.719 13.1986 17.2807 14.0625"
        stroke="#DEDFDF"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M10.7363 4.19766C11.2365 3.99818 11.7745 3.91163 12.312 3.94419C12.8494 3.97676 13.3731 4.12764 13.8455 4.38605C14.3179 4.64445 14.7274 5.00401 15.0447 5.43903C15.362 5.87405 15.5794 6.37379 15.6812 6.90253C15.783 7.43128 15.7667 7.97599 15.6336 8.49773C15.5004 9.01947 15.2537 9.50536 14.911 9.92069C14.5683 10.336 14.1381 10.6705 13.6512 10.9003C13.1642 11.1301 12.6325 11.2495 12.0941 11.25"
        stroke="#DEDFDF"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M17.9914 13.8206L16.1794 12.0086C16.0736 11.9035 15.9306 11.8442 15.7814 11.8438H13.2186C13.0694 11.8442 12.9264 11.9035 12.8206 12.0086L11.0086 13.8206C10.9035 13.9264 10.8442 14.0694 10.8438 14.2186V16.7814C10.8442 16.9306 10.9035 17.0736 11.0086 17.1794L12.8206 18.9914C12.9264 19.0965 13.0694 19.1558 13.2186 19.1562H15.7814C15.9306 19.1558 16.0736 19.0965 16.1794 18.9914L17.9914 17.1794C18.0965 17.0736 18.1558 16.9306 18.1562 16.7814V14.2186C18.1558 14.0694 18.0965 13.9264 17.9914 13.8206ZM14.2188 13.8125C14.2188 13.7379 14.2484 13.6664 14.3011 13.6136C14.3539 13.5609 14.4254 13.5312 14.5 13.5312C14.5746 13.5312 14.6461 13.5609 14.6989 13.6136C14.7516 13.6664 14.7812 13.7379 14.7812 13.8125V15.7812C14.7812 15.8558 14.7516 15.9274 14.6989 15.9801C14.6461 16.0329 14.5746 16.0625 14.5 16.0625C14.4254 16.0625 14.3539 16.0329 14.3011 15.9801C14.2484 15.9274 14.2188 15.8558 14.2188 15.7812V13.8125ZM14.5 17.4688C14.4166 17.4688 14.335 17.444 14.2656 17.3977C14.1962 17.3513 14.1422 17.2854 14.1102 17.2083C14.0783 17.1312 14.07 17.0464 14.0862 16.9646C14.1025 16.8827 14.1427 16.8076 14.2017 16.7486C14.2607 16.6896 14.3359 16.6494 14.4177 16.6331C14.4995 16.6168 14.5844 16.6252 14.6614 16.6571C14.7385 16.689 14.8044 16.7431 14.8508 16.8125C14.8971 16.8819 14.9219 16.9634 14.9219 17.0469C14.9219 17.1588 14.8774 17.2661 14.7983 17.3452C14.7192 17.4243 14.6119 17.4688 14.5 17.4688Z"
        fill="#DEDFDF"
      />
    </svg>
  );
}
