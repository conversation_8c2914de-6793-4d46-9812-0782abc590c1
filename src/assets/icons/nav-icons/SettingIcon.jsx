export default function SettingIcon(props) {
  return (
    <svg
      width="18"
      height="19"
      viewBox="0 0 18 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M8.99997 6.33266C7.40229 6.33266 6.10712 7.62784 6.10712 9.22552C6.10712 10.8232 7.40229 12.1184 8.99997 12.1184C9.86707 12.1184 10.6451 11.7369 11.1753 11.1326C11.622 10.6234 11.8928 9.9561 11.8928 9.22552C11.8928 8.92984 11.8485 8.64452 11.766 8.37585C11.4031 7.1928 10.302 6.33266 8.99997 6.33266ZM7.0714 9.22552C7.0714 8.1604 7.93485 7.29694 8.99997 7.29694C10.0651 7.29694 10.9285 8.1604 10.9285 9.22552C10.9285 10.2906 10.0651 11.1541 8.99997 11.1541C7.93485 11.1541 7.0714 10.2906 7.0714 9.22552ZM13.9559 14.6221L12.8454 14.1346C12.5277 13.9953 12.1545 14.0157 11.8543 14.1896C11.554 14.3634 11.3625 14.67 11.3243 15.0154L11.1908 16.2233C11.1633 16.4724 10.9826 16.6773 10.7392 16.7353C9.59543 17.008 8.40373 17.008 7.25992 16.7353C7.0166 16.6773 6.83588 16.4724 6.80835 16.2233L6.67503 15.0172C6.63588 14.6725 6.43575 14.3668 6.13574 14.1935C5.83572 14.0203 5.47144 13.9999 5.15478 14.1385L4.044 14.6261C3.81392 14.7271 3.54529 14.6717 3.37373 14.4878C2.57445 13.6314 1.97949 12.6044 1.63361 11.4843C1.55912 11.243 1.64519 10.9809 1.84812 10.8311L2.82995 10.1064C3.10961 9.90054 3.27478 9.5736 3.27478 9.2259C3.27478 8.8782 3.10961 8.55126 2.82959 8.34512L1.84837 7.62163C1.64514 7.47178 1.55898 7.20936 1.63374 6.96788C1.98022 5.84877 2.57549 4.82296 3.37479 3.96764C3.54652 3.78387 3.81528 3.72867 4.04532 3.82992L5.15116 4.31665C5.46936 4.45656 5.83516 4.43542 6.1368 4.25886C6.43713 4.08432 6.63707 3.77743 6.67567 3.43201L6.80998 2.22487C6.83802 1.97278 7.02289 1.76643 7.27001 1.71136C7.83642 1.58515 8.41437 1.51808 9.00838 1.51123C9.58799 1.51792 10.1653 1.58502 10.7311 1.71144C10.978 1.76662 11.1627 1.97291 11.1908 2.22487L11.3252 3.43293C11.386 3.98761 11.8534 4.40773 12.4107 4.40831C12.5604 4.40855 12.7086 4.3773 12.847 4.31594L13.953 3.82913C14.1831 3.72788 14.4519 3.78308 14.6236 3.96684C15.4229 4.82216 16.0182 5.84798 16.3646 6.96709C16.4393 7.20841 16.3533 7.47066 16.1504 7.62057L15.17 8.3446C14.8903 8.55047 14.7214 8.87741 14.7214 9.22511C14.7214 9.5728 14.8903 9.89974 15.1706 10.1061L16.1518 10.8303C16.3549 10.9802 16.4409 11.2425 16.3662 11.4839C16.0199 12.6028 15.4249 13.6285 14.6261 14.484C14.4545 14.6677 14.186 14.7231 13.9559 14.6221ZM10.4548 14.4947C10.606 14.0266 10.9214 13.6154 11.3711 13.3551C11.9379 13.0268 12.6344 12.9892 13.2326 13.2515L14.0966 13.6307C14.6513 12.9886 15.081 12.2478 15.3631 11.4467L14.5989 10.8826L14.5983 10.8822C14.0773 10.4983 13.7571 9.88612 13.7571 9.22511C13.7571 8.56456 14.0769 7.95231 14.5975 7.56865L14.5983 7.56804L15.3615 7.00442C15.0793 6.20328 14.6495 5.46242 14.0945 4.82043L13.2378 5.19749L13.2363 5.19815C12.9756 5.31345 12.6946 5.37297 12.4096 5.3726C11.3602 5.37125 10.4815 4.58039 10.3668 3.53918L10.3666 3.53806L10.2622 2.59957C9.84869 2.52259 9.42922 2.48108 9.00835 2.47559C8.575 2.4812 8.15276 2.52284 7.73851 2.59973L7.63399 3.5391C7.56157 4.18723 7.18667 4.76346 6.62263 5.09182C6.05528 5.42336 5.36431 5.46376 4.7627 5.19923L3.90387 4.82122C3.34893 5.46316 2.91913 6.20394 2.63695 7.00501L3.40185 7.569C3.92857 7.95704 4.23906 8.57214 4.23906 9.2259C4.23906 9.87944 3.92875 10.4946 3.40221 10.8825L2.63672 11.4476C2.91856 12.2498 3.34829 12.9917 3.90343 13.6347L4.76832 13.255C5.36619 12.9935 6.05294 13.0322 6.618 13.3585C7.18325 13.685 7.55955 14.2603 7.63316 14.9084L7.63349 14.9113L7.73752 15.8525C8.57092 16.0165 9.42824 16.0165 10.2616 15.8525L10.3659 14.9095C10.3815 14.768 10.4114 14.629 10.4548 14.4947Z"
        fill="#DEDFDF"
      />
    </svg>
  );
}
