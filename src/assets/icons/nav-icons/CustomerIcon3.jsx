export default function CustomerIcon3(props) {
  return (
    <svg
      width="20"
      height="19"
      viewBox="0 0 20 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M5.90625 11.25C7.92554 11.25 9.5625 9.61304 9.5625 7.59375C9.5625 5.57446 7.92554 3.9375 5.90625 3.9375C3.88696 3.9375 2.25 5.57446 2.25 7.59375C2.25 9.61304 3.88696 11.25 5.90625 11.25Z"
        stroke="#DEDFDF"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M0.719727 14.062C1.28159 13.1981 2.05032 12.4883 2.95613 11.9968C3.86195 11.5054 4.87615 11.248 5.90668 11.248C6.93721 11.248 7.95141 11.5054 8.85723 11.9968C9.76304 12.4883 10.5318 13.1981 11.0936 14.062"
        stroke="#DEDFDF"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M12.0938 11.25C13.1242 11.2494 14.1385 11.5064 15.0444 11.9976C15.9503 12.4888 16.719 13.1986 17.2807 14.0625"
        stroke="#DEDFDF"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M10.7363 4.19766C11.2365 3.99818 11.7745 3.91163 12.312 3.94419C12.8494 3.97676 13.3731 4.12764 13.8455 4.38605C14.3179 4.64445 14.7274 5.00401 15.0447 5.43903C15.362 5.87405 15.5794 6.37379 15.6812 6.90253C15.783 7.43128 15.7667 7.97599 15.6336 8.49773C15.5004 9.01947 15.2537 9.50536 14.911 9.92069C14.5683 10.336 14.1381 10.6705 13.6512 10.9003C13.1642 11.1301 12.6325 11.2495 12.0941 11.25"
        stroke="#DEDFDF"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M19.3254 16.6126L16.251 11.2734C16.1741 11.1426 16.0645 11.0341 15.9328 10.9588C15.8011 10.8834 15.6521 10.8438 15.5004 10.8438C15.3487 10.8438 15.1996 10.8834 15.068 10.9588C14.9363 11.0341 14.8266 11.1426 14.7498 11.2734L11.6754 16.6126C11.6015 16.7391 11.5625 16.883 11.5625 17.0295C11.5625 17.176 11.6015 17.3199 11.6754 17.4465C11.7512 17.5781 11.8607 17.6871 11.9926 17.7624C12.1245 17.8377 12.2741 17.8766 12.426 17.875H18.5748C18.7266 17.8765 18.876 17.8376 19.0077 17.7623C19.1395 17.6869 19.2489 17.578 19.3247 17.4465C19.3987 17.32 19.4378 17.1761 19.4379 17.0296C19.438 16.8831 19.3992 16.7391 19.3254 16.6126ZM15.2191 13.6563C15.2191 13.5817 15.2488 13.5101 15.3015 13.4574C15.3542 13.4047 15.4258 13.375 15.5004 13.375C15.575 13.375 15.6465 13.4047 15.6993 13.4574C15.752 13.5101 15.7816 13.5817 15.7816 13.6563V15.0625C15.7816 15.1371 15.752 15.2087 15.6993 15.2614C15.6465 15.3141 15.575 15.3438 15.5004 15.3438C15.4258 15.3438 15.3542 15.3141 15.3015 15.2614C15.2488 15.2087 15.2191 15.1371 15.2191 15.0625V13.6563ZM15.5004 16.75C15.4169 16.75 15.3354 16.7253 15.266 16.6789C15.1966 16.6326 15.1425 16.5667 15.1106 16.4896C15.0787 16.4125 15.0703 16.3277 15.0866 16.2458C15.1029 16.164 15.1431 16.0888 15.2021 16.0298C15.2611 15.9708 15.3362 15.9307 15.4181 15.9144C15.4999 15.8981 15.5847 15.9065 15.6618 15.9384C15.7389 15.9703 15.8048 16.0244 15.8512 16.0938C15.8975 16.1631 15.9223 16.2447 15.9223 16.3281C15.9223 16.44 15.8778 16.5473 15.7987 16.6265C15.7196 16.7056 15.6123 16.75 15.5004 16.75Z"
        fill="#DEDFDF"
      />
    </svg>
  );
}
