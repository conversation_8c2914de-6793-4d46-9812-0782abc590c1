export default function CustomerPurpleIcon(props) {
  return (
    <svg
      width="61"
      height="60"
      viewBox="0 0 61 60"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g filter="url(#filter0_dd_9504_150957)">
        <rect x="6.80005" y="2" width="48" height="48" rx="24" fill="#6565EE" />
        <rect
          x="7.30005"
          y="2.5"
          width="47"
          height="47"
          rx="23.5"
          stroke="#E3E0E0"
        />
      </g>
      <path
        opacity="0.35"
        d="M38.4001 32C38.4001 33.657 37.0571 35 35.4001 35H26.4001C24.7431 35 23.4001 33.657 23.4001 32V20C23.4001 18.343 24.7431 17 26.4001 17H35.4001C37.0571 17 38.4001 18.343 38.4001 20V32Z"
        fill="white"
      />
      <path
        d="M32.4001 32V31C32.4001 29.895 31.5051 29 30.4001 29H22.4001C21.2951 29 20.4001 29.895 20.4001 31V32C20.4001 33.657 21.7431 35 23.4001 35H35.4001C33.7431 35 32.4001 33.657 32.4001 32Z"
        fill="white"
      />
      <path
        d="M34.4001 23H31.4001C30.8471 23 30.4001 22.552 30.4001 22C30.4001 21.448 30.8471 21 31.4001 21H34.4001C34.9531 21 35.4001 21.448 35.4001 22C35.4001 22.552 34.9531 23 34.4001 23Z"
        fill="white"
      />
      <path
        d="M34.4001 27H31.4001C30.8471 27 30.4001 26.552 30.4001 26C30.4001 25.448 30.8471 25 31.4001 25H34.4001C34.9531 25 35.4001 25.448 35.4001 26C35.4001 26.552 34.9531 27 34.4001 27Z"
        fill="white"
      />
      <path
        d="M27.4001 23C27.9524 23 28.4001 22.5523 28.4001 22C28.4001 21.4477 27.9524 21 27.4001 21C26.8479 21 26.4001 21.4477 26.4001 22C26.4001 22.5523 26.8479 23 27.4001 23Z"
        fill="white"
      />
      <path
        d="M27.4001 27C27.9524 27 28.4001 26.5523 28.4001 26C28.4001 25.4477 27.9524 25 27.4001 25C26.8479 25 26.4001 25.4477 26.4001 26C26.4001 26.5523 26.8479 27 27.4001 27Z"
        fill="white"
      />
      <defs>
        <filter
          id="filter0_dd_9504_150957"
          x="0.800049"
          y="0"
          width="60"
          height="60"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology
            radius="2"
            operator="erode"
            in="SourceAlpha"
            result="effect1_dropShadow_9504_150957"
          />
          <feOffset dy="2" />
          <feGaussianBlur stdDeviation="2" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_9504_150957"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology
            radius="2"
            operator="erode"
            in="SourceAlpha"
            result="effect2_dropShadow_9504_150957"
          />
          <feOffset dy="4" />
          <feGaussianBlur stdDeviation="4" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"
          />
          <feBlend
            mode="normal"
            in2="effect1_dropShadow_9504_150957"
            result="effect2_dropShadow_9504_150957"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect2_dropShadow_9504_150957"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  );
}
