export default function WalletGreenIcon(props) {
  return (
    <svg
      width="60"
      height="60"
      viewBox="0 0 60 60"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g filter="url(#filter0_dd_648_29716)">
        <rect x="6" y="2" width="48" height="48" rx="24" fill="#429634" />
        <rect
          x="6.5"
          y="2.5"
          width="47"
          height="47"
          rx="23.5"
          stroke="#E3E0E0"
        />
      </g>
      <path
        opacity="0.35"
        d="M36 35H24C22.343 35 21 33.657 21 32V20H36C37.657 20 39 21.343 39 23V32C39 33.657 37.657 35 36 35Z"
        fill="white"
      />
      <path
        d="M35.5 29C36.3284 29 37 28.3284 37 27.5C37 26.6716 36.3284 26 35.5 26C34.6716 26 34 26.6716 34 27.5C34 28.3284 34.6716 29 35.5 29Z"
        fill="white"
      />
      <path
        d="M21 20C21 18.343 22.343 17 24 17H33C34.657 17 36 18.343 36 20H21Z"
        fill="white"
      />
      <defs>
        <filter
          id="filter0_dd_648_29716"
          x="0"
          y="0"
          width="60"
          height="60"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology
            radius="2"
            operator="erode"
            in="SourceAlpha"
            result="effect1_dropShadow_648_29716"
          />
          <feOffset dy="2" />
          <feGaussianBlur stdDeviation="2" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_648_29716"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology
            radius="2"
            operator="erode"
            in="SourceAlpha"
            result="effect2_dropShadow_648_29716"
          />
          <feOffset dy="4" />
          <feGaussianBlur stdDeviation="4" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"
          />
          <feBlend
            mode="normal"
            in2="effect1_dropShadow_648_29716"
            result="effect2_dropShadow_648_29716"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect2_dropShadow_648_29716"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  );
}
