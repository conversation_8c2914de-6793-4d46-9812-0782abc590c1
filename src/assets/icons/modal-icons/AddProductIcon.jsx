export default function AddProductIcon(props) {
  return (
    <svg
      width="48"
      height="48"
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <rect x="0.5" y="0.5" width="47" height="47" rx="11.5" fill="white" />
      <rect x="0.5" y="0.5" width="47" height="47" rx="11.5" stroke="#E4E7EC" />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M13.9765 18.8755C13.9766 18.3407 14.3601 17.6843 14.8345 17.4082L23.1291 12.5817C23.6029 12.3059 24.3703 12.3059 24.845 12.5826L33.1424 17.4184C33.6164 17.6945 34.0003 18.3511 34.0002 18.8882L33.9967 28.5275C33.9966 29.0631 33.6125 29.7221 33.1394 29.9993L24.827 34.8678C24.3537 35.1451 23.5873 35.145 23.1132 34.8666L14.8299 30.0005C14.3567 29.7226 13.9734 29.0641 13.9736 28.5291L13.9765 18.8755ZM17.9079 16.9189C16.9704 17.4216 16.9684 18.7652 17.9043 19.2707L23.317 22.194C23.7191 22.4112 24.2043 22.4077 24.6032 22.1847L29.7519 19.3061C30.66 18.7984 30.6644 17.4933 29.7598 16.9795L24.5904 14.0433C24.1914 13.8167 23.7041 13.8108 23.2998 14.0276L17.9079 16.9189ZM15.1896 19.5322C15.1896 19.7293 15.0563 28.2302 15.0563 28.2339C15.0563 28.234 15.0563 28.2339 15.0562 28.2341C15.0521 28.5012 15.24 28.8293 15.4778 28.9682L21.1998 32.3084C22.0861 32.8258 23.2005 32.1916 23.2082 31.1653L23.2559 24.8659C23.2596 24.3897 23.0129 23.9497 22.6023 23.7086C20.6753 22.5769 15.1896 19.3695 15.1896 19.5322ZM24.7172 31.0636C24.7172 32.1077 25.8614 32.7478 26.7512 32.2016L32.3426 28.7689C32.6345 28.5897 32.8713 28.1737 32.8713 27.839V21.7568C32.8713 20.7379 31.7761 20.0944 30.8861 20.5903L25.4027 23.645C24.9795 23.8808 24.7172 24.3272 24.7172 24.8115V31.0636Z"
        fill="#FF6500"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M17.7151 24.2261C17.1165 24.5606 16.3228 24.3151 15.9425 23.678C15.5623 23.041 15.7394 22.2534 16.3381 21.919C16.9368 21.5846 17.7305 21.83 18.1107 22.4671C18.491 23.1042 18.3139 23.8918 17.7151 24.2261ZM21.7211 26.769C21.1225 27.1035 20.3288 26.8581 19.9485 26.221C19.5683 25.5839 19.7454 24.7963 20.344 24.462C20.9428 24.1275 21.7365 24.373 22.1167 25.0101C22.497 25.6471 22.3199 26.4347 21.7211 26.769ZM17.7151 28.7065C17.1165 29.041 16.3228 28.7955 15.9425 28.1585C15.5623 27.5214 15.7394 26.7338 16.3381 26.3995C16.9368 26.065 17.7305 26.3105 18.1107 26.9475C18.491 27.5846 18.3139 28.3722 17.7151 28.7065ZM21.7211 31.2495C21.1225 31.5839 20.3288 31.3385 19.9485 30.7014C19.5683 30.0643 19.7454 29.2768 20.344 28.9424C20.9428 28.608 21.7365 28.8534 22.1167 29.4905C22.497 30.1276 22.3199 30.9151 21.7211 31.2495ZM31.5155 21.7979C30.9167 21.4635 30.1232 21.7089 29.7429 22.346C29.3627 22.9831 29.5397 23.7707 30.1384 24.105C30.7372 24.4395 31.5309 24.194 31.9111 23.5569C32.2914 22.9199 32.1143 22.1323 31.5155 21.7979ZM29.4827 25.3096C28.8841 24.9752 28.0904 25.2206 27.7101 25.8577C27.3299 26.4948 27.507 27.2824 28.1057 27.6167C28.7044 27.9512 29.4981 27.7057 29.8783 27.0686C30.2586 26.4316 30.0815 25.644 29.4827 25.3096ZM27.3545 28.7002C26.7559 28.3658 25.9622 28.6112 25.5819 29.2483C25.2017 29.8854 25.3788 30.673 25.9775 31.0073C26.5762 31.3418 27.3699 31.0963 27.7501 30.4592C28.1304 29.8222 27.9533 29.0346 27.3545 28.7002ZM24.0671 16.5625C23.272 16.5625 22.6275 17.1589 22.6275 17.8945C22.6275 18.6302 23.272 19.2265 24.0671 19.2265C24.8622 19.2265 25.5068 18.6302 25.5068 17.8945C25.5068 17.1589 24.8622 16.5625 24.0671 16.5625Z"
        fill="#FF6500"
      />
    </svg>
  );
}
