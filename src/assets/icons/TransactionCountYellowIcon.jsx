export default function TransactionCountYellowIcon(props) {
  return (
    <svg
      width="60"
      height="60"
      viewBox="0 0 60 60"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g filter="url(#filter0_dd_186_9759)">
        <rect x="6" y="2" width="48" height="48" rx="24" fill="#E59422" />
      </g>
      <path
        opacity="0.35"
        d="M34.5 26C36.9853 26 39 23.9853 39 21.5C39 19.0147 36.9853 17 34.5 17C32.0147 17 30 19.0147 30 21.5C30 23.9853 32.0147 26 34.5 26Z"
        fill="white"
      />
      <path
        opacity="0.87"
        d="M20 32.93V27.129C20 26.447 20.469 25.854 21.133 25.696L23.438 25.151C24.616 24.872 25.853 24.981 26.965 25.46L31.344 27.348C32.733 27.947 33.359 29.571 32.73 30.947L33.103 31.436L37.756 29.775C38.639 29.447 39.618 29.916 39.916 30.809C40.178 31.595 39.822 32.454 39.081 32.825L34.458 35.153C33.537 35.613 32.471 35.689 31.494 35.364L25.354 33.317C24.479 33.025 23.534 33.023 22.657 33.309L21.185 33.79C20.601 33.981 20 33.545 20 32.93Z"
        fill="white"
      />
      <path
        d="M36 16C34.864 16 33.843 16.479 33.115 17.24C33.554 17.097 34.014 17 34.5 17C36.985 17 39 19.015 39 21.5C39 21.986 38.903 22.446 38.76 22.885C39.521 22.156 40 21.136 40 20C40 17.791 38.209 16 36 16Z"
        fill="white"
      />
      <defs>
        <filter
          id="filter0_dd_186_9759"
          x="0"
          y="0"
          width="60"
          height="60"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology
            radius="2"
            operator="erode"
            in="SourceAlpha"
            result="effect1_dropShadow_186_9759"
          />
          <feOffset dy="2" />
          <feGaussianBlur stdDeviation="2" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_186_9759"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology
            radius="2"
            operator="erode"
            in="SourceAlpha"
            result="effect2_dropShadow_186_9759"
          />
          <feOffset dy="4" />
          <feGaussianBlur stdDeviation="4" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"
          />
          <feBlend
            mode="normal"
            in2="effect1_dropShadow_186_9759"
            result="effect2_dropShadow_186_9759"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect2_dropShadow_186_9759"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  );
}
