import { Route, Routes } from "react-router-dom";
import "./App.css";
import { useAuth } from "./context/global.context";
import ScrollToTop from "./lib/ScrollToTop";
import AuthenticationLayout from "./layouts/AuthenticationLayout";
import LoginPage from "./screens/auth-screens/LoginPage";

import DashboardLayout from "@/layouts/DashboardLayout";
import Dashboard from "@/screens/admin-screens/Dashboard";
import Playground from "@/screens/Playground";
import PayoutPartnerPage from "@/screens/admin-screens/PayoutPartnerPage";
import PayoutDashboard from "@/screens/admin-screens/PayoutDashboard";
import AgentsInvitePage from "@/screens/admin-screens/AgentsInvitePage";
import AgentsPage from "@/screens/admin-screens/AgentsPage";
import BeneficiariesPage from "@/screens/admin-screens/BeneficiariesPage";
import AgentDetailsPage from "@/screens/admin-screens/sub-screens/AgentDetailsPage";
import CustomersPage from "@/screens/admin-screens/CustomersPage";
import CustomerDetailsPage from "@/screens/admin-screens/sub-screens/CustomerDetailsPage";
import SendMoneyPage from "@/screens/admin-screens/SendMoneyPage";
import CurrencyRateMetadataPage from "@/screens/admin-screens/CurrencyRateMetadataPage";
import ViewTransfersPage from "@/screens/admin-screens/ViewTransfersPage";
import CreateCurrencyRateMetadataPage from "@/screens/admin-screens/sub-screens/CreateCurrencyRateMetadataPage";
import UpdateCurrencyRateMetadataPage from "@/screens/admin-screens/sub-screens/UpdateCurrencyRateMetadataPage";
import UpdateRatesAndFeesPage from "@/screens/admin-screens/UpdateRatesAndFeesPage";
import PaymentChannelProcessorPage from "@/screens/admin-screens/PaymentChannelProcessorPage";
import PayoutChannelProcessorPage from "@/screens/admin-screens/PayoutChannelProcessorPage";
import BanksPage from "@/screens/admin-screens/BanksPage";
import CompanyBanksPage from "@/screens/admin-screens/CompanyBanksPage";
import CountryPage from "@/screens/admin-screens/CountryPage";
import UserRolesPage from "@/screens/admin-screens/UserRolesPage";
import AddCustomerPage from "@/screens/admin-screens/sub-screens/AddCustomerPage";
import InitiateSendMoneyPage from "@/screens/admin-screens/sub-screens/InitiateSendMoneyPage";
import TransactionDetailsPage from "@/screens/admin-screens/sub-screens/TransactionDetailsPage";
import ActionCustomersPage from "@/screens/admin-screens/ActionCustomersPage";
import IncompleteCustomersPage from "@/screens/admin-screens/IncompleteCustomersPage";
import IncompleteTransferPage from "@/screens/admin-screens/IncompleteTransfersPage";
import IncompletePWBPage from "@/screens/admin-screens/IncompletePayWithBankTransfersPage";
import PyamentCheckPage from "@/screens/admin-screens/PyamentCheckPage";
import KycProvidersPage from "@/screens/admin-screens/KycProvidersPage";
import PayoutProvidersPage from "@/screens/admin-screens/PayoutProvidersPage";
import ClientPayoutPage from "@/screens/admin-screens/ClientPayoutPage";
import ClientDetailsPage from "@/screens/admin-screens/sub-screens/ClientDetailsPage";
import EditDocumentPage from "@/screens/admin-screens/sub-screens/EditDocument";
import AddDocumentPage from "@/screens/admin-screens/sub-screens/AddDocumentPage";
import UpdateRatePage from "@/screens/admin-screens/sub-screens/UpdateRatePage";
import PaymentChannelPage from "@/screens/admin-screens/PaymentChannelPage";
import PayoutChannelPage from "@/screens/admin-screens/PayoutChannelPage";
import ProfessionMasterPage from "@/screens/admin-screens/ProfessionMasterPage";
import PaymentProvidersPage from "@/screens/admin-screens/PaymentProvidersPage";
import EmployeeMasterPage from "@/screens/admin-screens/EmployeeMasterPage";
import UserAccessPage from "@/screens/admin-screens/UserAccessPage";
import AddEmployeePage from "@/screens/admin-screens/sub-screens/AddEmployeePage";
import EditEmployeePage from "@/screens/admin-screens/sub-screens/EditEmployeePage";
import CityPage from "@/screens/admin-screens/CityPage";
import NotFound from "@/screens/NotFound";
import CreatePasswordPage from "./screens/auth-screens/CreatePasswordPage";
import ConfirmTransactionPage from "./screens/admin-screens/ConfirmTransactionPage";
import { useEffect } from "react";
import OneSignal from "react-onesignal";
import EditCustomerPage from "./screens/admin-screens/sub-screens/EditCustomerPage";
import InfrastructureManagementPage from "./screens/admin-screens/InfrastructureManagementPage";
function App() {
  useEffect(() => {
    OneSignal.init({
      appId: "************************************",
      notifyButton: {
        enable: true,
      },
      allowLocalhostAsSecureOrigin: true,
    });
  }, []);
  const { isAuthenticated, menuAccessRoutes } = useAuth();
  const check =
    menuAccessRoutes?.find((itm) => itm.menuName === "Update Rate & Fees")
      ?.menuAccessType?.id > 1
      ? true
      : false;
  return (
    <>
      <ScrollToTop />
      <Routes>
        {isAuthenticated && (
          <Route element={<DashboardLayout />}>
            <Route element={<PayoutDashboard />} path="/payout-dashboard" />
            <Route element={<Dashboard />} path="/" />
            <Route element={<Dashboard />} path="/dashboard" />
            <Route element={<PayoutPartnerPage />} path="/payout-partner" />
            <Route element={<CustomersPage />} path="/customers" />
            <Route element={<EditDocumentPage />} path="/edit-document/:id" />
            <Route element={<AddDocumentPage />} path="/add-document" />
            <Route element={<ActionCustomersPage />} path="/action-required" />
            <Route
              element={<IncompleteCustomersPage />}
              path="/incomplete-registration"
            />
            <Route element={<AddCustomerPage />} path="/add-customer" />
            <Route element={<EditCustomerPage />} path="/edit-user" />
            <Route element={<CustomerDetailsPage />} path="/customers/:id" />
            <Route element={<AgentsPage />} path="/agents" />
            <Route element={<AgentDetailsPage />} path="/agents/:id" />
            <Route element={<AgentsInvitePage />} path="/pending-invitations" />
            <Route element={<BeneficiariesPage />} path="/beneficiary" />
            <Route element={<SendMoneyPage />} path="/send-money" />
            <Route element={<InitiateSendMoneyPage />} path="/send-money/:id" />
            <Route
              element={<CurrencyRateMetadataPage />}
              path="/currency-rate-metadata"
            />
            <Route
              element={<CreateCurrencyRateMetadataPage />}
              path="/create-currency-rate-metadata"
            />
            <Route
              element={<UpdateCurrencyRateMetadataPage />}
              path="/update-currency-rate-metadata"
            />
            {check ? (
              <Route
                element={<UpdateRatesAndFeesPage />}
                path="/update-rate-&-fees"
              />
            ) : null}
            <Route element={<UpdateRatePage />} path="/update-rate/:id" />
            <Route element={<ViewTransfersPage />} path="/view-transfers" />
            <Route
              element={<IncompleteTransferPage />}
              path="/incomplete-transfers"
            />
            <Route
              element={<IncompletePWBPage />}
              path="/incomplete-pay-with-bank-transfers"
            />
            <Route element={<PyamentCheckPage />} path="/payment-check" />
            <Route element={<TransactionDetailsPage />} path="/transfer/:id" />
            <Route
              element={<PaymentChannelProcessorPage />}
              path="/payment-channel-processor"
            />
            <Route
              element={<PayoutChannelProcessorPage />}
              path="/payout-channel-processor"
            />
            <Route element={<BanksPage />} path="/banks" />
            <Route element={<CompanyBanksPage />} path="/company-banks" />
            <Route element={<CountryPage />} path="/country" />
            <Route element={<CityPage />} path="/countries/:id" />
            <Route element={<UserRolesPage />} path="/user-role" />
            <Route element={<KycProvidersPage />} path="/kyc-provider" />
            <Route element={<PayoutProvidersPage />} path="/payout-provider" />
            <Route
              element={<PaymentProvidersPage />}
              path="/payment-provider"
            />
            <Route element={<ClientPayoutPage />} path="/payout-clients" />
            <Route element={<ClientDetailsPage />} path="/client/:id" />
            <Route element={<PaymentChannelPage />} path="/payment-channels" />
            <Route element={<PayoutChannelPage />} path="/payout-channels" />
            <Route
              element={<ProfessionMasterPage />}
              path="/profession-master"
            />
            <Route element={<UserAccessPage />} path="/user-access" />
            <Route element={<EmployeeMasterPage />} path="/employee-master" />
            <Route element={<AddEmployeePage />} path="/add-employee" />
            <Route element={<EditEmployeePage />} path="/edit-employee" />
            <Route
              element={<InfrastructureManagementPage />}
              path="/system-monitor"
            />

            <Route element={<NotFound />} path="*" />
            <Route element={<Playground />} path="/playground" />
          </Route>
        )}
        <Route
          element={<ConfirmTransactionPage />}
          path="/confirm-transaction"
        />
        {!isAuthenticated && (
          <>
            <Route
              element={
                <AuthenticationLayout
                  title="BCON Admin Login"
                  desc="Welcome back! Please enter your details."
                />
              }
            >
              <Route element={<LoginPage />} path="/" />
            </Route>
            <Route
              element={
                <AuthenticationLayout
                  title="BCON Reset Password"
                  desc="Please enter required details."
                />
              }
            >
              <Route element={<CreatePasswordPage />} path="/forgot-password" />
            </Route>
          </>
        )}
        <Route
          element={
            <AuthenticationLayout title="Not Found" desc="Page not found!" />
          }
        >
          <Route element={<NotFound />} path="*"></Route>
        </Route>
      </Routes>
    </>
  );
}

export default App;
