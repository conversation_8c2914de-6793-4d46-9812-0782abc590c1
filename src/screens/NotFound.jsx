/* eslint-disable react-hooks/exhaustive-deps */
import AppButton from "@/components/bits/AppButton";
import SectionHeader from "@/components/bits/SectionHeader";
import { useAuth } from "@/context/global.context";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import styled from "styled-components";

export default function NotFound() {
  const navigate = useNavigate();
  const { user_data } = useAuth();
  const filterAccess = user_data?.userRoleMenuAccess?.filter(
    (itm) => itm?.menuAccessType?.id >= 2
  );
  const filterAccessSub = filterAccess?.[0]?.userRoleSubMenuAccess?.filter(
    (itm) => itm?.menuAccessType?.id >= 2
  );
  //
  const hasSubMenus = filterAccessSub?.length;
  //
  const pickHas = filterAccessSub?.[0]?.subMenuName;
  const pickMain = filterAccess?.[0]?.menuName;
  //
  const firstRoute = hasSubMenus
    ? pickHas?.replace(/ /gu, "-")?.toLowerCase()
    : pickMain?.replace(/ /gu, "-")?.toLowerCase();

  useEffect(() => {
    if (user_data) {
      return;
    } else {
      navigate("/");
    }
  }, []);
  return (
    <DashboardStyle>
      <SectionHeader
        title={"Page Not Found"}
        description="Are you lost? The page your looking for doesn't exist"
      />

      <AppButton
        placeholder="Go Back"
        width="fit-content"
        outline
        loading={false}
        disabled={false}
        onClick={() => {
          navigate(firstRoute ? firstRoute : "/");
        }}
      />
    </DashboardStyle>
  );
}

const DashboardStyle = styled.div`
  color: #000;
`;
