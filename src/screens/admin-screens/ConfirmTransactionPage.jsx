import { useState } from "react";
import { styled } from "styled-components";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useLocation } from "react-router-dom";
import moment from "moment";
import { ApiServiceAdmin } from "@/service/admin-services";
import AppButton from "@/components/bits/AppButton";
import { FormatCurrency } from "@/lib/utils";
import Modal from "@/components/bits/Modal";
import { StatusBadge } from "@/components/bits/StatusBadge";
import { Notification, Skeleton } from "@arco-design/web-react";
import { Copy } from "lucide-react";
import BoxList from "@/components/BoxList";

function ConfirmTransactionPage() {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);

  const id = queryParams.get("tid") || queryParams.get("tfId");
  const statusMessage = queryParams.get("statusMessage");
  const statusCode = queryParams.get("statusCode");

  const { data, isLoading } = useQuery({
    queryKey: ["GetTransferListDetailsQuery"],
    queryFn: () => ApiServiceAdmin.GetTransferListDetailsQuery(id),
  });

  const {
    mutate,
    isPending,
    data: paymentData,
  } = useMutation({
    mutationFn: ApiServiceAdmin.SubmitTransferMutation,
    onSuccess: () => {
      setShowModal2(true);
    },
    onError: () => {
      return;
    },
  });

  const transactionData = data?.data;
  console.log(transactionData, "transactionData");
  const [showModal, setShowModal] = useState(false);
  const [showModal2, setShowModal2] = useState(false);

  /* Offline Bank */

  /* 
  Bank
  Account Name
     Account Number
       Sort Code
  
  */

  if (isLoading) {
    return (
      <>
        <Skeleton />
        <Skeleton />
        <Skeleton />
      </>
    );
  }

  return (
    <Content>
      {/*  */}
      <Modal
        open={statusCode}
        title={statusCode === 0 ? "Payment Successful" : "Payment Failed"}
        dialogBtns={false}
      >
        <p>{statusMessage}</p>
        <br />

        <AppButton
          placeholder={"Close"}
          onClick={() => {
            window.location.pathname = "/";
          }}
        />
      </Modal>
      <Modal
        open={showModal2}
        title={"Proceed to Make Payment"}
        trigger={() => {
          setShowModal2(false);
        }}
        submit={() => {
          window.location.replace(paymentData?.data?.response);
        }}
      >
        <p>{paymentData?.message}</p>
      </Modal>
      <Modal
        open={showModal}
        trigger={() => {
          setShowModal(false);
        }}
        dialogBtns={false}
        title={"Make Payment"}
      >
        <BoxList
          list={[
            {
              title: "Bank Name",
              value: transactionData?.systemOfflinePaymentBank?.bankName,
            },
            {
              title: "Account Name",
              value: transactionData?.systemOfflinePaymentBank?.accountName,
            },
            {
              title: "Account Number",
              value: (
                <p
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: 4,
                  }}
                >
                  {transactionData?.systemOfflinePaymentBank?.accountNumber}
                  <Copy
                    size={"12"}
                    color="#000"
                    onClick={() => {
                      navigator.clipboard.writeText(
                        transactionData?.systemOfflinePaymentBank?.accountNumber
                      );
                      Notification.success({
                        content: "Copied Account Number",
                      });
                    }}
                  />
                </p>
              ),
            },
            {
              title: "Sort Code",
              value: (
                <p
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: 4,
                  }}
                >
                  {transactionData?.systemOfflinePaymentBank?.sortCode}
                  <Copy
                    size={"12"}
                    color="#000"
                    onClick={() => {
                      navigator.clipboard.writeText(
                        transactionData?.systemOfflinePaymentBank?.sortCode
                      );
                      Notification.success({
                        content: "Copied Sort Code",
                      });
                    }}
                  />
                </p>
              ),
            },
          ]}
        />
        <br />
        <AppButton
          placeholder={"Close"}
          onClick={() => {
            window.location.pathname = "/";
          }}
        />
      </Modal>
      {/*  */}
      <div className="cont">
        <Header>
          <img src={"/logo.svg"} />

          <p>
            {moment(transactionData?.paymentDate).format(
              "DD MMM YYYY: hh:mm a"
            )}
          </p>

          <p>
            <StatusBadge
              status={transactionData?.paymentStatus}
              variant="other"
            />
          </p>
          <small>
            SN: <b>{transactionData?.sn}</b>
          </small>
        </Header>

        <Details>
          <h3 className="detailsinfo">Personal Details</h3>
          <div className="detailscont">
            <div className="details">
              <h5>Sending Amount</h5>
              <p>
                {FormatCurrency(
                  transactionData?.paymentAmount,
                  transactionData?.senderCurrency
                )}
              </p>
            </div>
            <div className="details">
              <h5>Transition Fee</h5>
              <p>
                {FormatCurrency(
                  transactionData?.transitionFee,
                  transactionData?.beneficiaryCurrency
                )}
              </p>
            </div>
            <div className="details">
              <h5>Received Amount</h5>
              <p>
                {FormatCurrency(
                  transactionData?.receivedAmount,
                  transactionData?.beneficiaryCurrency
                )}
              </p>
            </div>
            <div className="details">
              <h5>Sender Name</h5>
              <p>{transactionData?.senderName}</p>
            </div>
            <div className="details">
              <h5>Sender Address</h5>
              <p>{transactionData?.senderAddress}</p>
            </div>
            <div className="details">
              <h5>Mobile number</h5>
              <p>{transactionData?.userBeneficiary?.beneficiaryPhoneNumber}</p>
            </div>

            <div className="details">
              <h5>Collection Type</h5>
              <p>{transactionData?.collectionType}</p>
            </div>
            <div className="details">
              <h5>Payment Type</h5>
              <p>{transactionData?.paymentType}</p>
            </div>
            <div className="details">
              <h5>Payment Ref.</h5>
              <p
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: 4,
                }}
              >
                {transactionData?.paymentRef}
                <span>
                  <Copy
                    size={"12"}
                    color="#000"
                    onClick={() => {
                      navigator.clipboard.writeText(
                        transactionData?.paymentRef
                      );
                      Notification.success({
                        content: "Copied to clipboard",
                      });
                    }}
                  />
                </span>
              </p>
            </div>
            <div className="details">
              <h5>Transaction Source</h5>
              <p>{transactionData?.transactionSource}</p>
            </div>
          </div>

          <h3 className="detailsinfo">Bank Details</h3>
          <div className="detailscont">
            <div className="details">
              <h5>Beneficiary Name</h5>
              <p>
                {transactionData?.userBeneficiary?.beneficiaryBank?.accountName}
              </p>
            </div>
            <div className="details">
              <h5>Beneficiary Country</h5>
              <p>
                {transactionData?.userBeneficiary?.beneficiaryCountry?.name}
              </p>
            </div>
            <div className="details">
              <h5>Beneficiary Bank</h5>
              <p>
                {transactionData?.userBeneficiary?.beneficiaryBank?.bankName}
              </p>
            </div>

            <div className="details">
              <h5>Beneficiary Account Number</h5>
              <p>
                {
                  transactionData?.userBeneficiary?.beneficiaryBank
                    ?.accountNumber
                }
              </p>
            </div>
          </div>

          {transactionData?.paymentStatus === "Pending" ? (
            transactionData?.systemOfflinePaymentBank ? (
              ""
            ) : (
              <AppButton
                loading={isPending}
                disabled={isPending}
                onClick={() => {
                  mutate(id);
                }}
                placeholder={"Submit"}
              ></AppButton>
            )
          ) : (
            ""
          )}

          {transactionData?.paymentStatus === "Pending"
            ? transactionData?.systemOfflinePaymentBank && (
                <AppButton
                  onClick={() => {
                    setShowModal(true);
                  }}
                  placeholder={"Proceed to make Payment"}
                ></AppButton>
              )
            : ""}
        </Details>
      </div>
    </Content>
  );
}

const Content = styled.div`
  width: 100%;
  /* background-color: #fff; */
  padding: 30px;
  margin: 0 auto;

  @media screen and (max-width: 40em) {
    width: 100%;
  }

  .cont {
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 10px;
    /* width: 100%;
        border: 1px solid green;
         margin: 0 auto; */
  }
`;

const Header = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  /* padding-block: 10px; */
  gap: 10px;

  .av {
    background: rgba(0, 168, 90, 1);
    padding: 10px;
  }

  p {
    font-weight: 500;
    font-size: 18px;
  }
`;

const Details = styled.div`
  height: 85%;

  display: flex;
  flex-direction: column;
  gap: 10px;

  .actionbtn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    gap: 10px;

    button {
      padding: 15px 35px;
      border: 0.1px solid rgba(90, 99, 118, 1);
      border-radius: 4px;

      @media screen and (max-width: 40em) {
        margin-bottom: -10px;
      }
    }
    button:nth-of-type(2) {
      background: rgba(0, 168, 90, 1);
    }
    button:nth-of-type(1) {
      background: #fff;
      color: rgba(90, 99, 118, 1);
    }
  }

  .detailscont {
    background-color: #fff;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 1em;

    .detailsinfo {
      color: rgba(51, 59, 74, 1);
    }

    .details {
      padding: 10px 10px;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid rgba(233, 237, 245, 1);

      &:last-child {
        border-bottom: none;
      }

      h5 {
        color: rgba(102, 112, 133, 1);
        font-weight: 400;
        font-size: 13px;
      }
      p {
        font-weight: 450;
        font-size: 13px;
      }
    }
  }
`;

export default ConfirmTransactionPage;
