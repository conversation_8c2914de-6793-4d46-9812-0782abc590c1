import CustomTable from "@/components/bits/CustomTable";
//import MiniNavBar from "@/components/bits/MiniNavBar";
import SectionHeader from "@/components/bits/SectionHeader";
import { useAuth } from "@/context/global.context";
//import { handleGetItm } from "@/lib/utils";
import { ApiServiceAdmin } from "@/service/admin-services";
//import { handleSetItem } from "@/lib/utils";
import { Flex } from "@radix-ui/themes";
import { useQuery } from "@tanstack/react-query";
//import { useNavigate } from "react-router-dom";
import styled from "styled-components";

export default function BanksPage() {
  const { user_id } = useAuth();
  const { data } = useQuery({
    queryKey: ["GetBanksQuery"],
    queryFn: () =>
      ApiServiceAdmin.GetBanksQuery({
        role: user_id,
        rateMetaDataId: 0,
      }),
  });

  //const navigate = useNavigate();
  const columns = [
    {
      title: "bank name",
      dataIndex: "bankName",
    },
    {
      title: "bank Code",
      dataIndex: "bankCode",
    },
    {
      title: "date added",
      dataIndex: "dateAdded",
    },
  ];

  return (
    <DashboardStyle>
      <Flex
        align={{ initial: "flex-start", md: "center" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "flex-start", md: "between" }}
      >
        <SectionHeader
          title="Banks"
          description={`This page allows you to manage all banks`}
        />
      </Flex>
      &nbsp;
      <CustomTable tableColumns={columns} arrayData={data?.data} />
    </DashboardStyle>
  );
}

const DashboardStyle = styled.div`
  color: #000;
`;
