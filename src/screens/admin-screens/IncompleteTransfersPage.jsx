//import MiniNavBar from "@/components/bits/MiniNavBar";
import SectionHeader from "@/components/bits/SectionHeader";
import TransactionTableList from "@/components/dashboard-tables/TransactionTableList";
import { useAuth } from "@/context/global.context";
import { ApiServiceAdmin } from "@/service/admin-services";
//import { handleSetItem } from "@/lib/utils";
import { Flex } from "@radix-ui/themes";
import { useQuery } from "@tanstack/react-query";

import styled from "styled-components";

export default function IncompleteTransferPage() {
  const { user_id } = useAuth();

  const { data, isLoading, refetch } = useQuery({
    queryKey: ["GetTransferLsasfkkkffistQuery"],
    queryFn: () =>
      ApiServiceAdmin.GetOtherTransferQuery({
        id: user_id,
        body: {
          categoryId: 1,
        },
      }),
  });
  console.log(data, "transfers");

  const transfers = data?.data;
  return (
    <DashboardStyle>
      <Flex
        align={{ initial: "flex-start", md: "center" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "flex-start", md: "between" }}
      >
        <SectionHeader
          title="Incomplete Transfers
"
          description={`This page shows you all transfers in the system.`}
        />
      </Flex>
      &nbsp;
      <TransactionTableList
        recall={refetch}
        data={transfers}
        isLoading={isLoading}
      />
    </DashboardStyle>
  );
}

const DashboardStyle = styled.div`
  color: #000;
`;
