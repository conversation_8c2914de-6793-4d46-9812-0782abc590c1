/* eslint-disable react-hooks/exhaustive-deps */
import CancelledIcon from "@/assets/icons/CancelledIcon";
import CustomerPurpleIcon from "@/assets/icons/CustomerPurpleIcon";
import TransactionCountYellowIcon from "@/assets/icons/TransactionCountYellowIcon";
import WalletGreenIcon from "@/assets/icons/WalletGreenIcon";
import Box from "@/components/bits/Box";
import CurrencySelect from "@/components/bits/CurrencySelect";
import MainDashboardAnalysisCard from "@/components/bits/MainDashboardAnalysisCard";
import SectionHeader from "@/components/bits/SectionHeader";
import ClientListTable from "@/components/payout-dashboard-tables-chart/ClientListTable";
import ClientFundRequestLog from "@/components/payout-dashboard-tables-chart/ClientFundRequestLog";
import PayoutByGatewayChart from "@/components/payout-dashboard-tables-chart/PayoutByGatewayChart";
import PayoutTransactionLogs from "@/components/payout-dashboard-tables-chart/PayoutTransactionLogs";
import TransactionsTypeChart from "@/components/payout-dashboard-tables-chart/TransactionsTypeChart";
import { useAuth } from "@/context/global.context";
import { ApiServiceAdmin } from "@/service/admin-services";
import { Flex, Grid } from "@radix-ui/themes";
import { useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import styled from "styled-components";
import MainDashboardCard from "@/components/bits/MainDashboardCard";
import { useNavigate } from "react-router-dom";

export default function PayoutDashboard() {
  const { user_id } = useAuth();

  const { data, refetch } = useQuery({
    queryKey: ["GetPayoutClientDashboard"],
    queryFn: () => ApiServiceAdmin.GetPayoutClientDashboard(user_id),
  });

  const transactionTypeMonths =
    data?.data && Object.keys(data?.data?.analyticByTransactionStatus);

  const transactionTypeAnalytics = transactionTypeMonths?.map((itm) => {
    return {
      month: itm,
      ...data?.data?.analyticByTransactionStatus[itm],
    };
  });

  const gateways = data?.data && data?.data?.analyticByProvider;

  const currencyOptions =
    data &&
    data?.data?.transactionVolume?.map((itm) => {
      return {
        label: itm?.currency,
        value: itm?.currency,
        ...itm,
      };
    });

  const [currency, setCurrency] = useState();

  useEffect(() => {
    setCurrency(currencyOptions?.[0]);
  }, [data]);

  console.log(data);

  const route = useNavigate();
  return (
    <DashboardStyle>
      <Flex justify={"between"}>
        <SectionHeader
          title="Payout Dashboard"
          description="This overview provides a comprehensive snapshot of transactions on your system"
        />
        <CurrencySelect
          options={currencyOptions}
          setCurrency={setCurrency}
          currency={currency}
        />
      </Flex>
      <br />
      <Box>
        <Grid
          gap={"4"}
          columns={{ md: "1fr 1fr 1fr 1fr 1fr", initial: "2" }}
          width={"100%"}
        >
          <MainDashboardCard
            title="Today"
            amount={currency?.TodayValue}
            currency={currency?.value}
            count={currency?.todayCount}
            difference={currency?.thisWeekandLastWeekyPercentageDifference}
            timeType="yesterday"
            divider
          />
          <MainDashboardCard
            title="This Week"
            amount={currency?.thisWeekAmount}
            currency={currency?.value}
            count={currency?.thisWeekCount}
            difference={currency?.thisWeekandLastWeekyPercentageDifference}
            timeType="week"
            divider
          />
          <MainDashboardCard
            title="This Month"
            amount={currency?.thisMonthAmount}
            currency={currency?.value}
            count={currency?.thisMonthCount}
            difference={currency?.thisMonthAndLastMonthPercentageDifference}
            timeType="month"
            divider
          />
          <MainDashboardCard
            title="This Year"
            amount={currency?.thisYearAmount}
            currency={currency?.value}
            count={currency?.thisYearCount}
            difference={currency?.thisYearAndLastYearPercentageDifference}
            timeType="year"
            divider
          />
          <MainDashboardCard
            title="All Time"
            amount={currency?.totalAmount}
            currency={currency?.value}
            count={currency?.total}
          />
        </Grid>
      </Box>
      <Grid
        columns={{ initial: "1", md: "5fr 1fr" }}
        gap="4"
        mt="40px"
        mb="30px"
      >
        <Box>
          <Grid
            gap={"4"}
            columns={{ md: "2", lg: "4", initial: "2" }}
            width={"100%"}
          >
            <MainDashboardAnalysisCard
              Icon={WalletGreenIcon}
              title="Successful"
              amount={currency?.successfulAmount}
              count="40"
              divider
            />

            <MainDashboardAnalysisCard
              Icon={CustomerPurpleIcon}
              title="Initialized"
              amount={currency?.initializedAmount}
              count="40"
              divider
            />

            <MainDashboardAnalysisCard
              title="Pending"
              Icon={TransactionCountYellowIcon}
              amount={currency?.pendingAmount}
              count="40"
              divider
            />

            <MainDashboardAnalysisCard
              title="Cancelled"
              Icon={CancelledIcon}
              amount={currency?.failedAmount}
              count="40"
            />
          </Grid>
        </Box>

        <Grid gap={"4"}>
          <Box
            onClick={() => route("/payout-provider")}
            padding="12px"
            style={{
              display: "flex",
              alignItems: "center",
              height: "100%",
              justifyContent: "center",
              cursor: "pointer",
            }}
          >
            <Flex gap={"2"} align={"center"}>
              {" "}
              <img
                style={{
                  width: "20px",
                  height: "20px",
                }}
                src="/icons/Clock.png"
              />
              View Gateways
            </Flex>
          </Box>
          <Box
            onClick={() => {
              route("/payout-clients");
              localStorage.setItem(
                "payoutClients",
                JSON.stringify(data?.data?.allPayoutClients)
              );
            }}
            padding="12px"
            style={{
              display: "flex",
              alignItems: "center",
              height: "100%",
              justifyContent: "center",
            }}
          >
            <Flex gap={"2"} align={"center"}>
              {" "}
              <img
                style={{
                  width: "20px",
                  height: "20px",
                }}
                src="/icons/users.png"
              />
              Manage Clients
            </Flex>
          </Box>
        </Grid>
      </Grid>
      <br />
      <Grid columns={{ initial: "1", md: "2" }} gap="4">
        <PayoutByGatewayChart data={gateways} currency={currency?.label} />
        <TransactionsTypeChart
          data={transactionTypeAnalytics}
          currency={currency?.label}
        />
      </Grid>
      <br />
      <ClientFundRequestLog
        data={data?.data?.walletFundindRequests}
        refetch={refetch}
      />
      <br />
      <PayoutTransactionLogs data={data?.data?.payOutTransactions} />
      <br />
      <ClientListTable data={data?.data?.allPayoutClients} />
    </DashboardStyle>
  );
}

const DashboardStyle = styled.div`
  color: #000;
`;
