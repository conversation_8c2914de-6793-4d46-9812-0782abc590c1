import AppLink from "@/components/bits/AppLink";
import { CurrencyBadge } from "@/components/bits/CurrencyBadge";
import CustomTable from "@/components/bits/CustomTable";
import FormInput from "@/components/bits/FormInput";
//import MiniNavBar from "@/components/bits/MiniNavBar";
import SectionHeader from "@/components/bits/SectionHeader";
import { StatusBadge } from "@/components/bits/StatusBadge";
import { applyFilters } from "@/lib/utils";
//import { handleGetItm } from "@/lib/utils";
import { ApiServiceAdmin } from "@/service/admin-services";
//import { handleSetItem } from "@/lib/utils";
import { Flex } from "@radix-ui/themes";
import { useQuery } from "@tanstack/react-query";
import { Search } from "lucide-react";
import { useState } from "react";
//import { useNavigate } from "react-router-dom";
import styled from "styled-components";

export default function CountryPage() {
  const { data } = useQuery({
    queryKey: ["GetCountriesQuery"],
    queryFn: () => ApiServiceAdmin.GetCountriesQuery(),
  });

  //const navigate = useNavigate();
  const columns = [
    {
      title: "action",
      dataIndex: "id",
      render: (e, record) => (
        <AppLink to={`/countries/${e}?country=${record?.name}`}>
          View Cities
        </AppLink>
      ),
    },
    {
      title: "country id",
      dataIndex: "id",
    },
    {
      title: "region id",
      dataIndex: "regionId",
    },
    {
      title: "country name",
      dataIndex: "currencyCode",
      render: (e) => <CurrencyBadge currency={e} />,
    },
    {
      title: "sub region id",
      dataIndex: "subRegionId",
    },
    {
      title: "collection currency status",
      dataIndex: "isCollectionCurrency",
      render: (e) => <StatusBadge status={e ? "true" : "false"} />,
    },
    {
      title: "country telephone code",
      dataIndex: "telephoneCode",
    },
    {
      title: "country status",
      dataIndex: "status",
      render: (e) => <StatusBadge status={e ? "true" : "false"} />,
    },

    {
      title: "longitude",
      dataIndex: "longitude",
    },
    {
      title: "latitude",
      dataIndex: "latitude",
    },
  ];

  const [search, setSearch] = useState("");

  const newData = applyFilters(data?.data, search, [
    "id",
    "name",
    "regionId",
    "currencyCode",
    "subRegionId",
    "isCollectionCurrency",
    "telephoneCode",
    "status",
  ]);

  return (
    <DashboardStyle>
      <Flex
        align={{ initial: "flex-start", md: "center" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "flex-start", md: "between" }}
      >
        <SectionHeader
          title="Countries"
          description={`This page allows you to manage all countries`}
        />
      </Flex>
      &nbsp;
      <CustomTable
        tableColumns={columns}
        arrayData={newData}
        topContent={
          <Flex justify={"between"} align={"end"}>
            <FormInput
              placeholder={"Search by country name"}
              background="#fff"
              IconLeft={Search}
              width="400px"
              onChange={(e) => {
                setSearch(e?.target?.value?.trim());
              }}
            />
          </Flex>
        }
        scroll={{
          x: 1500,
        }}
      />
    </DashboardStyle>
  );
}

const DashboardStyle = styled.div`
  color: #000;
`;
