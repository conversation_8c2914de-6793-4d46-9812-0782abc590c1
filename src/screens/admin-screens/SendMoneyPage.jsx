import AppLink from "@/components/bits/AppLink";
import CustomTable from "@/components/bits/CustomTable";
import FormInput from "@/components/bits/FormInput";
//import MiniNavBar from "@/components/bits/MiniNavBar";
import SectionHeader from "@/components/bits/SectionHeader";
import { StatusBadge } from "@/components/bits/StatusBadge";
import { useAuth } from "@/context/global.context";
import { applyFilters, handleGetItm } from "@/lib/utils";
import InviteAgentModal from "@/Modals/InviteAgentModal";
import { ApiServiceAdmin } from "@/service/admin-services";
//import { handleSetItem } from "@/lib/utils";
import { Flex } from "@radix-ui/themes";
import { useQuery } from "@tanstack/react-query";
import { Search } from "lucide-react";
import moment from "moment";
import { useState } from "react";
import styled from "styled-components";

export default function SendMoneyPage() {
  const { user_id } = useAuth();
  const { data, isLoading } = useQuery({
    queryKey: ["GetCustomersQuery"],
    queryFn: () =>
      user_id
        ? ApiServiceAdmin.GetAgentCustomersQuery(user_id)
        : ApiServiceAdmin.GetCustomersQuery(),
  });
  console.log(data, "GetCustomersQuery");

  const columns = [
    {
      title: "Send Money",
      dataIndex: "userId",
      fixed: "left",
      width: 150,
      render: (e) => <AppLink to={`/send-money/${e}`}>Send Money</AppLink>,
    },
    {
      title: "Customer Ref",
      dataIndex: "userId",
      width: 120,
    },
    {
      title: "ID Verification",
      dataIndex: "isKYCCompleted",
      render: (e) => <StatusBadge status={e ? "verified" : "not verified"} />,
      width: 200,
    },
    {
      title: "Account Type",
      dataIndex: "accountType",
      render: (e) => <div>{e === 1 ? "Individual" : "Business"}</div>,
      width: 120,
    },
    {
      title: "Name",
      dataIndex: "userId",
      width: 200,
      render: (e) => (
        <AppLink to={`/customers/${e}?tab=overview`}>
          {handleGetItm(e, "userId", data?.data)?.firstName}
        </AppLink>
      ),
    },

    {
      title: "email",
      dataIndex: "email",
      width: 250,
    },
    {
      title: "Address",
      dataIndex: "address",
      width: 270,
    },
    {
      title: "Mobile Number",
      dataIndex: "phone",
      width: 140,
    },

    {
      title: "date",
      dataIndex: "dateCreated",
      render: (e) => moment(e).format("DD/MM/YY, hh:mm:ss"),
      width: 180,
    },
    {
      title: "Email Verified",
      dataIndex: "isEmailVerified",
      render: (e) => <StatusBadge status={e ? "True" : "False"} />,
      width: 140,
    },
  ];
  const [open, setOpen] = useState(false);

  const [search, setSearch] = useState("");

  const newData = applyFilters(data?.data, search, [
    "userId",
    "firstName",
    "surName",
    "address",
    "email",
    "phone",
    "status",
  ]);

  return (
    <DashboardStyle>
      <InviteAgentModal open={open} setOpen={setOpen} />
      <Flex
        align={{ initial: "flex-start", md: "center" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "flex-start", md: "between" }}
      >
        <SectionHeader
          title="Send Money"
          description={`This page allows you to manage customers.`}
        />
      </Flex>
      &nbsp;
      <CustomTable
        loading={isLoading}
        tableColumns={columns}
        arrayData={newData?.length ? newData : data?.data}
        topContent={
          <>
            <Flex justify={"between"} align={"center"}>
              <FormInput
                placeholder={"Search by Name, Customer ref or Address"}
                background="#fff"
                IconLeft={Search}
                width="400px"
                onChange={(e) => {
                  setSearch(e?.target?.value?.trim());
                }}
              />
            </Flex>
          </>
        }
        scroll={{
          x: 1600,
        }}
      />
    </DashboardStyle>
  );
}

const DashboardStyle = styled.div`
  color: #000;
`;
