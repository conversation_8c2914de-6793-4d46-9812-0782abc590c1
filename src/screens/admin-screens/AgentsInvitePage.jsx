import AppLink from "@/components/bits/AppLink";
import CustomTable from "@/components/bits/CustomTable";
import FormInput from "@/components/bits/FormInput";
//import MiniNavBar from "@/components/bits/MiniNavBar";
import SectionHeader from "@/components/bits/SectionHeader";
import { StatusBadge } from "@/components/bits/StatusBadge";
import { applyFilters, handleGetItm } from "@/lib/utils";
import { ApiServiceAdmin } from "@/service/admin-services";
import { Notification } from "@arco-design/web-react";
//import { handleSetItem } from "@/lib/utils";
import { Flex } from "@radix-ui/themes";
import { useMutation, useQuery } from "@tanstack/react-query";
import { CopyIcon, Search } from "lucide-react";
import { useState } from "react";
import styled from "styled-components";

export default function AgentsInvitePage() {
  const { data, isLoading } = useQuery({
    queryKey: ["GetAgentInviteQuery"],
    queryFn: () => ApiServiceAdmin.GetAgentInviteQuery(),
  });
  console.log(data, "GetAgentInviteQuery");

  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.SendAgentInviteMutation,
    onSuccess: () => {},
    onError: () => {
      return;
    },
  });

  const columns = [
    {
      title: "Action",
      dataIndex: "id",
      render: (e) => (
        <div
          onClick={() => {
            mutate({
              firstName: handleGetItm(e, "id", data?.data)?.name,
              email: handleGetItm(e, "id", data?.data)?.email,
            });
          }}
        >
          <AppLink type="normal">Resend Invite</AppLink>
        </div>
      ),
    },
    {
      title: "status",
      dataIndex: "status",
      render: (e) => <StatusBadge status={e} />,
    },
    {
      title: "agent email",
      dataIndex: "email",
    },
    {
      title: "agent name",
      dataIndex: "name",
    },
    {
      title: "agent invite code",
      dataIndex: "inviteCode",
    },
    {
      title: "Copy Agent URL",
      dataIndex: "signUpURL",
      render: (e) => (
        <div
          onClick={() => {
            navigator.clipboard.writeText(
              `https://dashboard.bconsolutionsltd.co.uk/register/${e}`
            );
            Notification.success({
              title: "Copied",
              content:
                "Agent URL successfully copied, you can share to new users",
            });
          }}
        >
          <AppLink type="normal" color="#FF7434">
            Agent URL <CopyIcon color="#FF7434" size={14} />
          </AppLink>
        </div>
      ),
    },
  ];

  const agentInvite = data?.data;

  const [search, setSearch] = useState("");
  const newData = applyFilters(agentInvite, search, [
    "id",
    "name",
    "address",
    "email",
    "inviteCode",
    "status",
  ]);
  return (
    <DashboardStyle>
      <Flex
        align={{ initial: "flex-start", md: "center" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "flex-start", md: "between" }}
      >
        <SectionHeader
          title="Pending Invitations"
          description={`This page allows you to manage agent invites.`}
        />
      </Flex>
      &nbsp;
      <CustomTable
        loading={isLoading || isPending}
        tableColumns={columns}
        topContent={
          <Flex justify={"between"} align={"end"}>
            <FormInput
              placeholder={"Search by Name, Agent code or email"}
              background="#fff"
              IconLeft={Search}
              width="400px"
              onChange={(e) => {
                setSearch(e?.target?.value?.trim());
              }}
            />
          </Flex>
        }
        arrayData={newData}
        scroll={{
          x: 1100,
        }}
      />
    </DashboardStyle>
  );
}

const DashboardStyle = styled.div`
  color: #000;
`;
