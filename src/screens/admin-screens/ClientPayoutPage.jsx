//import MiniNavBar from "@/components/bits/MiniNavBar";
import SectionHeader from "@/components/bits/SectionHeader";
import ClientListTable from "@/components/payout-dashboard-tables-chart/ClientListTable";
//import { handleGetItm } from "@/lib/utils";
//import { handleSetItem } from "@/lib/utils";
import { Flex } from "@radix-ui/themes";
//import { useNavigate } from "react-router-dom";
import styled from "styled-components";

export default function ClientPayoutPage() {
  return (
    <DashboardStyle>
      <Flex
        align={{ initial: "flex-start", md: "center" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "flex-start", md: "between" }}
      >
        <SectionHeader title="Clients" description={``} />
      </Flex>
      &nbsp;
      <ClientListTable topContent={true} />
    </DashboardStyle>
  );
}

const DashboardStyle = styled.div`
  color: #000;
`;
