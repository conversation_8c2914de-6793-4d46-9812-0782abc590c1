import CustomTable from "@/components/bits/CustomTable";
import FormInput from "@/components/bits/FormInput";
//import MiniNavBar from "@/components/bits/MiniNavBar";
import SectionHeader from "@/components/bits/SectionHeader";
import { useAuth } from "@/context/global.context";
import { applyFilters } from "@/lib/utils";
import { ApiServiceAdmin } from "@/service/admin-services";
//import { handleSetItem } from "@/lib/utils";
import { Flex } from "@radix-ui/themes";
import { useQuery } from "@tanstack/react-query";
import { Search } from "lucide-react";
import moment from "moment";
import { useState } from "react";
import styled from "styled-components";

export default function BeneficiariesPage() {
  const { user_id } = useAuth();

  const { data, isFetching } = useQuery({
    queryKey: ["GetBeneficiaryQuery"],
    queryFn: () =>
      ApiServiceAdmin.GetBeneficiaryQuery({
        userId: user_id,
        beneficiaryId: 0,
      }),
  });
  console.log(data, "bene");

  const columns = [
    {
      title: "Customer ID",
      dataIndex: "id",
      width: 200,
    },
    {
      title: "Beneficiary Name",
      dataIndex: "beneficiaryName",
      width: 200,
    },
    {
      title: "Beneficiary Bank",
      dataIndex: "beneficiaryBank[bankName]",
      width: 200,
    },
    {
      title: "Beneficiary Account No",
      dataIndex: "beneficiaryBank[accountNumber]",
      width: 200,
    },
    {
      title: "Mobile Number",
      dataIndex: "beneficiaryPhoneNumber",
      width: 200,
    },
    {
      title: "date",
      dataIndex: "dateCreated",
      render: (e) => moment(e).format("DD/MM/YY, hh:mm:ss"),
      width: 200,
    },
  ];

  const [search, setSearch] = useState("");

  const newData = applyFilters(data?.data, search, ["id", "beneficiaryName"]);
  return (
    <DashboardStyle>
      <Flex
        align={{ initial: "flex-start", md: "center" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "flex-start", md: "between" }}
      >
        <SectionHeader
          title="Beneficiaries"
          description={`This page shows you all beneficiaries in the system.`}
        />
      </Flex>
      &nbsp;
      <CustomTable
        topContent={
          <Flex justify={"between"} align={"end"}>
            <FormInput
              placeholder={"Search by Name or ID"}
              background="#fff"
              IconLeft={Search}
              width="400px"
              onChange={(e) => {
                setSearch(e?.target?.value?.trim());
              }}
            />
          </Flex>
        }
        tableColumns={columns}
        arrayData={newData?.length ? newData : data?.data}
        loading={isFetching}
        scroll={{
          x: 1600,
        }}
      />
    </DashboardStyle>
  );
}

const DashboardStyle = styled.div`
  color: #000;
`;
