import AppButton from "@/components/bits/AppButton";
import AppLink from "@/components/bits/AppLink";
import { CurrencyBadge } from "@/components/bits/CurrencyBadge";
import CustomTable from "@/components/bits/CustomTable";
import FormInput from "@/components/bits/FormInput";
import { ProviderBadge } from "@/components/bits/ProviderBadge";
//import MiniNavBar from "@/components/bits/MiniNavBar";
import SectionHeader from "@/components/bits/SectionHeader";
import SwitchInput from "@/components/bits/SwitchInput";
import { useAuth } from "@/context/global.context";
import { handleGetItm } from "@/lib/utils";
import AddPayoutProcessorModal from "@/Modals/AddPayoutProcessorModal";
import UpdatePayoutProcessorModal from "@/Modals/UpdatePayoutProcessorModal";
//import { handleGetItm } from "@/lib/utils";
import { ApiServiceAdmin } from "@/service/admin-services";
//import { handleSetItem } from "@/lib/utils";
import { Flex } from "@radix-ui/themes";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Play, Plus, Search, StopCircle } from "lucide-react";
import { useState } from "react";
//import { useNavigate } from "react-router-dom";
import styled from "styled-components";

export default function PayoutChannelProcessorPage() {
  const { user_id } = useAuth();
  const [open, setOpen] = useState(false);
  const [open2, setOpen2] = useState();
  const [item, setItem] = useState();
  const { data, isFetching, refetch } = useQuery({
    queryKey: ["GetPayoutChannelProcessorsQuery"],
    queryFn: () =>
      ApiServiceAdmin.GetPayoutChannelProcessorsQuery({
        role: user_id,
        rateMetaDataId: 0,
      }),
  });

  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.TogglePayoutProcessorMutation,
    onSuccess: () => {
      refetch();
    },
    onError: () => {
      return;
    },
  });
  //const navigate = useNavigate();
  const columns = [
    {
      title: "action",
      dataIndex: "id",
      fixed: "left",
      width: 160,
      render: (e) => (
        <div
          onClick={() => {
            console.log(e);
            setItem(handleGetItm(e, "id", data?.data));
            setOpen2(true);
          }}
        >
          <AppLink type="norml"> Update Channel</AppLink>
        </div>
      ),
    },
    {
      title: "channel id",
      dataIndex: "id",
      width: 200,
    },
    {
      title: "currency",
      dataIndex: "currency[code]",
      render: (e) => <CurrencyBadge currency={e} />,
      width: 200,
    },
    {
      title: "channel name",
      dataIndex: "name",
      width: 200,
    },
    {
      title: "payout channel",
      dataIndex: "payoutChannel[name]",
      width: 200,
    },
    {
      title: "payout processor",
      dataIndex: "payoutProvider",
      render: (e) => (
        <ProviderBadge name={e?.name} logo={e?.logo} fontSize="12px" />
      ),
      width: 200,
    },
    {
      title: "payout description",
      dataIndex: "description",
      width: 200,
    },
    {
      title: "status",
      dataIndex: "id",
      width: 200,
      render: (e) => (
        <SwitchInput
          checked={handleGetItm(e, "id", data?.data)?.status}
          onChange={(checked) => {
            mutate({
              action: checked ? 1 : 0,
              objectId: e,
            });
          }}
        />
      ),
    },
  ];

  const [search, setSearch] = useState("");

  const applyFilters = (data, search) => {
    return data?.filter((item) => {
      let matchesSearch = true;
      if (search) {
        const lowerSearch = search.toLowerCase();
        const refMatch = item?.id
          ?.toString()
          ?.toLowerCase()
          ?.includes(lowerSearch);
        const name = item?.name
          ?.toString()
          ?.toLowerCase()
          ?.includes(lowerSearch);

        const client = item?.currency?.name
          ?.toString()
          ?.toLowerCase()
          ?.includes(lowerSearch);
        const code = item?.currency?.code
          ?.toString()
          ?.toLowerCase()
          ?.includes(lowerSearch);
        matchesSearch = refMatch || client || code || name;
      }

      return matchesSearch;
    });
  };
  return (
    <DashboardStyle>
      <AddPayoutProcessorModal
        setOpen={setOpen}
        open={open}
        finished={refetch}
      />
      {open2 && (
        <UpdatePayoutProcessorModal
          setOpen={setOpen2}
          open={open2}
          finished={refetch}
          data={item}
        />
      )}
      <Flex
        align={{ initial: "flex-start", md: "center" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "flex-start", md: "between" }}
      >
        <SectionHeader
          title="Payout Channel Providers"
          description={`This page allows you manage payout channels`}
        />
        <AppButton
          IconLeft={Plus}
          placeholder="New Processor"
          width="200px"
          loading={false}
          disabled={false}
          onClick={() => {
            setOpen(true);
          }}
        />
      </Flex>
      &nbsp;
      <CustomTable
        topContent={
          <Flex justify={"between"} align={"center"}>
            <FormInput
              placeholder={"Search..."}
              background="#fff"
              IconLeft={Search}
              width="400px"
              onChange={(e) => {
                setSearch(e.target.value);
              }}
            />

            <Flex width={"fit-content"} gap={"4"}>
              <AppButton
                outline
                placeholder={"Disable Channels"}
                IconLeft={StopCircle}
                width="170px"
                loading={isPending}
                disabled={isPending}
                textColor="red"
                onClick={() => {
                  mutate({
                    action: 0,
                    objectId: 0,
                  });
                }}
              />
              <AppButton
                outline
                placeholder={"Activate Channels"}
                IconLeft={Play}
                width="170px"
                loading={isPending}
                disabled={isPending}
                onClick={() => {
                  mutate({
                    action: 1,
                    objectId: 0,
                  });
                }}
              />
            </Flex>
          </Flex>
        }
        tableColumns={columns}
        arrayData={applyFilters(data?.data, search)}
        loading={isFetching || isPending}
      />
    </DashboardStyle>
  );
}

const DashboardStyle = styled.div`
  color: #000;
`;
