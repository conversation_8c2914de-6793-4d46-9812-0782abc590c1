import AppButton from "@/components/bits/AppButton";
import AppLink from "@/components/bits/AppLink";
import CustomTable from "@/components/bits/CustomTable";
import FormInput from "@/components/bits/FormInput";
//import MiniNavBar from "@/components/bits/MiniNavBar";
import SectionHeader from "@/components/bits/SectionHeader";
import { StatusBadge } from "@/components/bits/StatusBadge";
import { useAuth } from "@/context/global.context";
import { applyFilters } from "@/lib/utils";
import AddProfessionModal from "@/Modals/AddProfession";
import EditProfessionModal from "@/Modals/EditProfession";
//import { handleGetItm } from "@/lib/utils";
import { ApiServiceAdmin } from "@/service/admin-services";
//import { handleSetItem } from "@/lib/utils";
import { Flex } from "@radix-ui/themes";
import { useQuery } from "@tanstack/react-query";
import { Plus, Search } from "lucide-react";
import { useState } from "react";
//import { useNavigate } from "react-router-dom";
import styled from "styled-components";

export default function ProfessionMasterPage() {
  const { user_id } = useAuth();
  const { data, refetch, isLoading } = useQuery({
    queryKey: ["GetProfessionQuery"],
    queryFn: () =>
      ApiServiceAdmin.GetProfessionQuery({
        role: user_id,
        rateMetaDataId: 0,
      }),
  });

  const [open, setOpen] = useState(false);
  const [open2, setOpen2] = useState(false);
  const [item, setItem] = useState({});

  //const navigate = useNavigate();
  const columns = [
    {
      title: "id",
      dataIndex: "id",
      render: (e, record) => (
        <div
          onClick={() => {
            setItem(record);
            setOpen2(true);
          }}
        >
          <AppLink>Update Profession</AppLink>
        </div>
      ),
    },
    {
      title: "profession name",
      dataIndex: "name",
    },
    {
      title: "Risk Level",
      dataIndex: "riskLevel[name]",
      render: (e) => <StatusBadge status={e} />,
    },
  ];

  const [search, setSearch] = useState("");

  const newData = applyFilters(data?.data, search, [
    "id",
    "name",
    "riskLevel[name]",
  ]);

  return (
    <DashboardStyle>
      {open2 && (
        <EditProfessionModal
          setOpen={setOpen2}
          open={open2}
          finished={refetch}
          item={item}
        />
      )}
      {open && (
        <AddProfessionModal setOpen={setOpen} open={open} finished={refetch} />
      )}
      <Flex
        align={{ initial: "flex-start", md: "center" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "flex-start", md: "between" }}
      >
        <SectionHeader
          title="Profession"
          description={`This page allows you to manage all profession`}
        />

        <AppButton
          placeholder={"New Profession"}
          IconLeft={Plus}
          width="200px"
          onClick={() => {
            setOpen(true);
          }}
        />
      </Flex>
      &nbsp;
      <CustomTable
        tableColumns={columns}
        loading={isLoading}
        arrayData={newData}
        topContent={
          <FormInput
            placeholder={"Search by profession name"}
            background="#fff"
            IconLeft={Search}
            width="400px"
            onChange={(e) => {
              setSearch(e?.target?.value?.trim());
            }}
          />
        }
      />
    </DashboardStyle>
  );
}

const DashboardStyle = styled.div`
  color: #000;
`;
