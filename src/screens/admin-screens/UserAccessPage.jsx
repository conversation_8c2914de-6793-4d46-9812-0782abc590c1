import AppLink from "@/components/bits/AppLink";
import CustomTable from "@/components/bits/CustomTable";
import MainSelect from "@/components/bits/MainSelect";
//import MiniNavBar from "@/components/bits/MiniNavBar";
import SectionHeader from "@/components/bits/SectionHeader";
import SwitchInput from "@/components/bits/SwitchInput";
import { useAuth } from "@/context/global.context";
import { ApiServiceAdmin } from "@/service/admin-services";
//import { handleSetItem } from "@/lib/utils";
import { Flex } from "@radix-ui/themes";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useState } from "react";
import styled from "styled-components";

export default function UserAccessPage() {
  const { user_id } = useAuth();
  const {
    data: list4,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["GetAccessRolesQuery"],
    queryFn: () => ApiServiceAdmin.GetAccessRolesQuery(),
  });
  const roles =
    list4 &&
    list4?.data?.map((itm) => {
      return {
        ...itm,
        label: itm?.name,
        value: itm?.name,
      };
    });
  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.ToggleAccessMenuMutation,
    onSuccess: () => {
      refetch();
    },
    onError: () => {
      return;
    },
  });
  const { mutate: mutateSub, isPending: isPendingSub } = useMutation({
    mutationFn: ApiServiceAdmin.ToggleSubAccessMenuMutation,
    onSuccess: () => {
      refetch();
    },
    onError: () => {
      return;
    },
  });
  const [subItem, setSubItem] = useState();

  const columns = [
    {
      title: "module",
      dataIndex: "menuName",
      render: (e, record) => (
        <a href="#sub">
          <AppLink
            type="mmm"
            onClick={() => {
              console.log(record);
              setSubItem(record);
            }}
          >
            {e}
          </AppLink>
        </a>
      ),
    },
    {
      title: "deny",
      dataIndex: "accessId",
      render: (e, record) => (
        <SwitchInput
          checked={record?.menuAccessType?.id === 1}
          onChange={() => {
            mutate({
              accessId: e,
              lastUpdatedBy: user_id,
              roleId: main?.id,
              menuAccessType: {
                id: 1,
              },
            });
          }}
        />
      ),
    },
    {
      title: "Read-Only",
      dataIndex: "accessId",
      render: (e, record) => (
        <SwitchInput
          checked={record?.menuAccessType?.id === 2}
          onChange={() => {
            mutate({
              accessId: e,
              lastUpdatedBy: user_id,
              roleId: main?.id,
              menuAccessType: {
                id: 2,
              },
            });
          }}
        />
      ),
    },
    {
      title: "Read/Write",
      dataIndex: "accessId",
      render: (e, record) => (
        <SwitchInput
          checked={record?.menuAccessType?.id === 3}
          onChange={() => {
            mutate({
              accessId: e,
              lastUpdatedBy: user_id,
              roleId: main?.id,
              menuAccessType: {
                id: 3,
              },
            });
          }}
        />
      ),
    },
  ];

  const columns2 = [
    {
      title: "module",
      dataIndex: "subMenuName",
    },
    {
      title: "deny",
      dataIndex: "accessId",
      render: (e, record) => (
        <SwitchInput
          checked={record?.menuAccessType?.id === 1}
          onChange={() => {
            mutateSub({
              accessId: e,
              lastUpdatedBy: user_id,
              roleId: main?.id,
              menuAccessType: {
                id: 1,
              },
            });
          }}
        />
      ),
    },
    {
      title: "Read-Only",
      dataIndex: "accessId",
      render: (e, record) => (
        <SwitchInput
          checked={record?.menuAccessType?.id === 2}
          onChange={() => {
            mutateSub({
              accessId: e,
              lastUpdatedBy: user_id,
              roleId: main?.id,
              menuAccessType: {
                id: 2,
              },
            });
          }}
        />
      ),
    },
    {
      title: "Read/Write",
      dataIndex: "accessId",
      render: (e, record) => (
        <SwitchInput
          checked={record?.menuAccessType?.id === 3}
          onChange={() => {
            mutateSub({
              accessId: e,
              lastUpdatedBy: user_id,
              roleId: main?.id,
              menuAccessType: {
                id: 3,
              },
            });
          }}
        />
      ),
    },
  ];

  const [item, setItem] = useState();

  const main = list4 && list4?.data?.find((itm) => itm?.id === item?.id);
  const main2 =
    main?.userRoleMenuAccess &&
    main?.userRoleMenuAccess?.find(
      (itm) => itm?.accessId === subItem?.accessId
    );

  console.log(main2, "main2");

  return (
    <DashboardStyle>
      <Flex
        align={{ initial: "flex-start", md: "center" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "flex-start", md: "between" }}
      >
        <SectionHeader
          title="User Access"
          description={`This page allows you to manage all access privileges.`}
        />
      </Flex>
      &nbsp;
      <CustomTable
        topContent={
          <>
            <MainSelect
              width="200px"
              options={roles || []}
              onChange={(e) => {
                setItem(e);
              }}
            />
          </>
        }
        loading={isLoading || isPending || isPendingSub}
        tableColumns={columns}
        arrayData={main?.userRoleMenuAccess}
        scroll={{
          x: 1100,
        }}
      />
      &nbsp;
      <div id="sub">
        <CustomTable
          topContent={
            <>
              <SectionHeader
                description=""
                title={
                  main2?.userRoleSubMenuAccess?.length
                    ? `Sub Menus - for ${main?.name}'s - ${main2?.menuName} Menu`
                    : "Sub Menus"
                }
                fontSize="28px"
              />
            </>
          }
          loading={isLoading || isPending || isPendingSub}
          tableColumns={columns2}
          arrayData={main2?.userRoleSubMenuAccess}
          scroll={{
            x: 1100,
          }}
        />
      </div>
    </DashboardStyle>
  );
}

const DashboardStyle = styled.div`
  color: #000;
`;
