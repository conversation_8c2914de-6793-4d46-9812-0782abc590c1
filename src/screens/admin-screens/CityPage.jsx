import CustomTable from "@/components/bits/CustomTable";
import FormInput from "@/components/bits/FormInput";
//import MiniNavBar from "@/components/bits/MiniNavBar";
import SectionHeader from "@/components/bits/SectionHeader";
import { applyFilters } from "@/lib/utils";
//import { handleGetItm } from "@/lib/utils";
import { ApiServiceAdmin } from "@/service/admin-services";
//import { handleSetItem } from "@/lib/utils";
import { Flex } from "@radix-ui/themes";
import { useQuery } from "@tanstack/react-query";
import { Search } from "lucide-react";
import { useState } from "react";
import { useParams, useSearchParams } from "react-router-dom";
//import { useNavigate } from "react-router-dom";
import styled from "styled-components";

export default function CityPage() {
  const [query] = useSearchParams();
  const { id } = useParams();
  const { data } = useQuery({
    queryKey: ["GetCountriesQuery"],
    queryFn: () => ApiServiceAdmin.GetCitiesQuery(id),
  });

  const country = query.get("country");

  //const navigate = useNavigate();
  const columns = [
    {
      title: "city id",
      dataIndex: "id",
    },

    {
      title: "city name",
      dataIndex: "name",
    },
    {
      title: "longitude",
      dataIndex: "longitude",
    },
    {
      title: "latitude",
      dataIndex: "latitude",
    },
  ];

  const [search, setSearch] = useState("");

  const newData = applyFilters(data?.data, search, [
    "id",
    "name",
    "regionId",
    "currencyCode",
    "subRegionId",
    "isCollectionCurrency",
    "telephoneCode",
    "status",
  ]);

  return (
    <DashboardStyle>
      <Flex
        align={{ initial: "flex-start", md: "center" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "flex-start", md: "between" }}
      >
        <SectionHeader
          title={`Cities for ${country}`}
          description={`This page allows you to see all ${country} cities`}
        />
      </Flex>
      &nbsp;
      <CustomTable
        tableColumns={columns}
        arrayData={newData}
        topContent={
          <FormInput
            placeholder={"Search by city name"}
            background="#fff"
            IconLeft={Search}
            width="400px"
            onChange={(e) => {
              setSearch(e?.target?.value?.trim());
            }}
          />
        }
      />
    </DashboardStyle>
  );
}

const DashboardStyle = styled.div`
  color: #000;
`;
