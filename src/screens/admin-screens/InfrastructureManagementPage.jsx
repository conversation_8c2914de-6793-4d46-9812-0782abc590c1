import AppButton from "@/components/bits/AppButton";
import Box from "@/components/bits/Box";
import DetailsHeading from "@/components/bits/DetailsHeading";
import SectionHeader from "@/components/bits/SectionHeader";
import MainSelect from "@/components/bits/MainSelect";
import { Flex, /* Switch ,*/ Checkbox, Spinner } from "@radix-ui/themes";
import { Circle } from "lucide-react";
import Gauge<PERSON>hart from "react-gauge-chart";
import FormTextArea from "@/components/bits/FormTextArea";
import TagInput from "@/components/bits/TagInput";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useQuery } from "@tanstack/react-query";
import styled from "styled-components";
import { useMutation } from "@tanstack/react-query";
import { useState } from "react";

export default function InfrastructureManagementPage() {
  const {
    data: resources,
    refetch,
    isLoading,
  } = useQuery({
    queryKey: ["GetSystemResourcesQuery"],
    queryFn: () => ApiServiceAdmin.GetSystemResourcesQuery(),
  });

  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.UpdateSystemResourcesMutation,
    onSuccess: () => {
      refetch();
    },
    onError: () => {
      return;
    },
  });

  const [formValues, setFormValues] = useState(() => {
    const initialFormValues = resources?.data?.resource?.reduce((acc, item) => {
      acc[item?.resourceId] = {
        mediumFrequency:
          item.notificationSettings.mediumLevelNotificationFrequency,
        highFrequency: item.notificationSettings.highLevelNotificationFrequency,
        mediumEnabled: true,
        highEnabled: true,
        message: item.notificationSettings.mediumLevelNotificationMessage,
        sendEmail: item.notificationSettings.sendEmail,
        emailRecipients: item.notificationSettings.emailRecipients,
        sendText: item.notificationSettings.sendText,
        textRecipients: item.notificationSettings.textRecipients,
      };
      return acc;
    }, {});
    return initialFormValues;
  });

  const handleFrequencyChange = (resourceId, field, value) => {
    console.log(`Frequency changed: ${resourceId}, ${field}, value:`, value);
    setFormValues((prevValues) => ({
      ...prevValues,
      [resourceId]: {
        ...prevValues?.[resourceId],
        [field]: value,
      },
    }));
  };

  const handleCheckboxChange = (resourceId, field, checked) => {
    setFormValues((prevValues) => ({
      ...prevValues,
      [resourceId]: {
        ...prevValues?.[resourceId],
        [field]: checked,
      },
    }));
  };

  const handleMessageChange = (resourceId, message) => {
    setFormValues((prevValues) => ({
      ...prevValues,
      [resourceId]: {
        ...prevValues?.[resourceId],
        message,
      },
    }));
  };

  const handleRecipientsChange = (resourceId, field, values) => {
    setFormValues((prevValues) => ({
      ...prevValues,
      [resourceId]: {
        ...prevValues?.[resourceId],
        [field]: values,
      },
    }));
  };

  const handleSaveChanges = () => {
    const updatedResources = resources?.data?.resource?.map((item) => ({
      ...item,
      notificationSettings: {
        mediumLevelNotificationFrequency:
          formValues?.[item.resourceId]?.mediumFrequency ||
          item.notificationSettings.mediumLevelNotificationFrequency,
        highLevelNotificationFrequency:
          formValues?.[item.resourceId]?.highFrequency ||
          item.notificationSettings.highLevelNotificationFrequency,
        mediumLevelNotificationMessage:
          formValues?.[item.resourceId]?.message ||
          item.notificationSettings.mediumLevelNotificationMessage,
        sendEmail:
          formValues?.[item.resourceId]?.sendEmail ||
          item.notificationSettings.sendEmail,
        emailRecipients:
          formValues?.[item.resourceId]?.emailRecipients ||
          item.notificationSettings.emailRecipients,
        sendText:
          formValues?.[item.resourceId]?.sendText ||
          item.notificationSettings.sendText,
        textRecipients:
          formValues?.[item.resourceId]?.textRecipients ||
          item.notificationSettings.textRecipients,
      },
    }));
    mutate({ resource: updatedResources });
  };

  if (isLoading) {
    return (
      <div
        style={{
          display: "grid",
          placeItems: "center",
          height: "80vh",
          textAlign: "center",
        }}
      >
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            textAlign: "center",
          }}
        >
          <div style={{}}>Loading System Monitor...</div>
          <Spinner
            size={"3"}
            style={{
              width: 80,
              height: 80,
            }}
          />
        </div>
      </div>
    );
  }

  return (
    <DashboardStyle>
      <Flex
        align={{ initial: "flex-start", md: "center" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "flex-start", md: "between" }}
      >
        <SectionHeader description="Set a notification trigger for each metrics" />
      </Flex>
      &nbsp;
      <Box>
        <div
          style={{
            display: "grid",
            gap: "20px",
            gridTemplateColumns: "1fr 1fr 1fr",
          }}
        >
          {resources?.data?.resource?.map((item) => (
            <div key={item.resourceId}>
              <SectionHeader
                title={item?.type}
                fontSize="16px"
                description={`Set a trigger notification for ${item.type.toLowerCase()} usage at medium and high risk`}
              />
              <GaugeChart
                nrOfLevels={3}
                colors={["#6AB36C", "#FDA333", "#EB615E"]}
                arcWidth={0.23}
                arcsLength={[0.2, 0.2, 0.2]}
                cornerRadius={0}
                style={{ width: "100%" }}
                textColor="#fd9c0a0"
                hideText={false}
                percent={item.usage / 100}
                marginInPercent={0}
                arcPadding={0}
              />
              <Flex
                align={{ initial: "start" }}
                justify={{ initial: "between" }}
                direction={{ initial: "column" }}
                width={"100%"}
                gap="4"
              >
                <Flex gap="4" width={"100%"} align={"center"}>
                  <Circle fill="#FDA333" stroke="#FDA333" size={"60"} />
                  <MainSelect
                    label={"Frequency"}
                    width="100%"
                    mainValue={
                      formValues?.[item.resourceId]?.mediumFrequency ||
                      item.notificationSettings.mediumLevelNotificationFrequency
                    }
                    onChange={(selectedOption) =>
                      handleFrequencyChange(
                        item.resourceId,
                        "mediumFrequency",
                        selectedOption.value
                      )
                    }
                    options={[
                      { label: "5 minutes", value: 5 },
                      { label: "10 minutes", value: 10 },
                      { label: "15 minutes", value: 15 },
                      { label: "20 minutes", value: 20 },
                      { label: "25 minutes", value: 25 },
                      { label: "30 minutes", value: 30 },
                      { label: "35 minutes", value: 35 },
                      { label: "40 minutes", value: 40 },
                      { label: "45 minutes", value: 45 },
                      { label: "50 minutes", value: 50 },
                      { label: "55 minutes", value: 55 },
                      { label: "60 minutes", value: 60 },
                    ]}
                  />
                  {/*  <Switch
                    checked={
                      formValues?.[item.resourceId]?.mediumEnabled || true
                    }
                    onCheckedChange={(checked) =>
                      handleCheckboxChange(
                        item.resourceId,
                        "mediumEnabled",
                        checked
                      )
                    }
                  /> */}
                </Flex>
                <Flex gap="4" width={"100%"} align={"center"}>
                  <Circle fill="#EB615E" stroke="#EB615E" size={"60"} />
                  <MainSelect
                    label={"Frequency"}
                    width="100%"
                    mainValue={
                      formValues?.[item.resourceId]?.highFrequency ||
                      item.notificationSettings.highLevelNotificationFrequency
                    }
                    onChange={(selectedOption) =>
                      handleFrequencyChange(
                        item.resourceId,
                        "highFrequency",
                        selectedOption.value
                      )
                    }
                    options={[
                      { label: "5 minutes", value: 5 },
                      { label: "10 minutes", value: 10 },
                      { label: "15 minutes", value: 15 },
                      { label: "20 minutes", value: 20 },
                      { label: "25 minutes", value: 25 },
                      { label: "30 minutes", value: 30 },
                      { label: "35 minutes", value: 35 },
                      { label: "40 minutes", value: 40 },
                      { label: "45 minutes", value: 45 },
                      { label: "50 minutes", value: 50 },
                      { label: "55 minutes", value: 55 },
                      { label: "60 minutes", value: 60 },
                    ]}
                  />
                  {/*    <Switch
                    checked={formValues?.[item.resourceId]?.highEnabled || true}
                    onCheckedChange={(checked) =>
                      handleCheckboxChange(
                        item.resourceId,
                        "highEnabled",
                        checked
                      )
                    }
                  /> */}
                </Flex>
              </Flex>

              <FormTextArea
                label={"Message"}
                value={
                  formValues?.[item.resourceId]?.message ||
                  item.notificationSettings.mediumLevelNotificationMessage
                }
                onChange={(e) =>
                  handleMessageChange(item.resourceId, e.target.value)
                }
              />
              <div>
                <Flex align={"start"} gap={"4"}>
                  <Checkbox
                    onCheckedChange={(checked) =>
                      handleCheckboxChange(
                        item.resourceId,
                        "sendEmail",
                        checked
                      )
                    }
                    type="checkbox"
                    checked={
                      formValues?.[item.resourceId]?.sendEmail !== undefined
                        ? formValues?.[item.resourceId]?.sendEmail
                        : item.notificationSettings.sendEmail
                    }
                    color="amber"
                    size="2"
                  />
                  <div>
                    <SectionHeader
                      title={"By Email"}
                      fontSize="15px"
                      description={"Send trigger notification via email"}
                    />
                    <TagInput
                      label={"Email address(es)"}
                      placeholder={"Enter Email address(es)"}
                      value={
                        formValues?.[item.resourceId]?.emailRecipients ||
                        item.notificationSettings.emailRecipients
                      }
                      onChange={(values) =>
                        handleRecipientsChange(
                          item.resourceId,
                          "emailRecipients",
                          values
                        )
                      }
                    />
                  </div>
                </Flex>
                <Flex align={"start"} gap={"4"}>
                  <Checkbox
                    checked={
                      formValues?.[item.resourceId]?.sendText !== undefined
                        ? formValues?.[item.resourceId]?.sendText
                        : item.notificationSettings.sendText
                    }
                    onCheckedChange={(checked) =>
                      handleCheckboxChange(item.resourceId, "sendText", checked)
                    }
                    type="checkbox"
                    color="amber"
                    size="2"
                  />
                  <div>
                    <SectionHeader
                      title={"By SMS"}
                      fontSize="15px"
                      description={"Send trigger notification via SMS"}
                    />
                    <TagInput
                      label={"Phone number(s)"}
                      placeholder={"Enter number(s)"}
                      hint={"Press (Enter) to add more numbers"}
                      value={
                        formValues?.[item.resourceId]?.textRecipients ||
                        item.notificationSettings.textRecipients
                      }
                      onChange={(values) =>
                        handleRecipientsChange(
                          item.resourceId,
                          "textRecipients",
                          values
                        )
                      }
                    />
                  </div>
                </Flex>
              </div>
            </div>
          ))}
        </div>
        <DetailsHeading title="" />
        <Flex gap="3" mt="4" justify="between">
          <div></div>
          <AppButton
            placeholder="Save changes"
            width="fit-content"
            loading={isPending}
            disabled={isPending}
            onClick={handleSaveChanges}
          />
        </Flex>
      </Box>
    </DashboardStyle>
  );
}

const DashboardStyle = styled.div`
  color: #000;
`;
