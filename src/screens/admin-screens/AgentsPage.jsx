import AppButton from "@/components/bits/AppButton";
import AppLink from "@/components/bits/AppLink";
import { CurrencyBadge } from "@/components/bits/CurrencyBadge";
import CustomTable from "@/components/bits/CustomTable";
import { MenuItem, MenuList } from "@/components/bits/DropDownMenu";
import FormInput from "@/components/bits/FormInput";
//import MiniNavBar from "@/components/bits/MiniNavBar";
import SectionHeader from "@/components/bits/SectionHeader";
import { StatusBadge } from "@/components/bits/StatusBadge";
import SwitchInput from "@/components/bits/SwitchInput";
import { applyFilters, handleGetItm } from "@/lib/utils";
import DepleteCustomerWalletModal from "@/Modals/DepleteCustomerWallet";
import FundCustomerModal from "@/Modals/FundCustomerWallet";
import InviteAgentModal from "@/Modals/InviteAgentModal";
import MessageCustomerModal from "@/Modals/MessageCustomer";
import UpdateCustomerRateModal from "@/Modals/UpdateCustomerRate";
import { ApiServiceAdmin } from "@/service/admin-services";
import { Notification } from "@arco-design/web-react";
//import { handleSetItem } from "@/lib/utils";
import { Flex } from "@radix-ui/themes";
import { useMutation, useQuery } from "@tanstack/react-query";
import { CopyIcon, Megaphone, Plus, Search } from "lucide-react";
import { useState } from "react";
import styled from "styled-components";

export default function AgentsPage() {
  const { data, refetch, isLoading } = useQuery({
    queryKey: ["GetAgentsQueryyyy"],
    queryFn: () => ApiServiceAdmin.GetAgentsQuery(),
  });

  const [updateRateModal, setUpdateRateModal] = useState(false);
  const [messageCustomerModal, setMessageCustomerModal] = useState(false);
  const [fundCustomerModal, setFundCustomerModal] = useState(false);
  const [depleteCustomerModal, setDepleteCustomerModal] = useState(false);

  const { mutate: activateUser, isPending: isPendingActivate } = useMutation({
    mutationFn: ApiServiceAdmin.ActivateCustomerMutation,
    onSuccess: () => {
      refetch();
    },
    onError: () => {
      return;
    },
  });

  const { mutate: deactivateUser, isPending: isPendingDeactivate } =
    useMutation({
      mutationFn: ApiServiceAdmin.DeactivateCustomerMutation,
      onSuccess: () => {
        refetch();
      },
      onError: () => {
        return;
      },
    });

  const { mutate: suspendUser, isPending: isPendingSuspend } = useMutation({
    mutationFn: ApiServiceAdmin.SuspendCustomerMutation,
    onSuccess: () => {
      refetch();
    },
    onError: () => {
      return;
    },
  });

  const { mutate: watchUser, isPending: isPendingWatch } = useMutation({
    mutationFn: ApiServiceAdmin.WatchCustomerMutation,
    onSuccess: () => {
      refetch();
    },
    onError: () => {
      return;
    },
  });

  const [item, setItem] = useState();
  const userMenu = [
    {
      name: "Update Rate",
      status: ["active", "inactive", "on hold"],
      action: () => {
        setUpdateRateModal(true);
      },
    },

    {
      name: "Suspend Agent",
      status: ["active", "on hold"],
      action: () => {
        suspendUser({
          userId: item?.userId,
        });
      },
    },
    {
      name: "Unsuspend Agent",
      status: ["suspended"],
      action: () => {
        activateUser({
          userId: item?.userId,
        });
      },
    },
    {
      name: "Watch Agent",
      status: ["active", "inactive", "on hold"],
      action: () => {
        watchUser({
          userId: item?.userId,
          watchListStatus: true,
        });
      },
    },
    {
      name: "Unwatch Agent",
      status: ["active", "inactive", "on hold"],
      action: () => {
        watchUser({
          userId: item?.userId,
          watchListStatus: false,
        });
      },
    },
    {
      name: "Message Agent",
      status: ["active", "inactive", "on hold"],
      action: () => {
        setMessageCustomerModal(true);
      },
    },
    {
      name: "Fund Wallet",
      status: ["active", "inactive", "on hold"],
      action: () => {
        setFundCustomerModal(true);
      },
    },
    {
      name: "Deplete Wallet",
      status: ["active", "inactive", "on hold"],

      action: () => {
        setDepleteCustomerModal(true);
      },
    },
    {
      name: "Activate Agent",
      status: ["inactive", "on hold"],
      action: () => {
        activateUser({
          userId: item?.userId,
        });
      },
    },
    {
      name: "Deactivate Agent",
      status: ["active", "on hold"],
      action: () => {
        deactivateUser({
          userId: item?.userId,
        });
      },
    },
  ];

  //ACTIONS START

  const { mutate: allowMultiCurrency, isPending: isPendingAllow } = useMutation(
    {
      mutationFn: ApiServiceAdmin.AllowMultiCurrencyMutation,
      onSuccess: () => {
        refetch();
      },
      onError: () => {
        return;
      },
    }
  );

  const { mutate: disallowMultiCurrency, isPending: isPendingDisallow } =
    useMutation({
      mutationFn: ApiServiceAdmin.DisallowMultiCurrencyMutation,
      onSuccess: () => {
        refetch();
      },
      onError: () => {
        return;
      },
    });

  const {
    mutate: allowCreditRequest,
    isPending: isPendingCreditRequestDisallow,
  } = useMutation({
    mutationFn: ApiServiceAdmin.AllowCreditRequestMutation,
    onSuccess: () => {
      refetch();
    },
    onError: () => {
      return;
    },
  });

  const {
    mutate: disallowCreditRequest,
    isPending: isPendingCreditRequestAllow,
  } = useMutation({
    mutationFn: ApiServiceAdmin.DisallowCreditRequestMutation,
    onSuccess: () => {
      refetch();
    },
    onError: () => {
      return;
    },
  });

  //ACTIONS END

  const columns = [
    {
      title: "Action",
      dataIndex: "userId",
      fixed: "left",
      width: 100,
      render: (e, record) => {
        const user = record;
        return (
          <MenuList
            onClick={() => {
              setItem(user);
            }}
            ActionIcon={undefined}
            ActionElement={undefined}
            iconWidth={undefined}
          >
            {!user?.watchListStatus
              ? userMenu
                  ?.filter((itm) => itm.name !== "Unwatch Agent")
                  ?.filter((itm) => {
                    return itm?.status?.includes(user?.status?.toLowerCase());
                  })
                  ?.map(({ name, action, index, color, to }) => (
                    <MenuItem
                      key={index}
                      name={name}
                      index={index}
                      action={() => {
                        action(e);
                      }}
                      Icon={undefined}
                      to={to && to(e)}
                      width={"200px"}
                      padding={2}
                      color={color}
                    />
                  ))
              : userMenu
                  ?.filter((itm) => itm.name !== "Watch Agent")
                  ?.filter((itm) => {
                    return itm?.status?.includes(user?.status?.toLowerCase());
                  })
                  ?.map(({ name, action, index, color, to }) => (
                    <MenuItem
                      key={index}
                      name={name}
                      index={index}
                      action={() => {
                        action(e);
                      }}
                      Icon={undefined}
                      to={to && to(e)}
                      width={"200px"}
                      padding={2}
                      color={color}
                    />
                  ))}
          </MenuList>
        );
      },
    },
    {
      title: "Agent name",
      dataIndex: "userId",
      width: 220,
      render: (e) => (
        <AppLink to={`/agents/${e}?tab=overview`}>
          {handleGetItm(e, "userId", data?.data)?.firstName}{" "}
          {handleGetItm(e, "userId", data?.data)?.surName}
        </AppLink>
      ),
    },
    {
      title: "Agent Category",
      render: (e) => <div>{e === 1 ? "Individual" : "Business"}</div>,
      width: 120,
    },
    {
      title: "Agent ref",
      dataIndex: "userId",
      width: 120,
    },
    {
      title: "status",
      dataIndex: "status",
      render: (e) => <StatusBadge status={e} />,

      width: 120,
    },
    {
      title: "ID Verification",
      dataIndex: "isKYCCompleted",
      render: (e) => <StatusBadge status={e ? "verified" : "not verified"} />,
      width: 120,
    },
    {
      title: "Multi Currency Trading",
      dataIndex: "userId",
      width: 200,
      render: (e, record) => (
        <SwitchInput
          checked={record?.allowMultiCurrencyTrading}
          onChange={() => {
            if (record?.allowMultiCurrencyTrading) {
              disallowMultiCurrency(e);
            } else {
              allowMultiCurrency(e);
            }
          }}
        />
      ),
    },
    {
      title: "Credit Request",
      dataIndex: "userId",
      width: 200,
      render: (e, record) => (
        <SwitchInput
          checked={record?.isCreditAgent}
          onChange={() => {
            if (record?.isCreditAgent) {
              disallowCreditRequest(e);
            } else {
              allowCreditRequest(e);
            }
          }}
        />
      ),
    },
    {
      title: "Agent email",
      dataIndex: "email",
      width: 220,
    },
    {
      title: "Address",
      dataIndex: "address",
      width: 270,
    },
    {
      title: "Mobile Number",
      dataIndex: "phone",
      width: 220,
    },
    {
      title: "Country",
      dataIndex: "country",
      render: (e) => (
        <CurrencyBadge currency={e?.currencyCode} name={e?.name} />
      ),
      width: 220,
    },
    {
      title: "Copy Agent URL",
      dataIndex: "userId",
      width: 220,
      render: (e) => (
        <div
          onClick={() => {
            navigator.clipboard.writeText(
              `https://dashboard.bconsolutionsltd.co.uk/register/${e}`
            );
            Notification.success({
              title: "Copied",
              content:
                "Agent URL successfully copied, you can share to new users",
            });
          }}
        >
          <AppLink type="normal" color="#FF7434">
            Agent URL <CopyIcon color="#FF7434" size={14} />
          </AppLink>
        </div>
      ),
    },
    {
      title: "date",
      dataIndex: "dateCreated",
      width: 220,
    },
    {
      title: "Email Verified",
      dataIndex: "isEmailVerified",
      render: (e) => <StatusBadge status={e ? "True" : "False"} />,
      width: 140,
    },
  ];
  const [open, setOpen] = useState(false);
  const [messageCustomerModalAll, setMessageCustomerModalAll] = useState(false);

  const [search, setSearch] = useState("");

  const newData = applyFilters(data?.data, search, [
    "userId",
    "firstName",
    "surName",
    "address",
    "email",
    "phone",
    "status",
  ]);

  return (
    <DashboardStyle>
      <InviteAgentModal open={open} setOpen={setOpen} finished={refetch} />
      <Flex
        align={{ initial: "flex-start", md: "center" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "flex-start", md: "between" }}
      >
        <SectionHeader
          title="Agents"
          description={`This page allows you to manage agent.`}
        />

        <Flex gap={"4"}>
          <AppButton
            IconLeft={Megaphone}
            placeholder="Message All"
            width="170px"
            loading={false}
            disabled={false}
            onClick={() => {
              setMessageCustomerModalAll(true);
            }}
            outline
          />
          <AppButton
            IconLeft={Plus}
            placeholder="Invite Agent"
            width="170px"
            loading={false}
            disabled={false}
            onClick={() => {
              setOpen(true);
            }}
          />
        </Flex>
      </Flex>
      {messageCustomerModalAll && (
        <MessageCustomerModal
          open={messageCustomerModalAll}
          setOpen={setMessageCustomerModalAll}
          finished={refetch}
          type={"all"}
          title="Message Agents"
          userRoleId={5}
        />
      )}
      {updateRateModal && (
        <UpdateCustomerRateModal
          open={updateRateModal}
          setOpen={setUpdateRateModal}
          item={item}
          finished={refetch}
        />
      )}
      {messageCustomerModal && (
        <MessageCustomerModal
          open={messageCustomerModal}
          setOpen={setMessageCustomerModal}
          item={item}
          title="Message Agent"
          finished={refetch}
          type={"single"}
          userRoleId={5}
        />
      )}
      {fundCustomerModal && (
        <FundCustomerModal
          open={fundCustomerModal}
          setOpen={setFundCustomerModal}
          item={item}
          finished={refetch}
        />
      )}
      {depleteCustomerModal && (
        <DepleteCustomerWalletModal
          open={depleteCustomerModal}
          setOpen={setDepleteCustomerModal}
          item={item}
          finished={refetch}
        />
      )}
      &nbsp;
      <CustomTable
        topContent={
          <Flex justify={"between"} align={"end"}>
            <FormInput
              placeholder={"Search by Name, Agent ref or Address"}
              background="#fff"
              IconLeft={Search}
              width="400px"
              onChange={(e) => {
                setSearch(e?.target?.value?.trim());
              }}
            />
          </Flex>
        }
        tableColumns={columns}
        loading={
          isLoading ||
          isPendingAllow ||
          isPendingDisallow ||
          isPendingActivate ||
          isPendingDeactivate ||
          isPendingSuspend ||
          isPendingWatch ||
          isPendingCreditRequestAllow ||
          isPendingCreditRequestDisallow
        }
        arrayData={newData?.length ? newData : data?.data}
        scroll={{
          x: 1600,
        }}
      />
    </DashboardStyle>
  );
}

const DashboardStyle = styled.div`
  color: #000;
`;
