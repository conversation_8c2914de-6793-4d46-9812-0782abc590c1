import CustomTable from "@/components/bits/CustomTable";
//import MiniNavBar from "@/components/bits/MiniNavBar";
import SectionHeader from "@/components/bits/SectionHeader";
import { StatusBadge } from "@/components/bits/StatusBadge";
import SwitchInput from "@/components/bits/SwitchInput";
import { useAuth } from "@/context/global.context";
//import { handleGetItm } from "@/lib/utils";
import { ApiServiceAdmin } from "@/service/admin-services";
//import { handleSetItem } from "@/lib/utils";
import { Flex } from "@radix-ui/themes";
import { useMutation, useQuery } from "@tanstack/react-query";
//import { useNavigate } from "react-router-dom";
import styled from "styled-components";

export default function PayoutChannelPage() {
  const { user_id } = useAuth();
  const { data, refetch, isLoading } = useQuery({
    queryKey: ["GetPayoutChannelQueryTableee"],
    queryFn: () =>
      ApiServiceAdmin.GetPayoutChannelQuery({
        role: user_id,
        rateMetaDataId: 0,
      }),
  });

  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.TogglePayoutChannelMutation,
    onSuccess: () => {
      refetch();
    },
    onError: () => {
      return;
    },
  });

  //const navigate = useNavigate();
  const columns = [
    {
      title: "id",
      dataIndex: "id",
    },
    {
      title: "Account Type",
      dataIndex: "accountType",
      render: (e) => <div>{e === 5 ? "Marketer" : "Customer"}</div>,
    },
    {
      title: "name",
      dataIndex: "name",
    },
    {
      title: "Description",
      dataIndex: "description",
    },
    {
      title: "status",
      dataIndex: "status",
      render: (e) => <StatusBadge status={e ? "true" : "false"} />,
    },
    {
      title: "make active",
      dataIndex: "status",
      render: (e, record) => (
        <SwitchInput
          onChange={() => {
            if (e) {
              mutate({
                objectId: record?.id, //Leave it as 0.
                action: 0, // 0 to deactivate, 1 to acctivate
                payoutChannelId: record?.id, //Payout channel ID.
              });
            } else {
              mutate({
                objectId: record?.id, //Leave it as 0.
                action: 1, // 0 to deactivate, 1 to acctivate
                payoutChannelId: record?.id, //Payout channel ID.
              });
            }
          }}
          checked={e}
        />
      ),
    },
  ];

  return (
    <DashboardStyle>
      <Flex
        align={{ initial: "flex-start", md: "center" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "flex-start", md: "between" }}
      >
        <SectionHeader
          title="Payout Channels"
          description={`This page allows you to manage all payout channels`}
        />
      </Flex>
      &nbsp;
      <CustomTable
        tableColumns={columns}
        arrayData={data?.data}
        loading={isPending || isLoading}
      />
    </DashboardStyle>
  );
}

const DashboardStyle = styled.div`
  color: #000;
`;
