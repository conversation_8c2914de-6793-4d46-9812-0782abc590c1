/* eslint-disable react-hooks/exhaustive-deps */
import AppButton from "@/components/bits/AppButton";
import Box from "@/components/bits/Box";
import CurrencySelect from "@/components/bits/CurrencySelect";
//import MiniNavBar from "@/components/bits/MiniNavBar";
import SectionHeader from "@/components/bits/SectionHeader";
import DividerHorizontal from "@/components/DividerHorizontal";
import { ApiServiceAdmin } from "@/service/admin-services";
//import { handleSetItem } from "@/lib/utils";
import { Flex, Grid } from "@radix-ui/themes";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useFormik } from "formik";
import { useEffect, useState } from "react";
import styled from "styled-components";
import { useParams } from "react-router-dom";
import { SendMoneySchema } from "@/Schema";
import BeneficiarySelect, {
  AddBeneficiaryButtton,
} from "@/components/bits/BeneficiarySelect";
import SendMoneyHeader from "@/components/customer-bits/SendMoneyHeader";
import PaymentChannelSelect from "@/components/bits/PaymentChannelSelect";
import PayoutChannelSelect from "@/components/bits/PayoutChannelSelect";
import PurposeSelect from "@/components/bits/PurposeSelect";
import FormInputLabel from "@/components/bits/FormInputLabel";
import FormInputNumber from "@/components/bits/FormInputNumber";
import FormTextArea from "@/components/bits/FormTextArea";
import { FormatCurrency } from "@/lib/utils";
import BoxList from "@/components/BoxList";
import BoxListHorizontal from "@/components/BoxListHorizontal";
import { Notification } from "@arco-design/web-react";
import QRCode from "react-qr-code";
import { Copy } from "lucide-react";
import AddBeneficiaryModal from "@/Modals/AddBeneficiaryModal";
import { useAuth } from "@/context/global.context";

export default function InitiateSendMoneyPage() {
  const [step, setStep] = useState(1);

  const [transactionData, setTransactionData] = useState();

  const { id } = useParams(); // Extract the ID from the route

  const { user_data } = useAuth();

  const { data: user } = useQuery({
    queryKey: ["GetCustomerQuery"],
    queryFn: () => ApiServiceAdmin.GetCustomerQuery(id),
  });

  const { data: beneficiaries, refetch: getBeneficiaries } = useQuery({
    queryKey: ["GetBeneficiaryUserQuery"],
    queryFn: () =>
      ApiServiceAdmin.GetBeneficiaryQuery({
        userId: id,
        beneficiaryId: 0,
      }),
  });

  console.log(user, "send money user");

  const steps = [
    {
      title: "Select Beneficiary",
      step: 1,
    },
    {
      title: "Send Money",
      step: 2,
    },
    {
      title: "Review Transfer",
      step: 3,
    },
  ];
  //const navigate = useNavigate();

  const { mutate, isPending, data } = useMutation({
    mutationFn: ApiServiceAdmin.SendMoneyMutation,
    onSuccess: () => {},
    onError: () => {
      return;
    },
  });

  const formik = useFormik({
    initialValues: {
      userId: Number(id),
      userBeneficiaryId: "",
      fromCurrencyId: "",
      toCurrencyId: 1,
      amount: 0,
      paymentChannelId: "",
      walletId: 0,
      payoutChannelId: "",
      purpose: "",
      note: "",
      transactionSource: "backOffice",
      promoCode: "",
      redirectURL: "https://admin.transferrocket.co.uk/send-money?step=1",
      source: "backOffice",
    },
    validationSchema: SendMoneySchema(step),
    onSubmit: (values) => {
      if (step === 3) {
        mutate(values);
      } else {
        setStep((curr) => curr + 1);
      }
    },
  });

  const {
    data: rate,
    refetch,
    isFetching,
  } = useQuery({
    queryKey: ["GetRateQuery"],
    queryFn: () =>
      user_data?.role?.name === "Agent"
        ? ApiServiceAdmin.GetAgentRateQuery({
            agentId: user_data?.userId,
            fromCurrencyId: formik?.values?.fromCurrencyId,
            toCurrencyId: formik?.values?.toCurrencyId,
            fromAmount: formik?.values?.amount,
            toAmount: 0,
            roleId: 6,
            userId: id,
          })
        : ApiServiceAdmin.GetRateQuery({
            fromCurrencyId: formik?.values?.fromCurrencyId,
            toCurrencyId: formik?.values?.toCurrencyId,
            fromAmount: formik?.values?.amount,
            toAmount: 0,
            roleId: 6,
            userId: id,
          }),
    enabled: formik?.values?.fromCurrencyId ? true : false,
  });
  useEffect(() => {
    refetch(
      user_data?.role?.name === "Agent"
        ? {
            agentId: user_data?.userId,
            fromCurrencyId: formik?.values?.fromCurrencyId,
            toCurrencyId: formik?.values?.toCurrencyId,
            fromAmount: formik?.values?.amount,
            toAmount: 0,
            roleId: 6,
            userId: id,
          }
        : {
            fromCurrencyId: formik?.values?.fromCurrencyId,
            toCurrencyId: formik?.values?.toCurrencyId,
            fromAmount: formik?.values?.amount,
            toAmount: 0,
            roleId: 6,
            userId: id,
          }
    );
    setTransactionData({
      ...transactionData,
      ...rate?.data,
      ...formik?.values,
    });
  }, [rate?.data]);

  useEffect(() => {
    refetch(
      user_data?.role?.name === "Agent"
        ? {
            agentId: user_data?.userId,
            fromCurrencyId: formik?.values?.fromCurrencyId,
            toCurrencyId: formik?.values?.toCurrencyId,
            fromAmount: formik?.values?.amount,
            toAmount: 0,
            roleId: 6,
            userId: id,
          }
        : {
            fromCurrencyId: formik?.values?.fromCurrencyId,
            toCurrencyId: formik?.values?.toCurrencyId,
            fromAmount: formik?.values?.amount,
            toAmount: 0,
            roleId: 6,
            userId: id,
          }
    );
    setTransactionData({
      ...transactionData,
      ...rate?.data,
      ...formik?.values,
    });
  }, [formik?.values]);

  console.log(formik.values, "values");

  const { data: list } = useQuery({
    queryKey: ["GetCurrencyHereQuery"],
    queryFn: () => ApiServiceAdmin.GetCurrencyQuery(),
  });

  const getCurrentCurrencyCountry = list?.data?.find(
    (itm) => itm?.code === user?.data?.country?.currencyCode
  );

  useEffect(() => {
    if (formik.values?.fromCurrencyId) {
      return;
    } else {
      formik.setFieldValue("fromCurrencyId", getCurrentCurrencyCountry?.id);
    }
  }, [getCurrentCurrencyCountry]);

  console.log(
    getCurrentCurrencyCountry,
    user?.data?.country?.currencyCode,
    "getCurrentCurrencyCountry"
  );
  return (
    <DashboardStyle>
      <Flex
        align={{ initial: "flex-start", md: "center" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "flex-start", md: "between" }}
      >
        <SectionHeader
          title={`Send Money for ${user?.data?.firstName}`}
          description={` `}
        />
      </Flex>
      &nbsp;
      <Box>
        <Flex gap={"6"}>
          {steps.map((itm) => {
            return (
              <Flex align={"center"} gap={"2"}>
                <div
                  style={{
                    borderRadius: "50%",
                    border:
                      step === itm.step
                        ? "1px solid #FF7434"
                        : "1px solid #A1A9B8",
                    color: step === itm.step ? "#FF7434" : "#A1A9B8",
                    width: "30px",
                    height: "30px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  {itm.step}
                </div>
                <div
                  style={{ color: step === itm.step ? "#FF7434" : "#A1A9B8" }}
                >
                  {itm.title}
                </div>
              </Flex>
            );
          })}
        </Flex>
        <br />
        {step === 1 && (
          <Step1
            formik={formik}
            id={id}
            beneficiaries={beneficiaries}
            getBeneficiaries={getBeneficiaries}
          />
        )}

        {/* STEP2 */}

        {step === 2 && (
          <Step2
            formik={formik}
            data={{
              beneficiaries: beneficiaries,
              user: user,
            }}
            list={list}
            setTransactionData={setTransactionData}
            transactionData={transactionData}
            rate={rate}
            refetch={refetch}
            user_data={user_data}
          />
        )}

        {step === 3 && (
          <Step3
            formik={formik}
            data={{
              beneficiaries,
              user,
              successData: data,
            }}
            transactionData={transactionData}
          />
        )}

        <DividerHorizontal />

        {data?.data ? (
          <Flex
            gap="3"
            mt="4"
            justify="end"
            maxWidth={"340px"}
            width={"100%"}
            ml={"auto"}
          >
            <AppButton
              width="50%"
              placeholder={"Back to Dashboard"}
              outline
              to={"/dashboard"}
            />
          </Flex>
        ) : (
          <Flex
            gap="3"
            mt="4"
            justify="end"
            maxWidth={"340px"}
            width={"100%"}
            ml={"auto"}
          >
            {step > 1 && (
              <AppButton
                placeholder="Previous"
                loading={false}
                disabled={isPending}
                onClick={() => {
                  setStep((curr) => curr - 1);
                }}
                secondary
                width="50%"
              />
            )}
            <AppButton
              width="50%"
              placeholder={step === 3 ? "Proceed to Payment" : "Continue"}
              loading={isPending}
              disabled={isPending || step === 2 ? isFetching : ""}
              onClick={() => {
                formik.handleSubmit();
              }}
            />
          </Flex>
        )}
      </Box>
    </DashboardStyle>
  );
}

const Step1 = ({ formik, beneficiaries, getBeneficiaries }) => {
  const { id } = useParams();
  const [modal, setModal] = useState(false);
  return (
    <div>
      {modal && (
        <AddBeneficiaryModal
          open={modal}
          setOpen={setModal}
          finished={getBeneficiaries}
          customerId={id}
        />
      )}
      <SectionHeader
        fontSize="20px"
        title="Beneficiary"
        description={`Please Select a beneficiary`}
      />
      <DividerHorizontal />
      <Grid columns="4" gap={"4"}>
        <BeneficiarySelect
          formik={formik}
          options={beneficiaries?.data}
          name="userBeneficiaryId"
          getBeneficiaries={getBeneficiaries}
        />
        <AddBeneficiaryButtton onClick={() => setModal(true)} />
      </Grid>
    </div>
  );
};

const Step2 = ({
  formik,
  data,
  setTransactionData,
  transactionData,
  rate,
  refetch,
  user_data,
  list,
}) => {
  console.log(data, "datadatadata");
  const user = data?.user?.data;
  const receiver = data?.beneficiaries?.data?.find(
    (itm) => itm?.id === formik?.values?.userBeneficiaryId
  );

  const getCurrentCurrencyCountry = list?.data?.find(
    (itm) => itm?.code === user?.country?.currencyCode
  );

  const fromId = list?.data?.find(
    (itm) => formik?.values?.fromCurrencyId === itm?.id
  );

  const toId = list?.data?.find(
    (itm) => itm?.id === formik?.values?.toCurrencyId
  );

  useEffect(() => {
    if (formik.values?.fromCurrencyId) {
      return;
    } else {
      formik.setFieldValue("fromCurrencyId", getCurrentCurrencyCountry?.id);
    }
  }, [list?.data || user]);

  const rateData = rate?.data;

  useEffect(() => {
    setTransactionData({
      ...transactionData,
      fromId: fromId?.id ? fromId : getCurrentCurrencyCountry,
      toId: toId,
    });
  }, [toId]);
  useEffect(() => {
    setTransactionData({
      ...transactionData,
      fromId: fromId,
      toId: toId,
    });
  }, [fromId]);

  useEffect(() => {
    refetch(
      user_data?.role?.name === "Agent"
        ? {
            agentId: user_data?.userId,
            fromCurrencyId: formik?.values?.fromCurrencyId,
            toCurrencyId: formik?.values?.toCurrencyId,
            fromAmount: formik?.values?.amount,
            toAmount: 0,
            roleId: 6,
            userId: user?.userId,
          }
        : {
            fromCurrencyId: formik?.values?.fromCurrencyId,
            toCurrencyId: formik?.values?.toCurrencyId,
            fromAmount: formik?.values?.amount,
            toAmount: 0,
            roleId: 6,
            userId: user?.userId,
          }
    );
    setTransactionData({
      ...transactionData,
      ...rateData,
      ...formik?.values,
      receiver: receiver,
      fromId: fromId,
      toId: toId,
    });
  }, [formik?.values]);

  return (
    <div>
      <SectionHeader
        fontSize="20px"
        title="Send Money"
        description={`How much do you want to send`}
      />
      <DividerHorizontal />

      <SendMoneyHeader data={user} receiver={receiver} />
      <DividerHorizontal />

      <Grid columns="3" gap={"8"}>
        <PaymentChannelSelect
          label={"How will the sender pay"}
          name="paymentChannelId"
          formik={formik}
          onChange={(e) => {
            setTransactionData({
              ...transactionData,
              paymentChannel: e,
            });
          }}
        />
        <PayoutChannelSelect
          label={"Collection Type"}
          name="payoutChannelId"
          onChange={(e) => {
            setTransactionData({
              ...transactionData,
              payoutChannel: e,
            });
          }}
          formik={formik}
        />
        <PurposeSelect
          label={"Select Purpose of Transfer"}
          name="purpose"
          formik={formik}
          optionValue={"label"}
        />
      </Grid>

      <DividerHorizontal />

      <Grid columns="3" gap={"8"}>
        <FormInputLabel
          label={"Sending Currency"}
          name="amount"
          formik={formik}
        >
          <Grid columns={{ initial: "1", md: "1.4fr 2fr" }} align={"end"}>
            <CurrencySelect
              cutBorder
              width="100%"
              name="fromCurrencyId"
              setCurrency={(e) => {
                setTransactionData({
                  ...transactionData,
                  fromId: e,
                });
              }}
              formik={formik}
              optionValue="id"
            />
            <FormInputNumber
              cutBorder
              showError={false}
              name="amount"
              formik={formik}
              amount
            />
          </Grid>
        </FormInputLabel>
        <FormInputLabel label={"Receiving Currency"} formik={formik}>
          <Grid columns={{ initial: "1", md: "1.4fr 2fr" }} align={"end"}>
            <CurrencySelect
              cutBorder
              width="100%"
              name="toCurrencyId"
              formik={formik}
              optionValue="id"
            />
            <FormInputNumber
              cutBorder
              showError={false}
              value={rateData?.toComputedToAmount || "0"}
              formik={formik}
              disabled={true}
              amount
            />
          </Grid>
        </FormInputLabel>
      </Grid>

      <Grid columns="3" gap={"8"}>
        <div style={{ gridColumn: "span 2" }}>
          <FormTextArea
            label={"Transaction Notes"}
            formik={formik}
            name={"note"}
            height={"140px"}
          />
        </div>
      </Grid>

      <DividerHorizontal />
      <Grid columns="2" gap={"8"}>
        {rateData?.transitionFee === 0 ? (
          <FormInputNumber
            label={"Transfer Fee"}
            showError={false}
            value={"0"}
            disabled={true}
            amount
          />
        ) : (
          <FormInputNumber
            label={"Transfer Fee"}
            showError={false}
            value={rateData?.transitionFee || "0"}
            disabled={true}
            amount
          />
        )}
        <FormInputNumber
          label={"Exchange Rate"}
          showError={false}
          value={rateData?.conversionRate || "0"}
          disabled={true}
          amount
        />
      </Grid>
      <DividerHorizontal />
      <div
        style={{
          display: "grid",
          placeItems: "center",
        }}
      >
        <Box
          style={{
            textAlign: "center",
            color: "#5A6376",
            justifyContent: "space-between",
            width: "400px",
          }}
        >
          <p
            style={{
              color: "#5A6376",
              fontSize: "14px",
            }}
          >
            Total Amount you{"'"}ll be sending
          </p>
          <h1 style={{ margin: 0 }}>
            {FormatCurrency(rateData?.totalAmountToPay || 0, fromId?.code)}
          </h1>
        </Box>
      </div>
    </div>
  );
};

const Step3 = ({ formik, data, transactionData }) => {
  const user = data?.user?.data;
  const receiver = data?.beneficiaries?.data?.find(
    (itm) => itm?.id === formik?.values?.userBeneficiaryId
  );

  const successData = data?.successData;

  console.log(successData, "successData");

  return (
    <div>
      <SectionHeader
        fontSize="20px"
        title="Review Transfer"
        description={`Review your transaction`}
      />

      <DividerHorizontal />
      <SendMoneyHeader data={user} receiver={receiver} />
      <DividerHorizontal />

      <Grid columns="2" gap={"8"}>
        <BoxList
          list={[
            {
              title: `Amount Sent in ${transactionData?.fromId?.code}`,
              value: FormatCurrency(
                transactionData?.amount,
                transactionData?.fromId?.code
              ),
            },
            {
              title: "Exchange Rate",
              value: FormatCurrency(
                transactionData?.conversionRate,
                transactionData?.toId?.code
              ),
            },
            {
              title: "Amount in Foreign Currency",
              value: FormatCurrency(
                transactionData?.toComputedToAmount,
                transactionData?.toId?.code
              ),
            },
            {
              title: `Transfer Fees in ${transactionData?.fromId?.code}`,
              value: FormatCurrency(
                transactionData?.transitionFee,
                transactionData?.fromId?.code
              ),
            },
          ]}
        />
        <BoxList
          list={[
            {
              title: "Payment Type",
              value: transactionData?.paymentChannel?.label,
            },
            {
              title: "Collection Type",
              value: transactionData?.payoutChannel?.label,
            },
            {
              title: "Purpose",
              value: transactionData?.purpose,
            },
            {
              title: "Note",
              value: transactionData?.note,
            },
          ]}
        />
      </Grid>
      <DividerHorizontal />

      {successData?.data && (
        <>
          <div
            style={{
              textAlign: "center",
            }}
          >
            <QRCode
              value={`${window.location.origin}/confirm-transaction?tid=${successData?.data}`}
            />
            <div
              style={{
                cursor: "pointer",
                fontSize: "18px",
                textAlign: "center",
                marginTop: "10px",
              }}
              onClick={() => {
                navigator.clipboard.writeText(
                  `${window.location.origin}/confirm-transaction?tid=${successData?.data}`
                );
                Notification.success({
                  title: "Payment Link Copied!",
                  content: "You can now share payment link with user",
                });
              }}
            >
              <span
                style={{
                  color: "#333B4A",
                }}
              >
                Scan barcode or click to copy payment link
              </span>
              &nbsp; &nbsp;
              <Copy size={16} color="#475467" />
            </div>
          </div>
          <DividerHorizontal />
        </>
      )}

      <BoxListHorizontal
        list={[
          {
            title: "Bank Name",
            value: receiver?.beneficiaryBank?.bankName,
          },
          {
            title: "Account Name",
            value: receiver?.beneficiaryName,
          },
          {
            title: "Account Number",
            value: receiver?.beneficiaryBank?.accountNumber,
          },
          {
            title: "Total amount you will be paying",
            value: FormatCurrency(
              transactionData?.totalAmountToPay,
              transactionData?.fromId?.code
            ),
          },
        ]}
      />
    </div>
  );
};

const DashboardStyle = styled.div`
  color: #000;
`;
