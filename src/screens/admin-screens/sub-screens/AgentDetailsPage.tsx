import BackBtn from "@/components/bits/BackBtn";
import FilterTab from "@/components/bits/FilterTab";
import AuditTab from "@/components/customer-bits/AuditTab";
import BeneficiaryTab from "@/components/customer-bits/BeneficiaryTab";
import CustomerDetailsHeader from "@/components/customer-bits/CustomerDetailsHeader";
import CustomerTab from "@/components/customer-bits/CustomerTab";
import DetailsOverviewTab from "@/components/customer-bits/DetailsOverviewTab";
import IDDocumentsTab from "@/components/customer-bits/IDDocumentsTab";
import TransactionTab from "@/components/customer-bits/TransactionTab";
import WalletTab from "@/components/customer-bits/WalletTab";
import { ApiServiceAdmin } from "@/service/admin-services";
import { Grid } from "@radix-ui/themes";
import { useQuery } from "@tanstack/react-query";
import React, { FC } from "react";
import { useParams, useSearchParams } from "react-router-dom";

const AgentDetailsPage: FC = () => {
  const [query] = useSearchParams();
  const { id } = useParams(); // Extract the ID from the route

  const active = query.get("tab");
  const { data, refetch } = useQuery({
    queryKey: ["GetCustomerQuery"],
    queryFn: () => ApiServiceAdmin.GetCustomerQuery(id),
  });
  const details = data?.data;

  return (
    <div>
      <BackBtn />
      <CustomerDetailsHeader data={details} />
      <FilterTab
        tab={[
          {
            tab: "overview",
            name: "Overview",
          },
          {
            tab: "id",
            name: "ID Documents",
          },
          {
            tab: "transfer",
            name: "Transfer List",
          },
          {
            tab: "customer",
            name: "Customer List",
          },
          {
            tab: "audit",
            name: "Audit Logs",
          },
          {
            tab: "wallet",
            name: "Wallets",
          },
        ]}
      />
      <br />
      {active === "overview" && <DetailsOverviewTab data={details} />}
      {active === "id" && <IDDocumentsTab data={details} />}
      {active === "transfer" && <TransactionTab data={details} />}
      {active === "customer" && <CustomerTab data={details} />}
      {active === "audit" && <AuditTab data={details} />}
      {active === "wallet" && (
        <WalletTab data={details} refetch={refetch} />
      )}{" "}
    </div>
  );
};

export default AgentDetailsPage;
