/* eslint-disable react-hooks/exhaustive-deps */
import AppButton from "@/components/bits/AppButton";
import Box from "@/components/bits/Box";
import CurrencySelect from "@/components/bits/CurrencySelect";
//import MiniNavBar from "@/components/bits/MiniNavBar";
import SectionHeader from "@/components/bits/SectionHeader";
import DividerHorizontal from "@/components/DividerHorizontal";
import { ApiServiceAdmin } from "@/service/admin-services";
//import { handleSetItem } from "@/lib/utils";
import { Flex, Grid } from "@radix-ui/themes";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useFormik } from "formik";
import { useEffect, useState } from "react";
import styled from "styled-components";
import { useLocation, useParams } from "react-router-dom";
import { SendMoneySchema } from "@/Schema";
import BeneficiarySelect from "@/components/bits/BeneficiarySelect";
import SendMoneyHeader from "@/components/customer-bits/SendMoneyHeader";
import PaymentChannelSelect from "@/components/bits/PaymentChannelSelect";
import PayoutChannelSelect from "@/components/bits/PayoutChannelSelect";
import PurposeSelect from "@/components/bits/PurposeSelect";
import FormInputLabel from "@/components/bits/FormInputLabel";
import FormInputNumber from "@/components/bits/FormInputNumber";
import FormTextArea from "@/components/bits/FormTextArea";
import { FormatCurrency } from "@/lib/utils";
import BoxList from "@/components/BoxList";
import BoxListHorizontal from "@/components/BoxListHorizontal";
import { Notification } from "@arco-design/web-react";
import QRCode from "react-qr-code";
import { Copy } from "lucide-react";
import React from "react";
import { CurrencyBadge } from "@/components/bits/CurrencyBadge";
import { StatusBadge } from "@/components/bits/StatusBadge";

export default function TransactionDetailsPage() {
  const { id } = useParams();

  const location = useLocation();

  const transactionData = location.state;
  console.log(transactionData, "transactionData");

  return (
    <DashboardStyle>
      <Flex
        align={{ initial: "start", md: "center" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "start", md: "between" }}
      >
        <SectionHeader
          title={`Transaction Details - ${id}`}
          description={` `}
          showBackBtn={undefined}
          align={undefined}
        />
      </Flex>
      <Box style={undefined}>
        <div>
          <DividerHorizontal />
          <Grid columns={"2"}>
            <div>
              <Flex
                style={{
                  marginBlock: "1rem",
                  gap: "1rem",
                }}
              >
                <span style={{ color: "#909090" }}>Sender Name:</span>
                <p>{transactionData?.senderName}</p>
              </Flex>
              <Flex
                style={{
                  marginBlock: "1rem",
                  gap: "1rem",
                }}
              >
                <span style={{ color: "#909090" }}>Sender User ID:</span>
                <p>{transactionData?.userId}</p>
              </Flex>
              <Flex
                style={{
                  marginBlock: "1rem",
                  gap: "1rem",
                }}
              >
                <span style={{ color: "#909090" }}>Transaction Source:</span>
                <p>{transactionData?.transactionSource}</p>
              </Flex>
            </div>

            <div>
              <Flex
                style={{
                  marginBlock: "1rem",
                  gap: "1rem",
                }}
              >
                <span style={{ color: "#909090" }}>Sender Address:</span>
                <p>{transactionData?.senderAddress}</p>
              </Flex>
              <Flex
                style={{
                  marginBlock: "1rem",
                  gap: "1rem",
                }}
              >
                <span style={{ color: "#909090" }}>Sender's Country:</span>
                <CurrencyBadge
                  currency={transactionData?.senderCurrency}
                  name={transactionData?.senderCurrency}
                />
              </Flex>
              <Flex
                style={{
                  marginBlock: "1rem",
                  gap: "1rem",
                }}
              >
                <span style={{ color: "#909090" }}>Payment Date:</span>
                <p>{transactionData?.paymentDate}</p>
              </Flex>
            </div>
          </Grid>
          <DividerHorizontal />

          <Grid columns="2" gap={"8"}>
            <BoxList
              list={[
                {
                  title: `Amount Sent in ${transactionData?.senderCurrency}`,
                  value: FormatCurrency(
                    transactionData?.paymentAmount,
                    transactionData?.senderCurrency
                  ),
                },
                {
                  title: "Exchange Rate",
                  value: FormatCurrency(
                    transactionData?.rate,
                    transactionData?.beneficiaryCurrency
                  ),
                },
                {
                  title: "Amount in Foreign Currency",
                  value: FormatCurrency(
                    transactionData?.receivedAmount,
                    transactionData?.beneficiaryCurrency
                  ),
                },
                {
                  title: `Transfer Fees in ${transactionData?.senderCurrency}`,
                  value: FormatCurrency(
                    transactionData?.transitionFee,
                    transactionData?.senderCurrency
                  ),
                },
                {
                  title: "Beneficiary ID",
                  value: transactionData?.beneficiaryId,
                },
              ]}
            />
            <BoxList
              list={[
                {
                  title: "Payment Type",
                  value: transactionData?.paymentType,
                },
                {
                  title: "Collection Type",
                  value: transactionData?.collectionType,
                },
                {
                  title: "Payment Status",
                  value: (
                    <StatusBadge
                      variant="other"
                      status={transactionData?.paymentStatus}
                    />
                  ),
                },
                {
                  title: "Collection Status",
                  value: (
                    <StatusBadge
                      variant="other"
                      status={transactionData?.collectStatus}
                    />
                  ),
                },
                {
                  title: "Note",
                  value: transactionData?.transactionNote,
                },
              ]}
            />
          </Grid>
          <DividerHorizontal />

          <>
            <div
              style={{
                textAlign: "center",
              }}
            >
              <QRCode
                value={`${window.location.origin}/confirm-transaction?tid=${transactionData?.paymentRef}`}
              />
              <div
                style={{
                  cursor: "pointer",
                  fontSize: "18px",
                  textAlign: "center",
                  marginTop: "10px",
                }}
                onClick={() => {
                  navigator.clipboard.writeText(
                    `${window.location.origin}/confirm-transaction?tid=${transactionData?.paymentRef}`
                  );
                  Notification.success({
                    title: "Payment Link Copied!",
                    content: "You can now share payment link with user",
                  });
                }}
              >
                <span
                  style={{
                    color: "#333B4A",
                  }}
                >
                  Scan barcode or click to copy payment link
                </span>
                &nbsp; &nbsp;
                <Copy size={16} color="#475467" />
              </div>
            </div>
            <DividerHorizontal />
          </>

          <BoxListHorizontal
            list={[
              {
                title: "Bank Name",
                value:
                  transactionData?.userBeneficiary?.beneficiaryBank?.bankName,
              },
              {
                title: "Account Name",
                value: transactionData?.userBeneficiary?.beneficiaryName,
              },
              {
                title: "Account Number",
                value:
                  transactionData?.userBeneficiary?.beneficiaryBank
                    ?.accountNumber,
              },
              {
                title: "Total amount paid",
                value: FormatCurrency(
                  transactionData?.paymentAmount,
                  transactionData?.senderCurrency
                ),
              },
            ]}
          />
        </div>
      </Box>
    </DashboardStyle>
  );
}

const DashboardStyle = styled.div`
  color: #000;
`;
