/* eslint-disable react-hooks/exhaustive-deps */
import * as Yu<PERSON> from "yup";
import AppButton from "@/components/bits/AppButton";
import Box from "@/components/bits/Box";
import FormInput from "@/components/bits/FormInput";
//import MiniNavBar from "@/components/bits/MiniNavBar";
import SectionHeader from "@/components/bits/SectionHeader";
import DividerHorizontal from "@/components/DividerHorizontal";
import { ApiServiceAdmin } from "@/service/admin-services";
//import { handleSetItem } from "@/lib/utils";
import { Flex, Grid } from "@radix-ui/themes";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useFormik } from "formik";
import styled from "styled-components";
import { useLocation, useNavigate } from "react-router-dom";
import FormInputNumber from "@/components/bits/FormInputNumber";
import { useEffect } from "react";
import MainSelect from "@/components/bits/MainSelect";
//import FormInputPassword from "@/components/bits/FormInputPassword";
import { useAuth } from "@/context/global.context";

export default function EditCustomerPage() {
  const { user_id } = useAuth();
  const location = useLocation();

  const data = location.state;

  console.log(data, "data");
  const navigate = useNavigate();

  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.UpdateUserDetailsMutation,
    onSuccess: () => {
      navigate(-1);
    },
    onError: () => {
      return;
    },
  });

  const formik = useFormik({
    initialValues: {
      firstName: data?.firstName || "",
      surName: data?.surName || "",
      email: data?.email || "",
      password: "",
      dob: data?.dob || "",
      gender: data?.gender?.toUpperCase() || "",
      phone: data?.phone || "",
      address: data?.address || "",
      postcode: data?.postcode || "",
      accountType: data?.accountType || "",
      country: {
        id: data?.country?.id || "",
      },
      city: {
        id: data?.city?.id || "",
      },
      role: {
        id: data?.role?.id || "",
      },
      employmentStatusId: data?.employmentStatusId || "",
      profession: {
        id: data?.profession?.id || "",
      },
      companyName: data?.companyName || "",
      onboardingSource: data?.onboardingSource || "Web",
      agentId: user_id,
      userId: data?.userId,
    },
    validationSchema: Yup.object().shape({
      country: Yup.object({
        id: Yup.string().required("Country is required"),
      }),
      city: Yup.object({
        id: Yup.string().required("City is required"),
      }),
      role: Yup.object({
        id: Yup.string().required("Role is required"),
      }),
      /*   profession: Yup.object({
        id: Yup.string().required("Profession is required"),
      }), */
      accountType: Yup.string().required("Account Type is required"),
      firstName: Yup.string().required("First Name is required"),
      surName: Yup.string().required("Last Name is required"),
      email: Yup.string().required("Email is required"),
      /*   password: Yup.string()
        .required("Password is required")
        ?.min(8, "Must be 8 characters or more")
        ?.matches(/[a-z]+/, "One lowercase character")
        ?.matches(/[A-Z]+/, "One uppercase character")
        ?.matches(/[@$!%*#?&]+/, "One special character")
        ?.matches(/\d+/, "One number"),
      confirmPassword: Yup.string()
        .required("Confirm Password")
        .oneOf([Yup.ref("password")], "Passwords must match"), */
      gender: Yup.string().required("Gender is required"),
      phone: Yup.string().required("Phone is required"),
      address: Yup.string().required("Address is required"),
      //companyName: Yup.string().required("Company is required"),
      postcode: Yup.string().required("Postcode is required"),
      employmentStatusId: Yup.string().required(
        "Employment Status is required"
      ),
    }),
    onSubmit: (values) => {
      const omitKey = (obj, keyToRemove) => {
        // eslint-disable-next-line no-unused-vars
        const { [keyToRemove]: _, ...rest } = obj;
        return rest;
      };
      mutate(omitKey(values, "confirmPassword"));
    },
  });
  console.log(formik.values, "values");
  return (
    <DashboardStyle>
      <Flex
        align={{ initial: "flex-start", md: "center" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "flex-start", md: "between" }}
      >
        <SectionHeader title="Edit User" description={` `} />
      </Flex>
      &nbsp;
      <Box>
        <Step1 formik={formik} userId={data?.userId} />

        <DividerHorizontal />

        <Flex
          gap="3"
          mt="4"
          justify="end"
          maxWidth={"500px"}
          width={"100%"}
          ml={"auto"}
        >
          <AppButton
            placeholder="Back"
            loading={false}
            disabled={isPending}
            onClick={() => {
              navigate(-1);
            }}
            secondary
            width="30%"
          />
          <AppButton
            width="70%"
            placeholder="Submit"
            loading={isPending}
            disabled={isPending}
            onClick={() => {
              formik.handleSubmit();
            }}
          />
        </Flex>
      </Box>
    </DashboardStyle>
  );
}

const Step1 = ({ formik }) => {
  const { data: list1 } = useQuery({
    queryKey: ["GetCountriesQueryList"],
    queryFn: () => ApiServiceAdmin.GetCountriesQuery(),
  });

  const { data: list2, refetch } = useQuery({
    queryKey: ["GetCitiesQueryList"],
    queryFn: () => ApiServiceAdmin.GetCitiesQuery(formik?.values?.country?.id),
    enabled: formik?.values?.country?.id ? true : false,
  });

  useEffect(() => {
    refetch(formik?.values?.country?.id);
  }, [formik?.values?.country?.id]);

  const countries =
    list1 &&
    list1?.data?.map((itm) => {
      return {
        ...itm,
        label: itm?.name,
        value: itm?.name,
      };
    });

  const cities =
    list2 &&
    list2?.data?.map((itm) => {
      return {
        ...itm,
        label: itm?.name,
        value: itm?.name,
      };
    });

  const { data: list3 } = useQuery({
    queryKey: ["GetProfessionQueryList"],
    queryFn: () => ApiServiceAdmin.GetProfessionQuery(),
  });
  const professions =
    list3 &&
    list3?.data?.map((itm) => {
      return {
        ...itm,
        label: itm?.name,
        value: itm?.name,
      };
    });

  /*   const { data: list4 } = useQuery({
    queryKey: ["GetUserRolesQueryList"],
    queryFn: () => ApiServiceAdmin.GetUserRolesQuery(),
  });
  const roles =
    list4 &&
    list4?.data?.map((itm) => {
      return {
        ...itm,
        label: itm?.name,
        value: itm?.name,
      };
    });
 */
  const { data: list5 } = useQuery({
    queryKey: ["GetEmploymentStatusQuery"],
    queryFn: () => ApiServiceAdmin.GetEmploymentStatusQuery(),
  });
  const statuses =
    list5 &&
    list5?.data?.map((itm) => {
      return {
        ...itm,
        label: itm?.name,
        value: itm?.name,
      };
    });
  return (
    <div>
      <SectionHeader
        fontSize="20px"
        title="User Details"
        description={`Please fill in with necessary information`}
      />
      <DividerHorizontal />
      <Grid columns="2" gap={"8"}>
        <FormInput label={"First Name"} name="firstName" formik={formik} />
        <FormInput label={"Last Name"} name="surName" formik={formik} />
        <FormInput label={"Email Address"} name="email" formik={formik} />
        <FormInputNumber label={"Phone Number"} name="phone" formik={formik} />
      </Grid>
      <DividerHorizontal />

      <SectionHeader
        fontSize="20px"
        title="Address Details"
        description={`Please fill in with necessary information`}
      />
      <DividerHorizontal />
      <Grid columns="2" gap={"8"}>
        <MainSelect
          label={"Country"}
          width="100%"
          name="country[id]"
          formik={formik}
          options={countries}
          optionValue="id"
        />
        <MainSelect
          label={"City"}
          width="100%"
          name="city[id]"
          formik={formik}
          optionValue="id"
          options={cities || []}
        />

        <FormInput label={"Post Code"} name="postcode" formik={formik} />
        <FormInput label={"Address"} name="address" formik={formik} />
      </Grid>
      <DividerHorizontal />

      <SectionHeader
        fontSize="20px"
        title="Profession Details"
        description={`Please fill in with necessary information`}
      />
      <DividerHorizontal />
      <Grid columns="2" gap={"8"}>
        <MainSelect
          label={"Employement Status"}
          width="100%"
          name="employmentStatusId"
          optionValue="id"
          formik={formik}
          options={statuses}
        />
        <MainSelect
          label={"Profession"}
          width="100%"
          name="profession[id]"
          formik={formik}
          optionValue="id"
          options={professions || []}
        />

        <FormInput label={"Company Name"} name="companyName" formik={formik} />
        <MainSelect
          label={"Account Type"}
          width="100%"
          name="accountType"
          formik={formik}
          options={[
            { label: "Individual", value: 1 },
            { label: "Business", value: 2 },
          ]}
        />
      </Grid>
      <DividerHorizontal />

      <SectionHeader
        fontSize="20px"
        title="Account Details"
        description={`Please fill in with necessary information`}
      />
      <DividerHorizontal />
      <Grid columns="1" gap={"8"}>
        {/*     <MainSelect
          label={"Role"}
          width="100%"
          name="role[id]"
          optionValue="id"
          formik={formik}
          options={roles || []}
        /> */}
        <MainSelect
          label={"Gender"}
          width="100%"
          name="gender"
          formik={formik}
          options={[
            { label: "Male", value: "MALE" },
            { label: "Female", value: "FEMALE" },
          ]}
        />

        {/*   <FormInputPassword label={"Password"} name="password" formik={formik} />
        <FormInputPassword
          label={"Confirm Password"}
          name="confirmPassword"
          formik={formik}
        /> */}
      </Grid>
      <DividerHorizontal />
    </div>
  );
};

const DashboardStyle = styled.div`
  color: #000;
`;
