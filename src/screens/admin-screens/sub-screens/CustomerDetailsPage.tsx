import DetailsOverviewTab from "@/components/customer-bits/DetailsOverviewTab";
import BackBtn from "@/components/bits/BackBtn";
import FilterTab from "@/components/bits/FilterTab";
import CustomerDetailsHeader from "@/components/customer-bits/CustomerDetailsHeader";
import { Grid } from "@radix-ui/themes";
import React, { FC } from "react";
import { useParams, useSearchParams } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { ApiServiceAdmin } from "@/service/admin-services";
import IDDocumentsTab from "@/components/customer-bits/IDDocumentsTab";
import TransactionTab from "@/components/customer-bits/TransactionTab";
import BeneficiaryTab from "@/components/customer-bits/BeneficiaryTab";
import AuditTab from "@/components/customer-bits/AuditTab";
import WalletTab from "@/components/customer-bits/WalletTab";
import { Skeleton } from "@arco-design/web-react";
import { useAuth } from "@/context/global.context";

const CustomerDetailsPage: FC = () => {
  const [query] = useSearchParams();
  const { user_data } = useAuth();
  const { id } = useParams(); // Extract the ID from the route

  const active = query.get("tab");
  const { data, refetch, isLoading, isFetching } = useQuery({
    queryKey: ["GetCustomerQuery"],
    queryFn: () => ApiServiceAdmin.GetCustomerQuery(id),
  });

  const details = data?.data;
  return isLoading || isFetching ? (
    <div>
      <Skeleton />
      <br />
      <Skeleton />
      <br />
      <Skeleton />
      <br />
      <Skeleton />
      <br />
      <Skeleton />
      <br />
      <Skeleton />
      <br />
      <Skeleton />
      <br />
      <Skeleton />
      <br />
      <Skeleton />
      <br />
      <Skeleton />
      <br />
      <Skeleton />
    </div>
  ) : (
    <div>
      <BackBtn />
      <CustomerDetailsHeader data={details} />
      <FilterTab
        tab={
          user_data?.userId !== 0
            ? [
                {
                  tab: "overview",
                  name: "Overview",
                },
                {
                  tab: "id",
                  name: "ID Documents",
                },
                {
                  tab: "transfer",
                  name: "Transfer List",
                },
                {
                  tab: "beneciaries",
                  name: "Beneficiary List",
                },
                {
                  tab: "audit",
                  name: "Audit Logs",
                },
                {
                  tab: "wallet",
                  name: "Wallets",
                },
              ]
            : [
                {
                  tab: "overview",
                  name: "Overview",
                },
                {
                  tab: "id",
                  name: "ID Documents",
                },
                {
                  tab: "transfer",
                  name: "Transfer List",
                },
                {
                  tab: "beneciaries",
                  name: "Beneficiary List",
                },
                {
                  tab: "audit",
                  name: "Audit Logs",
                },
              ]
        }
      />
      <br />
      {active === "overview" && <DetailsOverviewTab data={details} />}
      {active === "id" && <IDDocumentsTab data={details} refetch={refetch} />}
      {active === "transfer" && <TransactionTab data={details} />}
      {active === "beneciaries" && <BeneficiaryTab data={details} />}
      {active === "audit" && <AuditTab data={details} />}
      {user_data?.userId === 0 && active === "wallet" && (
        <WalletTab data={details} refetch={refetch} />
      )}
    </div>
  );
};

export default CustomerDetailsPage;
