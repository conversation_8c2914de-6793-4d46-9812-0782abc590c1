import AppButton from "@/components/bits/AppButton";
import Box from "@/components/bits/Box";
import CurrencySelect from "@/components/bits/CurrencySelect";
import FormInput from "@/components/bits/FormInput";
import FormInputLabel from "@/components/bits/FormInputLabel";
import FormInputNumber from "@/components/bits/FormInputNumber";
import MainSelect from "@/components/bits/MainSelect";
//import MiniNavBar from "@/components/bits/MiniNavBar";
import SectionHeader from "@/components/bits/SectionHeader";
import DividerHorizontal from "@/components/DividerHorizontal";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useAuth } from "@/context/global.context";
//import { handleSetItem } from "@/lib/utils";
import { Flex, Grid } from "@radix-ui/themes";
import { useMutation } from "@tanstack/react-query";
import { useFormik } from "formik";
import { useState } from "react";
import styled from "styled-components";
import { useNavigate } from "react-router-dom";
import SwitchInput from "@/components/bits/SwitchInput";
import { CreateCurrencyRateMetadataSchema } from "@/Schema";

export default function CreateCurrencyRateMetadataPage() {
  const { user_id } = useAuth();

  const [step, setStep] = useState(1);

  const steps = [
    {
      title: "Category Details",
      step: 1,
    },
    {
      title: "Limits",
      step: 2,
    },
    {
      title: "KYC Threshold & Partners",
      step: 3,
    },
  ];
  const navigate = useNavigate();

  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.AddNewCurrencyRateMetadataMutation,
    onSuccess: () => {
      navigate(-1);
    },
    onError: () => {
      return;
    },
  });

  const formik = useFormik({
    initialValues: {
      updatedBy: user_id,
      currency: {
        id: "",
      },
      role: {
        id: "",
      },
      name: "",
      description: "",
      dailyLimit: "",
      weeklyLimit: "",
      monthlyLimit: "",
      annualLimit: "",
      proofOfPaymentThresholdAmount: "",
      sourceOfFundThresholdAmount: "",
      transferBonusThreshold: "",
      minTransferLimit: "",
      allowBelowMinimum: false,
      belowMinimumChargeType: "",
      belowMinimumCharges: "",
      allowTransactionOutsideCountryOfResidence: "",
      maxTransferLimit: "",
      allowAboveMaximum: false,
      aboveMaximumChargeType: "",
      aboveMaximumLimitCharges: "",
      autopayout: false,
      kycThreshold: "",
      bonusRateValue: "",
      allowTransferPreKCY: false,

      //"proofOfPaymentThresholdAmount": 4000,
      //"sourceOfFundThresholdAmount": 7000,

      // proofOfAddressAmountThreshold: "",
      //sourceOfFundAmountThreshold: "",
    },
    validationSchema: CreateCurrencyRateMetadataSchema(step),
    onSubmit: (values) => {
      if (step === 3) {
        mutate(values);
      } else {
        setStep((curr) => curr + 1);
      }
    },
  });
  console.log(formik.values, "values");
  return (
    <DashboardStyle>
      <Flex
        align={{ initial: "flex-start", md: "center" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "flex-start", md: "between" }}
      >
        <SectionHeader
          title="Create Currency Rate Metadata"
          description={` `}
        />
      </Flex>
      &nbsp;
      <Box>
        <Flex gap={"6"}>
          {steps.map((itm) => {
            return (
              <Flex align={"center"} gap={"2"}>
                <div
                  style={{
                    borderRadius: "50%",
                    border:
                      step === itm.step
                        ? "1px solid #FF7434"
                        : "1px solid #A1A9B8",
                    color: step === itm.step ? "#FF7434" : "#A1A9B8",
                    width: "30px",
                    height: "30px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  {itm.step}
                </div>
                <div
                  style={{ color: step === itm.step ? "#FF7434" : "#A1A9B8" }}
                >
                  {itm.title}
                </div>
              </Flex>
            );
          })}
        </Flex>
        <br />
        {step === 1 && <Step1 formik={formik} />}

        {/* STEP2 */}

        {step === 2 && <Step2 formik={formik} />}

        {step === 3 && <Step3 formik={formik} />}

        <DividerHorizontal />

        <Flex
          gap="3"
          mt="4"
          justify="end"
          maxWidth={"500px"}
          width={"100%"}
          ml={"auto"}
        >
          {step > 1 && (
            <AppButton
              placeholder="Back"
              loading={false}
              disabled={isPending}
              onClick={() => {
                setStep((curr) => curr - 1);
              }}
              secondary
              width="30%"
            />
          )}
          <AppButton
            width="70%"
            placeholder="Continue"
            loading={isPending}
            disabled={isPending}
            onClick={() => {
              formik.handleSubmit();
            }}
          />
        </Flex>
      </Box>
    </DashboardStyle>
  );
}

const Step1 = ({ formik }) => {
  return (
    <div>
      <SectionHeader
        fontSize="20px"
        title="Category Details"
        description={` Add basic information about the category`}
      />
      <DividerHorizontal />
      <Grid columns="2" gap={"8"}>
        <CurrencySelect
          label={"Currency"}
          width="100%"
          name="currency[id]"
          formik={formik}
          optionValue="id"
        />
        <MainSelect
          label={"User Type"}
          name="role[id]"
          formik={formik}
          options={[
            {
              label: "Agent",
              value: 5,
            },
            {
              label: "Customer",
              value: 6,
            },
          ]}
        />
        <FormInput label={"Name"} name="name" formik={formik} />
        <FormInput label={"Description"} name="description" formik={formik} />
      </Grid>
    </div>
  );
};

const Step2 = ({ formik }) => {
  return (
    <div>
      <SectionHeader
        fontSize="20px"
        title="Limits"
        description={`Set Limits`}
      />
      <DividerHorizontal />
      <Grid columns="3" gap={"8"}>
        <FormInputNumber
          label={"Minimum Transfer Limit"}
          name="minTransferLimit"
          formik={formik}
        />
        <SwitchInput
          label={
            <div>
              <b>Allow Below Minimum Transfer Limit</b>
              <p>
                All users in the category to transfer lower than the minimum
                category
              </p>
            </div>
          }
          formik={formik}
          name="allowBelowMinimum"
        />
        {formik.values.allowBelowMinimum ? (
          <FormInputLabel
            label={"Fee Charge*"}
            name="belowMinimumChargeType"
            formik={formik}
          >
            <Grid columns={{ initial: "1", md: "1.2fr 2fr" }} align={"end"}>
              <MainSelect
                cutBorder
                formik={formik}
                showError={false}
                placeholder="Type"
                name="belowMinimumChargeType"
                options={[
                  {
                    label: "Percentage",
                    value: "Percentage",
                  },
                  {
                    label: "Fixed",
                    value: "Fixed",
                  },
                ]}
              />
              <FormInputNumber
                cutBorder
                showError={false}
                name="belowMinimumCharges"
                formik={formik}
                percent={formik.values.belowMinimumChargeType === "Percentage"}
              />
            </Grid>
          </FormInputLabel>
        ) : (
          <div></div>
        )}

        <FormInputNumber
          label={"Maximum Transfer Limit"}
          name="maxTransferLimit"
          formik={formik}
        />
        <SwitchInput
          label={
            <div>
              <b>Allow Below Minimum Transfer Limit</b>
              <p>
                All users in the category to transfer lower than the minimum
                category
              </p>
            </div>
          }
          formik={formik}
          name="allowAboveMaximum"
        />
        {formik.values.allowAboveMaximum ? (
          <FormInputLabel
            label={"Fee Charge*"}
            name="aboveMaximumChargeType"
            formik={formik}
          >
            <Grid columns={{ initial: "1", md: "1.2fr 2fr" }} align={"end"}>
              <MainSelect
                cutBorder
                formik={formik}
                showError={false}
                placeholder="Type"
                name="aboveMaximumChargeType"
                options={[
                  {
                    label: "Percentage",
                    value: "Percentage",
                  },
                  {
                    label: "Fixed",
                    value: "Fixed",
                  },
                ]}
              />
              <FormInputNumber
                cutBorder
                showError={false}
                name="aboveMaximumLimitCharges"
                formik={formik}
                percent={formik.values.aboveMaximumChargeType === "Percentage"}
              />
            </Grid>
          </FormInputLabel>
        ) : (
          <div></div>
        )}
      </Grid>
      <DividerHorizontal />
      <SwitchInput
        label={<b>Auto Payout</b>}
        formik={formik}
        name="autopayout"
      />
      <DividerHorizontal />
      <Grid columns="2" gap={"8"}>
        <FormInputNumber
          label={"Daily Limit"}
          name="dailyLimit"
          formik={formik}
        />
        <FormInputNumber
          label={"Weekly Limit"}
          name="weeklyLimit"
          formik={formik}
        />
        <FormInputNumber
          label={"Monthly Limit"}
          name="monthlyLimit"
          formik={formik}
        />
        <FormInputNumber
          label={"Annual Limit"}
          name="annualLimit"
          formik={formik}
        />
        <FormInputNumber
          label={"Transfer Bonus Threshold"}
          name="transferBonusThreshold"
          formik={formik}
        />
        <FormInputNumber
          label={"Bonus Rate Value"}
          name="bonusRateValue"
          formik={formik}
        />
      </Grid>
    </div>
  );
};

const Step3 = ({ formik }) => {
  return (
    <div>
      <SectionHeader
        fontSize="20px"
        title="KYC Threshold & Partners"
        description={`Set Threshold`}
      />
      <DividerHorizontal />
      <Grid columns="2" gap={"8"}>
        <FormInputNumber
          label={"Proof of Address Amount Threshold"}
          name="proofOfPaymentThresholdAmount"
          formik={formik}
        />
        <FormInputNumber
          label={"Source of Funds Threshold"}
          name="sourceOfFundThresholdAmount"
          formik={formik}
        />
      </Grid>
      <DividerHorizontal />
      <Grid columns="2" gap={"8"}>
        <FormInputNumber
          label={"Transfer Bonus Threshold"}
          name="transferBonusThreshold"
          formik={formik}
        />
      </Grid>
      <DividerHorizontal />
      <Grid columns="3" gap={"8"}>
        <SwitchInput
          label={
            <div>
              <b>Allow Transaction outside country of residence</b>
              <p>This is to allow transfers outside country of residence</p>
            </div>
          }
          formik={formik}
          name="allowTransactionOutsideCountryOfResidence"
        />
        <SwitchInput
          label={
            <div>
              <b>Allow KYC Threshold</b>
              <p>All Users in this category are to transfer pre KYC</p>
            </div>
          }
          formik={formik}
          name="allowTransferPreKCY"
        />
        <FormInputNumber
          label={"KYC Threshold"}
          name="kycThreshold"
          formik={formik}
        />
      </Grid>
    </div>
  );
};

const DashboardStyle = styled.div`
  color: #000;
`;
