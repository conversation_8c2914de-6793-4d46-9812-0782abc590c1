import DetailsOverviewTab from "@/components/client-bits/DetailsOverviewTab";
import BackBtn from "@/components/bits/BackBtn";
import FilterTab from "@/components/bits/FilterTab";
import { Flex } from "@radix-ui/themes";
import React, { FC, useState } from "react";
import { useParams, useSearchParams } from "react-router-dom";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ApiServiceAdmin } from "@/service/admin-services";
import IDDocumentsTab from "@/components/client-bits/IDDocumentsTab";
import TransactionTab from "@/components/client-bits/TransactionTab";
import ClientDetailsHeader from "@/components/client-bits/ClientDetailsHeader";
import AppButton from "@/components/bits/AppButton";
import { LucideMinusCircle, Plus } from "lucide-react";
import GatewayTab from "@/components/client-bits/GatewayTab";
import ChargesTab from "@/components/client-bits/ChargesTab";
import FundClientWalletModal from "@/Modals/FundClientWallet";
import { Skeleton } from "@arco-design/web-react";

const ClientDetailsPage: FC = () => {
  const [query] = useSearchParams();

  const [open, setOpen] = useState(false);

  const { id } = useParams();

  const active = query.get("tab");
  const data = localStorage.getItem("client")
    ? JSON.parse(localStorage.getItem("client"))
    : [];

  const {
    data: client,
    refetch,
    isLoading,
    isFetching,
  } = useQuery({
    queryKey: ["GetPayoutClientDashboardClient"],
    queryFn: () => ApiServiceAdmin.GetPayoutClientDashboard(id),
  });
  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.ToggleClientMutation,
    onSuccess: () => {
      refetch();
    },
    onError: () => {
      return;
    },
  });
  const details = client?.data ? client?.data : data;
  return isLoading || isFetching ? (
    <div>
      <Skeleton />
      <br />
      <Skeleton />
      <br />
      <Skeleton />
      <br />
      <Skeleton />
      <br />
      <Skeleton />
      <br />
      <Skeleton />
      <br />
      <Skeleton />
      <br />
      <Skeleton />
      <br />
      <Skeleton />
      <br />
      <Skeleton />
      <br />
      <Skeleton />
    </div>
  ) : (
    <div>
      {open && (
        <FundClientWalletModal
          open={open}
          setOpen={setOpen}
          finished={refetch}
          item={{ userId: id, ...details }}
        />
      )}
      <Flex justify={"between"} align={"center"} mb={"6"}>
        <BackBtn />

        <Flex
          gap="3"
          mt="4"
          justify="end"
          maxWidth={"300px"}
          width={"100%"}
          ml={"auto"}
        >
          <AppButton
            placeholder="Fund Wallet"
            loading={false}
            onClick={() => {
              setOpen(true);
            }}
            outline
            width="50%"
            disabled={undefined}
            borderColor={undefined}
            style={undefined}
            style2={undefined}
            IconLeft={Plus}
            IconRight={undefined}
            to={undefined}
            roundedFull={undefined}
            radius={undefined}
            secondary={undefined}
          />
          <AppButton
            width="50%"
            placeholder={details?.status === "Active" ? "Suspend" : "Activate"}
            onClick={() => {
              mutate({
                objectId: id,
                action: details?.status === "Active" ? 0 : 1,
              });
            }}
            loading={isPending}
            disabled={isPending}
            outline={undefined}
            secondary={undefined}
            borderColor={undefined}
            style={undefined}
            style2={undefined}
            IconLeft={LucideMinusCircle}
            IconRight={undefined}
            to={undefined}
            roundedFull={undefined}
            radius={undefined}
          />
        </Flex>
      </Flex>
      <ClientDetailsHeader data={details} />
      <br />
      <br />

      <FilterTab
        tab={[
          {
            tab: "profile",
            name: "Profile",
          },
          {
            tab: "id",
            name: "ID Documents",
          },
          {
            tab: "transfer",
            name: "Transactions",
          },
          {
            tab: "charges",
            name: "Charges",
          },
          {
            tab: "gateways",
            name: "Gateways",
          },
        ]}
      />
      <br />
      {active === "profile" && (
        <DetailsOverviewTab data={details} refetch={refetch} />
      )}
      {active === "id" && <IDDocumentsTab data={details} refetch={refetch} />}
      {active === "transfer" && (
        <TransactionTab data={details} refetch={refetch} />
      )}
      {active === "charges" && <ChargesTab data={details} refetch={refetch} />}
      {active === "gateways" && <GatewayTab data={details} refetch={refetch} />}
    </div>
  );
};

export default ClientDetailsPage;
