import * as Yup from "yup";
import AppButton from "@/components/bits/AppButton";
import Box from "@/components/bits/Box";
import MainSelect from "@/components/bits/MainSelect";
//import MiniNavBar from "@/components/bits/MiniNavBar";
import SectionHeader from "@/components/bits/SectionHeader";
import DividerHorizontal from "@/components/DividerHorizontal";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useAuth } from "@/context/global.context";
//import { handleSetItem } from "@/lib/utils";
import { Flex, Grid } from "@radix-ui/themes";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useFormik } from "formik";
import styled from "styled-components";
import { useLocation, useNavigate } from "react-router-dom";
import FormInputNumber from "@/components/bits/FormInputNumber";
import CurrencySelect from "@/components/bits/CurrencySelect";
import { CurrencyCompare } from "@/components/bits/CurrencyCompare";
import CustomTable from "@/components/bits/CustomTable";
import { Pencil } from "lucide-react";
import Modal from "@/components/bits/Modal";
import { useState } from "react";
import { Notification } from "@arco-design/web-react";

export default function UpdateRatePage() {
  const { user_id, user_data } = useAuth();

  const location = useLocation();

  const data = location.state;

  console.log(data, "data");
  const navigate = useNavigate();

  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.UpdateRateMutation,
    onSuccess: () => {
      navigate(-1);
    },
    onError: () => {
      return;
    },
  });

  const formik = useFormik({
    initialValues: {
      id: data?.id,
      updatedBy: {
        userId: user_id,
        firstName: user_data?.firstName,
      },
      currencyRateMetaData: {
        id: data?.currencyRateMetaData?.id,
      },
      conversionRate: data?.conversionRate,
      fromCurrency: {
        id: data?.fromCurrency?.id,
      },
      toCurrency: {
        id: data?.toCurrency?.id,
      },
      feePercentage: data?.feePercentage,
      transactionFeeThreshold: data?.transactionFeeThreshold,
      adminRateBands: data?.adminRateBands,
    },
    onSubmit: (values) => {
      mutate(values);
    },
  });
  console.log(formik.values, "values");
  return (
    <DashboardStyle>
      <Flex
        align={{ initial: "flex-start", md: "center" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "flex-start", md: "between" }}
      >
        <SectionHeader title="Update Rate" showBackBtn description={` `} />
      </Flex>
      &nbsp;
      <Box>
        <Step1 formik={formik} data={data} />

        <DividerHorizontal />

        <Flex
          gap="3"
          mt="4"
          justify="end"
          maxWidth={"500px"}
          width={"100%"}
          ml={"auto"}
        >
          <AppButton
            placeholder="Cancel"
            loading={false}
            disabled={isPending}
            onClick={() => {
              navigate(-1);
            }}
            secondary
            width="50%"
          />
          <AppButton
            width="50%"
            placeholder="Update"
            loading={isPending}
            disabled={isPending}
            onClick={() => {
              formik.handleSubmit();
            }}
          />
        </Flex>
      </Box>
    </DashboardStyle>
  );
}

const Step1 = ({ formik, data }) => {
  const { user_id } = useAuth();
  const [open, setOpen] = useState(false);
  const columns = [
    {
      title: "action",
      dataIndex: "id",
      width: 70,
      render: (e, record) => (
        <div
          style={{
            width: "100%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
          onClick={() => {
            formik2.setValues(record);
            setOpen(true);
          }}
        >
          <Pencil color="#464F60" size={16} />
        </div>
      ),
    },
    {
      title: "id",
      dataIndex: "id",
    },
    {
      title: "charge TYPE",
      dataIndex: "chargeType",
    },
    {
      title: "Charge/Fees",
      dataIndex: "charge",
    },
    {
      title: "Min Amount",
      dataIndex: "minimumAmount",
    },
    {
      title: "Max Amount",
      dataIndex: "maximumAmount",
    },
    {
      title: "Rate",
      dataIndex: "rate",
    },
    {
      title: "Last Updated Date",
      dataIndex: "lastUpdated",
    },
  ];
  const formik2 = useFormik({
    initialValues: {
      minimumAmount: "",
      maximumAmount: "",
      rate: "",
      charge: "",
      id: 34955229,
      currencyRateId: 0,
      chargeType: "Fixed",
      lastUpdated: "2025-04-13 06:59",
      lastUpdatedBy: 0,
    },
    validationSchema: Yup.object().shape({
      minimumAmount: Yup.string().required("Min Amount is required"),
      maximumAmount: Yup.string()
        .required("Maximum amount is required")
        .transform((value) => value.replace(/,/g, "")) // Remove commas
        .test(
          "is-greater",
          "Amount must be greater than minimum amount",
          function (value) {
            return Number(value) > Number(this.parent.minimumAmount);
          }
        ),
      rate: Yup.string().required("Rate is required"),
      charge: Yup.string().required("Charge is required"),
    }),
    onSubmit: (values) => {
      const newBand = formik?.values?.adminRateBands?.filter((itm) => {
        return itm?.id !== values.id;
      });
      formik.setFieldValue("adminRateBands", [values, ...newBand]);
      setOpen(false);
      Notification.success({
        title: "Field Updated",
        content: "Rate Band Field Updated",
      });
    },
  });

  const { data: meta } = useQuery({
    queryKey: ["GetCurrencyRateMetadataListQuery"],
    queryFn: () =>
      ApiServiceAdmin.GetCurrencyRateMetadataQuery({
        role: user_id,
        rateMetaDataId: 0,
      }),
  });
  const metadata = meta?.data?.map((itm) => {
    return {
      ...itm,
      label: itm?.name,
      value: itm?.id,
    };
  });

  return (
    <div>
      {open && (
        <Modal
          trigger={() => {
            setOpen(!open);
          }}
          submit={() => {
            formik2.handleSubmit();
          }}
          title={"Update Fees"}
          open={open}
        >
          <svg
            width="44"
            height="44"
            viewBox="0 0 44 44"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            style={{
              position: "absolute",
              top: "20px",
              right: "20px",
              cursor: "pointer",
            }}
            onClick={() => {
              setOpen(false);
            }}
          >
            <circle
              cx="22"
              cy="22"
              r="21.45"
              fill="white"
              stroke="#F0F0F0"
              stroke-width="1.1"
            />
            <path
              d="M28.6009 15.4009L15.4009 28.6009M15.4009 15.4009L28.6009 28.6009"
              stroke="#666D80"
              stroke-width="2.2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>

          <div>
            <FormInputNumber
              label="Min Amount"
              name="minimumAmount"
              width="100%"
              amount
              formik={formik2}
            />
            <FormInputNumber
              label="Max Amount"
              name="maximumAmount"
              width="100%"
              amount
              formik={formik2}
            />
            <FormInputNumber
              label="Rate"
              name="rate"
              width="100%"
              amount
              formik={formik2}
            />
            <FormInputNumber
              label="Fees"
              name="charge"
              width="100%"
              amount
              formik={formik2}
            />
          </div>
        </Modal>
      )}
      <Grid columns="3" gap={"8"}>
        <MainSelect
          label="Rate Metadata"
          name="currencyRateMetaData[id]"
          width="100%"
          options={metadata}
          formik={formik}
          disabled
        />
        <CurrencySelect
          label="Sending Currency"
          name="fromCurrency[id]"
          width="100%"
          optionValue="id"
          formik={formik}
          disabled
        />
        <CurrencySelect
          label="Receiving Currency"
          name="toCurrency[id]"
          optionValue="id"
          width="100%"
          formik={formik}
          disabled
        />
        <FormInputNumber
          label="Rate"
          name="conversionRate"
          width="100%"
          amount
          formik={formik}
        />
        <FormInputNumber
          label="Charge Percentage"
          name="feePercentage"
          width="100%"
          amount
          formik={formik}
        />
        <FormInputNumber
          label="Charge Threshold"
          name="transactionFeeThreshold"
          width="100%"
          amount
          formik={formik}
        />
      </Grid>
      <DividerHorizontal />
      {data && (
        <CurrencyCompare
          fromCurrency={data?.fromCurrency?.code}
          fromRate={"1"}
          toCurrency={data?.toCurrency?.code}
          toRate={formik.values?.conversionRate}
        />
      )}
      <SectionHeader title="Rate Band" description="" fontSize="20px" />

      <CustomTable
        tableWidth={1870}
        tableColumns={columns}
        arrayData={formik?.values?.adminRateBands}
        scroll={{
          x: 1200,
        }}
      />
    </div>
  );
};

const DashboardStyle = styled.div`
  color: #000;
`;
