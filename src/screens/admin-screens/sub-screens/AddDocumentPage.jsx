import * as Yup from "yup";
import AppButton from "@/components/bits/AppButton";
import Box from "@/components/bits/Box";
import FormInput from "@/components/bits/FormInput";
import MainSelect from "@/components/bits/MainSelect";
//import MiniNavBar from "@/components/bits/MiniNavBar";
import SectionHeader from "@/components/bits/SectionHeader";
import DividerHorizontal from "@/components/DividerHorizontal";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useAuth } from "@/context/global.context";
//import { handleSetItem } from "@/lib/utils";
import { Flex, Grid } from "@radix-ui/themes";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useFormik } from "formik";
import styled from "styled-components";
import { useLocation, useNavigate } from "react-router-dom";
import FormInputDate from "@/components/bits/FormInputDate";
import FormInputFile from "@/components/bits/FormInputFile";
import { useState } from "react";

export default function AddDocumentPage() {
  const { user_id } = useAuth();

  const location = useLocation();

  const data = location.state;

  console.log(data, "data");
  const navigate = useNavigate();

  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.AddUserDocumentMutation,
    onSuccess: () => {
      navigate(-1);
    },
    onError: () => {
      return;
    },
  });

  const [documentId, setDocumentId] = useState();

  const getValidationSchema = (id) => {
    switch (id) {
      case "6":
        return Yup.object().shape({
          userKYCDocument: Yup.object({
            documentType: Yup.object({
              id: Yup.string().required("Document Type is required"),
            }),
            nameOnTheDocument: Yup.string().required(
              "Name on Document is required"
            ),
            documentNumber: Yup.string().required(
              "Document Number is required"
            ),
            placeIssued: Yup.string().required("Place of Issue is required"),
            dateIssued: Yup.string().required("Date Issued is required"),
            expiryDate: Yup.string().required("Expiry Date is required"),
            documentFrontPageURL: Yup.string().required(
              "Front Document Image is required"
            ),
            documentBackPageURL: Yup.string().required(
              "Back Document Image is required"
            ),
          }),
        });
      case "2":
        return Yup.object().shape({
          userKYCDocument: Yup.object({
            documentType: Yup.object({
              id: Yup.string().required("Document Type is required"),
            }),
            nameOnTheDocument: Yup.string().required(
              "Name on Document is required"
            ),
            documentFrontPageURL: Yup.string().required(
              "Front Document Image is required"
            ),
          }),
        });
      case "3":
        return Yup.object().shape({
          userKYCDocument: Yup.object({
            documentType: Yup.object({
              id: Yup.string().required("Document Type is required"),
            }),
            nameOnTheDocument: Yup.string().required(
              "Name on Document is required"
            ),
            /*  documentNumber: Yup.string().required(
                  "Document Number is required"
                ), */
            // placeIssued: Yup.string().required("Place of Issue is required"),
            dateIssued: Yup.string().required("Date Issued is required"),
            expiryDate: Yup.string().required("Expiry Date is required"),
            documentFrontPageURL: Yup.string().required(
              "Front Document Image is required"
            ),
            documentBackPageURL: Yup.string().required(
              "Back Document Image is required"
            ),
          }),
        });
      default:
        return Yup.object().shape({
          userKYCDocument: Yup.object({
            documentType: Yup.object({
              id: Yup.string().required("Document Type is required"),
            }),
            nameOnTheDocument: Yup.string().required(
              "Name on Document is required"
            ),
            documentNumber: Yup.string().required(
              "Document Number is required"
            ),
            placeIssued: Yup.string().required("Place of Issue is required"),
            dateIssued: Yup.string().required("Date Issued is required"),
            expiryDate: Yup.string().required("Expiry Date is required"),
            documentFrontPageURL: Yup.string().required(
              "Front Document Image is required"
            ),
            documentBackPageURL: Yup.string().required(
              "Back Document Image is required"
            ),
          }),
        });
    }
  };

  const formik = useFormik({
    initialValues: {
      userId: data?.userId,
      userKYCDocument: {
        documentType: {
          id: "",
        },
        nameOnTheDocument: "",
        documentNumber: "",
        placeIssued: "",
        dateIssued: "",
        expiryDate: "",
        uploadedBy: user_id,
        verifiedBy: user_id,
        documentFrontPageURL: "",
        documentBackPageURL: "",
        comment: "",
      },
    },
    validationSchema: getValidationSchema(documentId),
    onSubmit: (values) => {
      // Ensure all fields are present in the payload, even if empty
      const payload = {
        ...values,
        userKYCDocument: {
          documentType: {
            id: values.userKYCDocument.documentType.id,
          },
          nameOnTheDocument: values.userKYCDocument.nameOnTheDocument || "",
          documentNumber: values.userKYCDocument.documentNumber || "",
          placeIssued: values.userKYCDocument.placeIssued || "",
          dateIssued: values.userKYCDocument.dateIssued || "",
          expiryDate: values.userKYCDocument.expiryDate || "",
          uploadedBy: user_id,
          verifiedBy: user_id,
          documentFrontPageURL:
            values.userKYCDocument.documentFrontPageURL || "",
          documentBackPageURL: values.userKYCDocument.documentBackPageURL || "",
          comment: values.userKYCDocument.comment || "",
        },
      };

      mutate(payload);
    },
  });
  console.log(formik.values, "values");
  return (
    <DashboardStyle>
      <Flex
        align={{ initial: "flex-start", md: "center" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "flex-start", md: "between" }}
      >
        <SectionHeader title="ID Upload" description={` `} />
      </Flex>
      &nbsp;
      <Box>
        <Step1
          formik={formik}
          userId={data?.userId}
          setDocumentId={setDocumentId}
        />

        <DividerHorizontal />

        <Flex
          gap="3"
          mt="4"
          justify="end"
          maxWidth={"500px"}
          width={"100%"}
          ml={"auto"}
        >
          <AppButton
            placeholder="Back"
            loading={false}
            disabled={isPending}
            onClick={() => {
              navigate(-1);
            }}
            secondary
            width="30%"
          />
          <AppButton
            width="70%"
            placeholder="Submit"
            loading={isPending}
            disabled={isPending}
            onClick={() => {
              formik.handleSubmit();
            }}
          />
        </Flex>
      </Box>
    </DashboardStyle>
  );
}

const Step1 = ({ formik, userId, setDocumentId }) => {
  const { data } = useQuery({
    queryKey: ["GetUserDocumentTypesQuery"],
    queryFn: () => ApiServiceAdmin.GetUserDocumentTypesQuery(),
  });

  const documents =
    data &&
    data?.data?.map((itm) => {
      return {
        ...itm,
        label: itm?.name,
        value: itm?.id,
      };
    });

  const selectedDocumentId = formik.values.userKYCDocument.documentType.id;
  const isFullDocument = ["6", "7", "1"].includes(String(selectedDocumentId));

  return (
    <div>
      <SectionHeader
        fontSize="20px"
        title="Add ID Document"
        description={`Upload the necessary documents`}
      />
      <DividerHorizontal />
      <Grid columns="2" gap={"8"}>
        <MainSelect
          label={"Document Type"}
          width="100%"
          name="userKYCDocument[documentType][id]"
          formik={formik}
          options={documents}
          optionValue="id"
          onChange={(e) => {
            setDocumentId(e?.id);

            // Clear unrequired fields when switching to simple document types
            if (!["6", "7", "1"].includes(String(e?.id))) {
              formik.setFieldValue("userKYCDocument.documentNumber", "");
              formik.setFieldValue("userKYCDocument.placeIssued", "");
              formik.setFieldValue("userKYCDocument.dateIssued", "");
              formik.setFieldValue("userKYCDocument.expiryDate", "");
              formik.setFieldValue("userKYCDocument.documentBackPageURL", "");
            }
          }}
        />

        <FormInput
          label={"Name on Document"}
          name="userKYCDocument[nameOnTheDocument]"
          formik={formik}
        />

        {isFullDocument && (
          <FormInputDate
            label={"Issue Date"}
            name="userKYCDocument[dateIssued]"
            formik={formik}
          />
        )}

        {isFullDocument && (
          <FormInputDate
            label={"Expiry Date"}
            name="userKYCDocument[expiryDate]"
            formik={formik}
          />
        )}

        {isFullDocument && (
          <FormInput
            label={"Place of Issue"}
            name="userKYCDocument[placeIssued]"
            formik={formik}
          />
        )}

        {isFullDocument && (
          <FormInput
            label={"Document Number"}
            name="userKYCDocument[documentNumber]"
            formik={formik}
            userId={userId}
          />
        )}

        <FormInput
          label={"Comments"}
          name="userKYCDocument[comment]"
          formik={formik}
        />
      </Grid>
      <DividerHorizontal />

      <SectionHeader
        fontSize="20px"
        title="Upload your Documents"
        description={`Note: We can accept only png, jpg, jpeg, bmp, pdf files (Please upload file of size less than 6MB)`}
      />
      <Grid columns={isFullDocument ? "2" : "1"} gap={"8"}>
        <FormInputFile
          userId={userId}
          label={"Upload Front ID"}
          id={"front"}
          name={"userKYCDocument[documentFrontPageURL]"}
          formik={formik}
        />
        {isFullDocument && (
          <FormInputFile
            userId={userId}
            label={"Upload Back ID"}
            id={"back"}
            name={"userKYCDocument[documentBackPageURL]"}
            formik={formik}
          />
        )}
      </Grid>
    </div>
  );
};

const DashboardStyle = styled.div`
  color: #000;
`;
