import AppButton from "@/components/bits/AppButton";
import AppLink from "@/components/bits/AppLink";
import { CurrencyBadge } from "@/components/bits/CurrencyBadge";
import CustomTable from "@/components/bits/CustomTable";
//import MiniNavBar from "@/components/bits/MiniNavBar";
import SectionHeader from "@/components/bits/SectionHeader";
import AddCompanyBank from "@/Modals/AddCompanyBank";
//import { handleGetItm } from "@/lib/utils";
import { ApiServiceAdmin } from "@/service/admin-services";
//import { handleSetItem } from "@/lib/utils";
import { Flex } from "@radix-ui/themes";
import { useQuery } from "@tanstack/react-query";
import { Plus } from "lucide-react";
import { useState } from "react";
//import { useNavigate } from "react-router-dom";
import styled from "styled-components";

export default function CompanyBanksPage() {
  const { data, refetch } = useQuery({
    queryKey: ["GetCompanyBanksQuery"],
    queryFn: () => ApiServiceAdmin.GetCompanyBanksQuery(),
  });

  //const navigate = useNavigate();
  const [item, setItem] = useState();
  const columns = [
    {
      title: "action",
      dataIndex: "sn",
      render: (e, record) => (
        <AppLink
          type="mmm"
          onClick={() => {
            setItem(record);
            setOpen(true);
          }}
        >
          Update Bank
        </AppLink>
      ),
    },
    {
      title: "bank name",
      dataIndex: "bankName",
    },
    {
      title: "account holder name",
      dataIndex: "accountName",
    },
    {
      title: "account number",
      dataIndex: "accountNumber",
    },
    {
      title: "sort code",
      dataIndex: "sortCode",
    },
    {
      title: "currency",
      dataIndex: "currency[code]",
      render: (e) => <CurrencyBadge currency={e} />,
    },

    {
      title: "date added",
      dataIndex: "dateCreated",
    },
  ];

  const [open, setOpen] = useState();

  return (
    <DashboardStyle>
      {open && (
        <AddCompanyBank
          open={open}
          setOpen={setOpen}
          finished={refetch}
          item={item}
        />
      )}
      <Flex
        align={{ initial: "flex-start", md: "center" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "flex-start", md: "between" }}
      >
        <SectionHeader
          title="Company Banks"
          description={`This page allows you to manage all company banks`}
        />

        <AppButton
          onClick={() => {
            setItem();
            setOpen(true);
          }}
          width="230px"
          IconLeft={Plus}
          placeholder={"New Company Bank"}
        />
      </Flex>
      &nbsp;
      <CustomTable tableColumns={columns} arrayData={data?.data} />
    </DashboardStyle>
  );
}

const DashboardStyle = styled.div`
  color: #000;
`;
