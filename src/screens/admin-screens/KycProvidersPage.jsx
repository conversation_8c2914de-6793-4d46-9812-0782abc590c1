import AppButton from "@/components/bits/AppButton";
import Box from "@/components/bits/Box";
import SectionHeader from "@/components/bits/SectionHeader";
import DividerHorizontal from "@/components/DividerHorizontal";
import { ApiServiceAdmin } from "@/service/admin-services";
import { Flex, Grid } from "@radix-ui/themes";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useState } from "react";
import styled from "styled-components";

export default function KycProvidersPage() {
  const { data, refetch } = useQuery({
    queryKey: ["GetKycProviderQuery"],
    queryFn: () => ApiServiceAdmin.GetKycProviderQuery(),
  });

  const [id, setId] = useState();
  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.ChangeKycProviderMutation,
    onSuccess: () => {
      refetch();
    },
    onError: () => {
      return;
    },
  });

  const check = (itm) => {
    return id ? id === itm?.id : itm?.status;
  };

  return (
    <DashboardStyle>
      <Flex
        align={{ initial: "flex-start", md: "center" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "flex-start", md: "between" }}
      >
        <SectionHeader
          title="KYC Providers"
          description={`This page allows you to manage all KYC providers`}
        />
      </Flex>
      &nbsp;
      <Box>
        <Grid columns={"3"} gap="1rem">
          {data?.data?.map((item, index) => (
            <Flex
              key={index}
              direction={{ initial: "column", md: "row" }}
              justify={{ initial: "flex-start", md: "between" }}
              align={{ initial: "flex-start", md: "center" }}
              style={{
                padding: "1rem",
                borderRadius: "8px",
                border: check(item) ? "1px solid #FF7434" : "1px solid #E5E5E5",
                cursor: "pointer",
              }}
              onClick={() => {
                if (isPending) {
                  return;
                } else {
                  setId(item?.id);
                }
              }}
            >
              <Flex
                justify={"between"}
                align={"center"}
                gap={"2"}
                width={"100%"}
              >
                <Flex gap={"2"} align={"center"}>
                  <img
                    src={item?.logo}
                    style={{
                      width: "40px",
                      height: "40px",
                      borderRadius: 999,
                      background: "#000",
                    }}
                  />
                  <h4>{item?.name}</h4>
                </Flex>

                {check(item) ? <Checked /> : <Unchecked />}
              </Flex>
            </Flex>
          ))}
        </Grid>

        <DividerHorizontal />

        <AppButton
          width="200px"
          style2={{
            marginLeft: "auto",
          }}
          onClick={() => {
            mutate(id);
          }}
          disabled={isPending}
          loading={isPending}
          placeholder={"Save Changes"}
        />
      </Box>
    </DashboardStyle>
  );
}

const Checked = () => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect x="4" y="4" width="16" height="16" rx="8" fill="#FF7434" />
      <g filter="url(#filter0_d_11371_219296)">
        <rect x="9" y="9" width="6" height="6" rx="3" fill="white" />
      </g>
      <defs>
        <filter
          id="filter0_d_11371_219296"
          x="7"
          y="8"
          width="10"
          height="10"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="1" />
          <feGaussianBlur stdDeviation="1" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.297064 0 0 0 0 0.1404 0 0 0 0 0.6396 0 0 0 0.6 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_11371_219296"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_11371_219296"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  );
};

const Unchecked = () => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g filter="url(#filter0_dd_11371_219180)">
        <rect x="4" y="4" width="16" height="16" rx="8" fill="white" />
      </g>
      <defs>
        <filter
          id="filter0_dd_11371_219180"
          x="2"
          y="3"
          width="20"
          height="20"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology
            radius="1"
            operator="dilate"
            in="SourceAlpha"
            result="effect1_dropShadow_11371_219180"
          />
          <feOffset />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.8757 0 0 0 0 0.882373 0 0 0 0 0.9043 0 0 0 1 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_11371_219180"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="1" />
          <feGaussianBlur stdDeviation="1" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.643275 0 0 0 0 0.672823 0 0 0 0 0.725353 0 0 0 0.4 0"
          />
          <feBlend
            mode="normal"
            in2="effect1_dropShadow_11371_219180"
            result="effect2_dropShadow_11371_219180"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect2_dropShadow_11371_219180"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  );
};

const DashboardStyle = styled.div`
  color: #000;
`;
