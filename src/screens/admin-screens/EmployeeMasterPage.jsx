import AppButton from "@/components/bits/AppButton";
import CustomTable from "@/components/bits/CustomTable";
import { MenuItem, MenuList } from "@/components/bits/DropDownMenu";
//import MiniNavBar from "@/components/bits/MiniNavBar";
import SectionHeader from "@/components/bits/SectionHeader";
import SwitchInput from "@/components/bits/SwitchInput";
import UpdatePasswordModal from "@/Modals/UpdatePasswordModal";
import { ApiServiceAdmin } from "@/service/admin-services";
//import { handleSetItem } from "@/lib/utils";
import { Flex } from "@radix-ui/themes";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Plus } from "lucide-react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import styled from "styled-components";

export default function EmployeeMasterPage() {
  const { data, isLoading, refetch } = useQuery({
    queryKey: ["GetEmployeeQuery"],
    queryFn: () => ApiServiceAdmin.GetEmployeeQuery(),
  });

  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.ActivateCustomerMutation,
    onSuccess: () => {
      refetch();
    },
    onError: () => {
      return;
    },
  });
  const { mutate: mutate2, isPending: isPendingOther } = useMutation({
    mutationFn: ApiServiceAdmin.DeactivateCustomerMutation,
    onSuccess: () => {
      refetch();
    },
    onError: () => {
      return;
    },
  });
  console.log(data, "GetAgentInviteQuery");

  const navigate = useNavigate();

  const [updatePasswordModal, setUpdatePasswordModal] = useState(false);
  const userMenu = [
    {
      name: "Update Employee",
      action: (record) => {
        navigate(`/edit-employee`, {
          state: record,
        });
      },
      index: 1,
    },
    {
      name: "Update Password",
      action: (record) => {
        setUpdatePasswordModal(true);
        setItem(record);
      },
      index: 2,
    },
  ];

  const [item, setItem] = useState();
  const columns = [
    {
      title: "Action",
      dataIndex: "id",
      fixed: "left",
      width: 100,
      render: (e, record) => (
        <MenuList
          onClick={() => {
            setItem(record);
          }}
          ActionIcon={undefined}
          ActionElement={undefined}
          iconWidth={undefined}
        >
          {userMenu?.map(({ name, action, index, color }) => {
            return (
              <>
                <MenuItem
                  name={name}
                  index={index}
                  action={() => {
                    action(record);
                  }}
                  Icon={undefined}
                  to={undefined}
                  width={"160px"}
                  padding={2}
                  color={color}
                />
              </>
            );
          })}
        </MenuList>
      ),
    },

    {
      title: "username",
      dataIndex: "username",
    },
    {
      title: "role",
      dataIndex: "role[name]",
    },
    {
      title: "name",
      dataIndex: "firstName",
    },
    {
      title: "mobile number",
      dataIndex: "phone",
    },
    {
      title: "city",
      dataIndex: "city[name]",
    },
    {
      title: "make active",
      dataIndex: "status",
      render: (e, record) => (
        <SwitchInput
          onChange={() => {
            if (e === "InActive") {
              mutate({
                userId: record?.userId,
              });
            } else {
              mutate2({
                userId: record?.userId,
              });
            }
          }}
          checked={e === "InActive" ? false : true}
        />
      ),
    },
  ];

  const list = data?.data;
  return (
    <DashboardStyle>
      <Flex
        align={{ initial: "flex-start", md: "center" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "flex-start", md: "between" }}
      >
        <SectionHeader
          title="Employee Master"
          description={`This page allows you to manage employees.`}
        />

        <AppButton
          IconLeft={Plus}
          placeholder="New Employee"
          width="200px"
          loading={false}
          disabled={false}
          to={"/add-employee"}
        />
      </Flex>
      &nbsp;
      <CustomTable
        loading={isLoading || isPending || isPendingOther}
        tableColumns={columns}
        arrayData={list}
        scroll={{
          x: 1100,
        }}
      />
      <UpdatePasswordModal
        open={updatePasswordModal}
        setOpen={setUpdatePasswordModal}
        item={item}
      />
    </DashboardStyle>
  );
}

const DashboardStyle = styled.div`
  color: #000;
`;
