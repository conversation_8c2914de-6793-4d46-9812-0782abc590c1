import AppButton from "@/components/bits/AppButton";
import AppLink from "@/components/bits/AppLink";
import { CurrencyBadge } from "@/components/bits/CurrencyBadge";
import CustomTable from "@/components/bits/CustomTable";
import FormInput from "@/components/bits/FormInput";
//import MiniNavBar from "@/components/bits/MiniNavBar";
import SectionHeader from "@/components/bits/SectionHeader";
import SwitchInput from "@/components/bits/SwitchInput";
import { useAuth } from "@/context/global.context";
import { downloadFile } from "@/lib/utils";
import CreateRateModal from "@/Modals/CreateRateModal";
//import { handleGetItm } from "@/lib/utils";
import { ApiServiceAdmin } from "@/service/admin-services";
import { DatePicker } from "@arco-design/web-react";
//import { handleSetItem } from "@/lib/utils";
import { Flex } from "@radix-ui/themes";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Download, Play, Plus, Search, StopCircle } from "lucide-react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import styled from "styled-components";

export default function UpdateRatesAndFeesPage() {
  const { user_id, user_data } = useAuth();
  const [open, setOpen] = useState(false);
  const {
    data: allRates,
    refetch,
    isLoading,
  } = useQuery({
    queryKey: ["GetAllRatesQuery"],
    queryFn: () =>
      ApiServiceAdmin.GetAllRatesQuery({
        role: user_id,
        rateMetaDataId: 0,
      }),
  });

  const { data: rateLogs, isLoading: ourLoading } = useQuery({
    queryKey: ["GetRateLogsQuery"],
    queryFn: () =>
      ApiServiceAdmin.GetRateLogsQuery({
        role: user_id,
        rateMetaDataId: 0,
      }),
  });

  const { data: agentRates, isLoading: agentLoading } = useQuery({
    queryKey: ["GetAgentRatesQuery"],
    queryFn: () =>
      ApiServiceAdmin.GetAgentRatesQuery({
        agentId: user_id,
        rateId: 0,
      }),
  });

  const { mutate: toggle, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.ToggleCurrencyRateMutation,
    onSuccess: () => {
      refetch();
    },
    onError: () => {
      return;
    },
  });
  const navigate = useNavigate();

  const existingColumns = [
    {
      title: "action",
      dataIndex: "id",
      fixed: "left",
      width: 160,
      render: (e, record) => (
        <div
          onClick={() => {
            navigate(`/update-rate/${e}`, {
              state: record,
            });
          }}
        >
          <AppLink>Update Rate</AppLink>
        </div>
      ),
    },
    {
      title: "sending currency",
      dataIndex: "fromCurrency[code]",
      render: (e) => <CurrencyBadge currency={e} />,
      width: 160,
    },
    {
      title: "receiving currency",
      dataIndex: "toCurrency[code]",
      render: (e) => <CurrencyBadge currency={e} />,
      width: 160,
    },
    {
      title: "currency code",
      dataIndex: "currencyRateMetaData[currency][code]",
      render: (e) => <CurrencyBadge currency={e} />,
      width: 160,
    },
    {
      title: "category",
      dataIndex: "currencyRateMetaData[name]",
    },
    {
      title: "rate",
      dataIndex: "conversionRate",
    },
    {
      title: "stop trading",
      dataIndex: "status",
      render: (e, record) => (
        <SwitchInput
          checked={e}
          onChange={() => {
            if (e) {
              toggle({
                action: 0,
                objectId: record?.id,
              });
            } else {
              toggle({
                action: 1,
                objectId: record?.id,
              });
            }
          }}
        />
      ),
    },
    {
      title: "date",
      dataIndex: "dateCreated",
      // render: (e) => moment(e).format("DD/MM/YY, hh:mm:ss"),
    },
  ];

  const ourColumns = [
    {
      title: "created by",
      dataIndex: "createdByName",
      width: 160,
    },
    {
      title: "sending currency",
      dataIndex: "fromCurrency",
      render: (e) => <CurrencyBadge currency={e} />,
      width: 160,
    },
    {
      title: "receiving currency",
      dataIndex: "toCurrency",
      render: (e) => <CurrencyBadge currency={e} />,
      width: 160,
    },
    {
      title: "currency code",
      dataIndex: "fromCurrency",
      render: (e) => <CurrencyBadge currency={e} />,
      width: 160,
    },
    {
      title: "category",
      dataIndex: "currencyRateMetaData[name]",
    },
    {
      title: "rate",
      dataIndex: "rate",
    },

    {
      title: "date",
      dataIndex: "ratAsAt",
      // render: (e) => moment(e).format("DD/MM/YY, hh:mm:ss"),
    },
  ];

  const agentColumns = [
    {
      title: "rate id",
      dataIndex: "id",
      width: 160,
    },
    {
      title: "agent id",
      dataIndex: "agentId",
      width: 160,
    },

    {
      title: "sending currency",
      dataIndex: "fromCurrency[code]",
      render: (e) => <CurrencyBadge currency={e} />,
      width: 160,
    },
    {
      title: "receiving currency",
      dataIndex: "toCurrency[code]",
      render: (e) => <CurrencyBadge currency={e} />,
      width: 160,
    },
    {
      title: "agent fee threshold",
      dataIndex: "agentTransactionFeeThreshold",
      width: 200,
    },
    {
      title: "initial rate",
      dataIndex: "conversionRate",
      width: 200,
    },
    {
      title: "agent rate",
      dataIndex: "agentRate",
      width: 200,
    },

    {
      title: "admin last updated",
      width: 210,
      dataIndex: "adminLastUpdate",
      // render: (e) => moment(e).format("DD/MM/YY, hh:mm:ss"),
    },
  ];

  const [date, setDate] = useState();

  const [search, setSearch] = useState("");

  const applyFilters = (data, search, start, end) => {
    return data?.filter((item) => {
      const itemDate = new Date(item?.ratAsAt?.replace(" ", "T"));

      // Handle date filtering
      let isWithinDateRange = true;
      if (start) {
        const startDate = start;
        isWithinDateRange = itemDate >= startDate;
      }
      if (end) {
        const endDate = end;
        isWithinDateRange = isWithinDateRange && itemDate <= endDate;
      }

      // Handle search filtering (on paymentRef or userId)
      let matchesSearch = true;
      if (search) {
        const lowerSearch = search.toLowerCase();
        const refMatch = item?.createdByName
          ?.toString()
          ?.toLowerCase()
          ?.includes(lowerSearch);

        const client = item?.currencyRateMetaData?.name
          ?.toString()
          ?.toLowerCase()
          ?.includes(lowerSearch);
        matchesSearch = refMatch || client;
      }

      return isWithinDateRange && matchesSearch;
    });
  };

  const newData = applyFilters(
    rateLogs?.data,
    search,
    date && date?.[0],
    date && date?.[1]
  );

  const [date2, setDate2] = useState();

  const [search2, setSearch2] = useState("");

  const applyFilters2 = (data, search, start, end) => {
    return data?.filter((item) => {
      const itemDate = new Date(item?.adminLastUpdate?.replace(" ", "T"));

      // Handle date filtering
      let isWithinDateRange = true;
      if (start) {
        const startDate = start;
        isWithinDateRange = itemDate >= startDate;
      }
      if (end) {
        const endDate = end;
        isWithinDateRange = isWithinDateRange && itemDate <= endDate;
      }

      // Handle search filtering (on paymentRef or userId)
      let matchesSearch = true;
      if (search) {
        const lowerSearch = search.toLowerCase();

        const ag = item?.agentId
          ?.toString()
          ?.toLowerCase()
          ?.includes(lowerSearch);
        const client = item?.id
          ?.toString()
          ?.toLowerCase()
          ?.includes(lowerSearch);

        matchesSearch = ag || client;
      }

      return isWithinDateRange && matchesSearch;
    });
  };

  const newData2 = applyFilters2(
    agentRates?.data,
    search2,
    date2 && date2?.[0],
    date2 && date2?.[1]
  );

  return (
    <DashboardStyle>
      <CreateRateModal finished={refetch} open={open} setOpen={setOpen} />
      <Flex
        align={{ initial: "flex-start", md: "center" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "flex-start", md: "between" }}
      >
        <SectionHeader
          title={
            user_data?.role?.name === "Agent"
              ? "Rates & Fees"
              : "Update Rates & Fees"
          }
          description={
            user_data?.role?.name === "Agent"
              ? `This page allows you to manage transfer rates and fee`
              : `This page allows you to manage and update transfer rates and fee`
          }
        />
        {user_data?.role?.name === "Agent" ? (
          ""
        ) : (
          <AppButton
            IconLeft={Plus}
            placeholder="Create Rate"
            width="200px"
            loading={false}
            disabled={false}
            onClick={() => {
              setOpen(true);
            }}
          />
        )}
      </Flex>
      &nbsp;
      {user_data?.role?.name === "Agent" ? (
        ""
      ) : (
        <CustomTable
          tableColumns={existingColumns}
          arrayData={allRates?.data}
          topContent={
            <Flex justify={"between"}>
              <SectionHeader
                title="Existing Rates"
                fontSize="25px"
                description={``}
              />

              <Flex gap={"4"}>
                <AppButton
                  IconLeft={StopCircle}
                  placeholder="Stop Trading"
                  width="140px"
                  loading={isPending}
                  disabled={isPending}
                  textColor="red"
                  onClick={() => {
                    toggle({
                      action: 0,
                      objectId: 0,
                    });
                  }}
                  outline
                />
                <AppButton
                  IconLeft={Play}
                  placeholder="Start Trading"
                  width="140px"
                  loading={isPending}
                  disabled={isPending}
                  outline
                  onClick={() => {
                    toggle({
                      action: 1,
                      objectId: 0,
                    });
                  }}
                />
              </Flex>
            </Flex>
          }
          loading={isPending || isLoading}
        />
      )}
      <br />
      {user_data?.role?.name === "Agent" ? (
        ""
      ) : (
        <CustomTable
          tableColumns={ourColumns}
          loading={ourLoading}
          arrayData={newData?.length ? newData : rateLogs?.data}
          topContent={
            <>
              <SectionHeader
                title="Our Rates"
                fontSize="25px"
                description={``}
              />
              <Flex justify={"between"} align={"end"}>
                <FormInput
                  placeholder={"Search by Created By or Category..."}
                  background="#fff"
                  IconLeft={Search}
                  width="400px"
                  onChange={(e) => {
                    setSearch(e?.target?.value?.trim());
                  }}
                  name={undefined}
                  type={undefined}
                  label={undefined}
                  disabled={undefined}
                  formik={undefined}
                  max={undefined}
                  IconRight={undefined}
                  hint={undefined}
                />

                <Flex justify={"between"} align={"start"} gap={"4"}>
                  <DatePicker.RangePicker
                    className="range"
                    placeholder={["Start Date", "End Date"]}
                    onChange={(e) => {
                      if (e?.length) {
                        setDate([
                          new Date(`${e[0]}T00:00:00`),
                          new Date(`${e[1]}T23:59:59`),
                        ]);
                      } else {
                        setDate(undefined);
                      }
                      console.log(e, "date");
                    }}
                    style={{
                      marginBottom: "10px",
                    }}
                    position="br"
                  />
                  {search || date ? (
                    <AppButton
                      width="160px"
                      IconLeft={Download}
                      placeholder="Download"
                      onClick={() => {
                        if (newData) downloadFile(newData, "logs");
                      }}
                      loading={undefined}
                      disabled={undefined}
                      outline={undefined}
                      secondary={undefined}
                      borderColor={undefined}
                      style={undefined}
                      style2={undefined}
                      IconRight={undefined}
                      to={undefined}
                      roundedFull={undefined}
                      radius={undefined}
                    />
                  ) : (
                    ""
                  )}
                </Flex>
              </Flex>
            </>
          }
        />
      )}
      <br />
      <CustomTable
        tableColumns={agentColumns}
        loading={agentLoading}
        arrayData={newData2?.length ? newData2 : agentRates?.data}
        topContent={
          <>
            <SectionHeader
              title="Agent Rates"
              fontSize="25px"
              description={``}
            />

            <Flex justify={"between"} align={"end"}>
              <FormInput
                placeholder={"Search by Created By or Category..."}
                background="#fff"
                IconLeft={Search}
                width="400px"
                onChange={(e) => {
                  setSearch2(e?.target?.value?.trim());
                }}
                name={undefined}
                type={undefined}
                label={undefined}
                disabled={undefined}
                formik={undefined}
                max={undefined}
                IconRight={undefined}
                hint={undefined}
              />

              <Flex justify={"between"} align={"start"} gap={"4"}>
                <DatePicker.RangePicker
                  className="range"
                  placeholder={["Start Date", "End Date"]}
                  onChange={(e) => {
                    if (e?.length) {
                      setDate2([
                        new Date(`${e[0]}T00:00:00`),
                        new Date(`${e[1]}T23:59:59`),
                      ]);
                    } else {
                      setDate2(undefined);
                    }
                    console.log(e, "date");
                  }}
                  style={{
                    marginBottom: "10px",
                  }}
                  position="br"
                />
                {search2 || date2 ? (
                  <AppButton
                    width="160px"
                    IconLeft={Download}
                    placeholder="Download"
                    onClick={() => {
                      if (newData2) downloadFile(newData2, "logs");
                    }}
                    loading={undefined}
                    disabled={undefined}
                    outline={undefined}
                    secondary={undefined}
                    borderColor={undefined}
                    style={undefined}
                    style2={undefined}
                    IconRight={undefined}
                    to={undefined}
                    roundedFull={undefined}
                    radius={undefined}
                  />
                ) : (
                  ""
                )}
              </Flex>
            </Flex>
          </>
        }
      />
    </DashboardStyle>
  );
}

const DashboardStyle = styled.div`
  color: #000;
`;
