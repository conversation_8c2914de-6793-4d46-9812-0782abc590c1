import AppButton from "@/components/bits/AppButton";
//import MiniNavBar from "@/components/bits/MiniNavBar";
import SectionHeader from "@/components/bits/SectionHeader";
import CustomerTableList from "@/components/dashboard-tables/CustomerTableList";
import { useAuth } from "@/context/global.context";
import MessageCustomerModal from "@/Modals/MessageCustomer";
import { ApiServiceAdmin } from "@/service/admin-services";
//import { handleSetItem } from "@/lib/utils";
import { Flex } from "@radix-ui/themes";
import { useQuery } from "@tanstack/react-query";
import { Megaphone, Plus } from "lucide-react";
import { useState } from "react";
import styled from "styled-components";

export default function CustomersPage() {
  const { user_id, user_data } = useAuth();
  const { data, isLoading, isFetching, refetch } = useQuery({
    queryKey: ["GetCustomersQuery"],
    queryFn: () =>
      user_id
        ? ApiServiceAdmin.GetAgentCustomersQuery(user_id)
        : ApiServiceAdmin.GetCustomersQuery(),
  });
  console.log(data, "GetCustomersQuery");
  const [messageCustomerModal, setMessageCustomerModal] = useState(false);

  return (
    <DashboardStyle>
      <Flex
        align={{ initial: "flex-start", md: "center" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "flex-start", md: "between" }}
      >
        <SectionHeader
          title="Customers"
          description={`This page allows you to manage customers.`}
        />
        <Flex gap={"4"}>
          {user_data?.role?.name !== "Agent" && (
            <AppButton
              IconLeft={Megaphone}
              placeholder="Message All"
              width="170px"
              loading={false}
              disabled={false}
              onClick={() => {
                setMessageCustomerModal(true);
              }}
              outline
            />
          )}
          <AppButton
            IconLeft={Plus}
            placeholder="Add Customer"
            width="170px"
            loading={false}
            disabled={false}
            to="/add-customer"
          />
        </Flex>
      </Flex>
      {messageCustomerModal && (
        <MessageCustomerModal
          open={messageCustomerModal}
          setOpen={setMessageCustomerModal}
          finished={refetch}
          type={"all"}
          userRoleId={6}
        />
      )}
      &nbsp;
      <CustomerTableList
        data={data}
        isLoading={isLoading || isFetching}
        refetch={refetch}
      />
    </DashboardStyle>
  );
}

const DashboardStyle = styled.div`
  color: #000;
`;
