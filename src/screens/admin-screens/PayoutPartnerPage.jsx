import AppButton from "@/components/bits/AppButton";
import Box from "@/components/bits/Box";
import CustomTable from "@/components/bits/CustomTable";
import FormInputLabel from "@/components/bits/FormInputLabel";
import MainSelect from "@/components/bits/MainSelect";
//import MiniNavBar from "@/components/bits/MiniNavBar";
import SectionHeader from "@/components/bits/SectionHeader";
import { StatusBadge } from "@/components/bits/StatusBadge";
import DividerHorizontal from "@/components/DividerHorizontal";
import { FormatCurrency } from "@/lib/utils";
import { ApiServiceAdmin } from "@/service/admin-services";
import { DatePicker } from "@arco-design/web-react";
//import { handleSetItem } from "@/lib/utils";
import { Flex, Grid } from "@radix-ui/themes";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import styled from "styled-components";

export default function PayoutPartnerPage() {
  const [partner, setPartner] = useState("");
  const [gateway, setGateway] = useState("");
  const [date, setDate] = useState("");

  const columns = [
    {
      title: "id",
      dataIndex: "id",
      width: 100,
    },
    {
      title: "bank name",
      dataIndex: "beneficiary['beneficiaryBank']['bankName']",
      width: 170,
    },
    {
      title: "account number",
      dataIndex: "beneficiary['beneficiaryBank']['accountNumber']",
      width: 240,
    },
    {
      title: "account name",
      dataIndex: "beneficiary['beneficiaryBank']['accountName']",
      width: 170,
    },
    {
      title: "transaction id",
      dataIndex: "clientRef",
      width: 170,
    },
    {
      title: "status",
      dataIndex: "status",
      render: (e) => <StatusBadge status={e} />,
      width: 170,
    },
    {
      title: "charges",
      dataIndex: "transferFee",
      render: (e) => FormatCurrency(e),
      width: 170,
    },
    {
      title: "amount",
      dataIndex: "Amount",
      width: 190,
      render: (e, record) => FormatCurrency(e, record.currency.code),
    },
    {
      title: "narration",
      dataIndex: "note",
      width: 170,
    },
    {
      title: "date",
      dataIndex: "dateCreated",
      width: 170,
    },
  ];

  const { data } = useQuery({
    queryKey: ["GetPayoutPartnerPageQuery"],
    queryFn: () => ApiServiceAdmin.GetPayoutPartnerQuery(),
  });

  const { data: list } = useQuery({
    queryKey: ["GetPayoutPartnerGatewaysPageQuery"],
    queryFn: () =>
      ApiServiceAdmin.GetPayoutPartnerGatewaysQuery(partner?.value),
    enabled: partner?.value ? true : false,
  });

  const {
    data: logs,
    refetch,
    isLoading,
    isFetching,
  } = useQuery({
    queryKey: ["GetPayoutPartnerLogsQuery"],
    queryFn: () =>
      ApiServiceAdmin.GetPayoutPartnerLogsQuery({
        partnerId: partner?.value,
        start: date[0],
        end: date[1],
      }),
    enabled: false,
  });

  console.log(logs);
  const payoutPartners =
    data &&
    data.data.map((itm) => {
      return {
        label: itm.name,
        value: itm.id,
      };
    });

  const gateways =
    list &&
    list.data.map((itm) => {
      return {
        ...itm,
        label: itm.providerName,
        value: itm.providerId,
      };
    });
  console.log(list, "data");
  return (
    <DashboardStyle>
      <Flex
        align={{ initial: "flex-start", md: "center" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "flex-start", md: "between" }}
      >
        <SectionHeader
          title="Payout Partners"
          description={`This page allows you to manage all Payout Partners`}
        />
      </Flex>
      &nbsp;
      <Grid columns={{ initial: "1", md: "4" }} gap={"6"}>
        {gateways && !gateways.length ? (
          <div>No Selected gateways</div>
        ) : (
          gateways &&
          gateways.map((itm) => {
            return (
              <Box
                style={{
                  textAlign: "center",
                  color: "#5A6376",
                  justifyContent: "space-between",
                }}
              >
                <p
                  style={{
                    color: "#5A6376",
                  }}
                >
                  {itm.providerName}
                </p>
                <p style={{ marginBlock: "10px", color: "#5A6376" }}>
                  Naira Balance
                </p>
                <h1 style={{ margin: 0 }}>
                  {FormatCurrency(itm.wallet.balance, itm.wallet.currency.code)}
                </h1>
              </Box>
            );
          })
        )}
      </Grid>
      &nbsp;
      <Box
        style={{
          height: "280px",
        }}
      >
        <Grid columns={"3"} gap={"4"}>
          <MainSelect
            label="Payout Partner"
            placeholder="Select..."
            options={payoutPartners}
            value={partner}
            onChange={(e) => {
              setPartner(e);
            }}
          />
          <MainSelect
            label="Gateway"
            placeholder="Select..."
            options={gateways}
            value={gateway}
            onChange={(e) => {
              setGateway(e);
            }}
          />

          <FormInputLabel label={"Date Range"}>
            <DatePicker.RangePicker
              className="range"
              onChange={(e) => {
                setDate(e);
                console.log(e);
              }}
              position="br"
            />
          </FormInputLabel>
        </Grid>
        <DividerHorizontal />
        <AppButton
          placeholder={"Search"}
          style2={{
            margin: "auto",
          }}
          width="170px"
          disabled={!partner || !gateway || !date}
          onClick={() => {
            refetch({
              partnerId: partner?.value,
              start: date[0],
              end: date[1],
            });
          }}
        />
      </Box>
      &nbsp;
      <CustomTable
        arrayData={logs?.data}
        tableColumns={columns}
        loading={isLoading || isFetching}
      />
    </DashboardStyle>
  );
}

const DashboardStyle = styled.div`
  color: #000;

  .range {
    width: 240px;
    margin-top: 10px;
    appearance: none;
    outline: none;
    padding: 22px 15px;
    font-size: 16px;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    background-color: #fff;
    position: relative;
  }
`;
