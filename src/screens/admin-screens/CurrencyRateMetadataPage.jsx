import AppButton from "@/components/bits/AppButton";
import AppLink from "@/components/bits/AppLink";
import CustomTable from "@/components/bits/CustomTable";
//import MiniNavBar from "@/components/bits/MiniNavBar";
import SectionHeader from "@/components/bits/SectionHeader";
import { useAuth } from "@/context/global.context";
import { handleGetItm } from "@/lib/utils";
import { ApiServiceAdmin } from "@/service/admin-services";
//import { handleSetItem } from "@/lib/utils";
import { Flex } from "@radix-ui/themes";
import { useQuery } from "@tanstack/react-query";
import { Plus } from "lucide-react";
import { useNavigate } from "react-router-dom";
import styled from "styled-components";

export default function CurrencyRateMetadataPage() {
  const { user_id } = useAuth();
  const { data } = useQuery({
    queryKey: ["GetCurrencyRateMetadataQuery"],
    queryFn: () =>
      ApiServiceAdmin.GetCurrencyRateMetadataQuery({
        role: user_id,
        rateMetaDataId: 0,
      }),
  });

  const navigate = useNavigate();
  const columns = [
    {
      title: "action",
      dataIndex: "id",
      fixed: "left",
      width: 160,
      render: (e) => (
        <div
          onClick={() => {
            navigate(
              `/update-currency-rate-metadata`,

              {
                state: handleGetItm(e, "id", data?.data),
              }
            );
          }}
        >
          <AppLink type="normal"> Update Metadata</AppLink>
        </div>
      ),
    },
    {
      title: "name",
      dataIndex: "name",
    },
    {
      title: "description",
      dataIndex: "description",
    },
    {
      title: "TRANSFER BONUS THRESHOLD",
      dataIndex: "transferBonusThreshold",
    },

    {
      title: "date",
      dataIndex: "dateCreated",
      // render: (e) => moment(e).format("DD/MM/YY, hh:mm:ss"),
    },
  ];

  return (
    <DashboardStyle>
      <Flex
        align={{ initial: "flex-start", md: "center" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "flex-start", md: "between" }}
      >
        <SectionHeader
          title="Currency Rate Metadata"
          description={`This page allows you to manage and update transfer rates and fee`}
        />
        <AppButton
          IconLeft={Plus}
          placeholder="New Metadata"
          width="200px"
          loading={false}
          disabled={false}
          to={"/create-currency-rate-metadata"}
        />
      </Flex>
      &nbsp;
      <CustomTable tableColumns={columns} arrayData={data?.data} />
    </DashboardStyle>
  );
}

const DashboardStyle = styled.div`
  color: #000;
`;
