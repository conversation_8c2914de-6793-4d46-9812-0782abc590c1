/* eslint-disable react-hooks/exhaustive-deps */
import CancelledIcon from "@/assets/icons/CancelledIcon";
import CustomerPurpleIcon from "@/assets/icons/CustomerPurpleIcon";
import PendingInvoiceIcon from "@/assets/icons/PendingInvoiceIcon";
import TransactionCountYellowIcon from "@/assets/icons/TransactionCountYellowIcon";
import WalletGreenIcon from "@/assets/icons/WalletGreenIcon";
import AdminBox from "@/components/AdminBox";
import Box from "@/components/bits/Box";
import CurrencySelect from "@/components/bits/CurrencySelect";
import MainDashboardAnalysisCard from "@/components/bits/MainDashboardAnalysisCard";
import MainDashboardCard from "@/components/bits/MainDashboardCard";
import SectionHeader from "@/components/bits/SectionHeader";
import CustomerTypeChart from "@/components/dashboard-tables/CustomerTypeChart";
import FundRequestLog from "@/components/dashboard-tables/FundRequestLog";
import NewCustomerList from "@/components/dashboard-tables/NewCustomerList";
import PaymentTypeChart from "@/components/dashboard-tables/PaymentTypeChart";
import TodayLogs from "@/components/dashboard-tables/TodayLogs";
import TransactionsTypeChart from "@/components/dashboard-tables/TransactionsTypeChart";
import { useAuth } from "@/context/global.context";
import { ApiServiceAdmin } from "@/service/admin-services";
import { Flex, Grid } from "@radix-ui/themes";
import { useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import styled from "styled-components";

export default function Dashboard() {
  const {
    currencyOptionsDashboard,
    user_data,
    refetchUserData /* menuAccessRoutes */,
  } = useAuth();

  const [currency, setCurrency] = useState(
    currencyOptionsDashboard?.find((itm) => itm?.label?.toLowerCase() === "gbp")
  );

  const { data: analytics, refetch } = useQuery({
    queryKey: ["GetUserAnalyticsQuery"],
    queryFn: () => ApiServiceAdmin.GetUserAnalyticsQuery(currency?.label),
  });

  const paymentTypeMonths =
    analytics?.data && Object.keys(analytics?.data?.analyticsByPaymentTypes);

  const paymentTypeAnalytics = paymentTypeMonths?.map((itm) => {
    return {
      month: itm,
      ...analytics?.data?.analyticsByPaymentTypes[itm],
    };
  });

  const transactionTypeMonths =
    analytics?.data &&
    Object.keys(analytics?.data?.analyticsByTransactionStatus);

  const transactionTypeAnalytics = transactionTypeMonths?.map((itm) => {
    return {
      month: itm,
      ...analytics?.data?.analyticsByTransactionStatus[itm],
    };
  });

  const channelTypeMonths =
    analytics?.data &&
    Object.keys(analytics?.data?.analyticsByTransactionChannel);

  const channelTypeAnalytics = channelTypeMonths?.map((itm) => {
    return {
      month: itm,
      ...analytics?.data?.analyticsByTransactionChannel[itm],
    };
  });

  useEffect(() => {
    refetch(currency?.label);
  }, [currency]);

  useEffect(() => {
    refetchUserData();
  }, []);

  /*  const check =
    menuAccessRoutes?.find((itm) => itm.menuName === "Update Rate & Fees")
      ?.menuAccessType?.id > 1
      ? true
      : false; */
  const check = user_data?.userId === 0;

  console.log(check, "check");

  return (
    <DashboardStyle>
      <SectionHeader
        title="Overview"
        description="This overview provides a comprehensive snapshot of transactions on your system"
      />
      <Grid
        columns={{ initial: "1", md: "5fr 1fr" }}
        gap="4"
        mt="40px"
        mb="20px"
      >
        <div style={{ display: "grid", gap: "20px" }}>
          <Box>
            <CurrencySelect
              options={currencyOptionsDashboard}
              setCurrency={setCurrency}
              currency={currency}
            />
            <Grid
              gap={"4"}
              columns={{ md: "1fr 1fr 1fr 1fr 1fr", initial: "2" }}
              width={"100%"}
            >
              <MainDashboardCard
                title="This Week"
                amount={currency?.thisWeekTransferAmount}
                currency={currency?.value}
                count={currency?.thisWeekTransferCount}
                difference={currency?.thisWeekandLastWeekyPercentageDifference}
                timeType="week"
                divider
              />
              <MainDashboardCard
                title="This Month"
                amount={currency?.thisMonthTransferAmount}
                currency={currency?.value}
                count={currency?.thisMonthTransferCount}
                difference={currency?.thisMonthAndLastMonthPercentageDifference}
                timeType="month"
                divider
              />
              <MainDashboardCard
                title="Current Month Extrapolation"
                amount={currency?.thisMonthExtraPolationmount}
                currency={currency?.value}
                count={currency?.thisMonthExtraPolationTransferCount}
                difference={currency?.thisMonthAndLastMonthPercentageDifference}
                timeType="month"
                divider
              />
              <MainDashboardCard
                title="This Year"
                amount={currency?.thisYearTransferAmount}
                currency={currency?.value}
                count={currency?.thisYearTransferCount}
                difference={currency?.thisYearAndLastYearPercentageDifference}
                timeType="year"
                divider
              />
              <MainDashboardCard
                title="All Time"
                amount={currency?.allTimeTransferAmount}
                currency={currency?.value}
                count={currency?.allTimeTransferCount}
              />
            </Grid>
          </Box>
        </div>
        <Grid gap={"4"}>
          <a href="#today-logs">
            <Box
              style={{
                textAlign: "center",
                height: "100%",
              }}
            >
              <h2
                style={{
                  fontSize: "50px",
                }}
              >
                {user_data.todayTransferCount}
              </h2>
              <div
                style={{
                  color: "#818181",
                }}
                className="dText"
              >
                Today{"'"}s transfer
              </div>
            </Box>
          </a>
          {!check ? (
            <Box
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                opacity: 0.3,
              }}
              padding="1rem"
            >
              <Flex gap={"2"} align={"center"}>
                {" "}
                <img
                  style={{
                    width: "20px",
                    height: "20px",
                  }}
                  src="/icons/ArrowsDownUp.png"
                />
                Send Money
              </Flex>
            </Box>
          ) : (
            <Link to="/send-money">
              <Box
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
                padding="1rem"
              >
                <Flex gap={"2"} align={"center"}>
                  {" "}
                  <img
                    style={{
                      width: "20px",
                      height: "20px",
                    }}
                    src="/icons/send.png"
                  />
                  Send Money
                </Flex>
              </Box>
            </Link>
          )}
        </Grid>
      </Grid>

      <Grid columns={{ initial: "1", md: "5fr 1fr" }} gap="4">
        <AdminBox
          style={{
            height: "100%",
          }}
          title="Today's Transactions"
        >
          <br />
          <Grid
            gap={"4"}
            columns={{ md: "1fr 1fr 1fr 1fr 1fr 1fr", initial: "2" }}
            width={"100%"}
          >
            <MainDashboardAnalysisCard
              Icon={CustomerPurpleIcon}
              title="All Transfers"
              amount={currency?.todayTransferAmount}
              currency={currency?.value}
              count={currency?.todayTransferCount}
              difference={currency?.todayandYesterdayPercentageDifference}
              divider
            />
            <MainDashboardAnalysisCard
              Icon={WalletGreenIcon}
              title="Deposited"
              amount={currency?.todayDepositedAmount}
              currency={currency?.value}
              count={currency?.todayDeposited}
              divider
            />

            <MainDashboardAnalysisCard
              Icon={CustomerPurpleIcon}
              title="Processed"
              amount={currency?.todayProcessedAmount}
              currency={currency?.value}
              count={currency?.todayProcessed}
              divider
            />

            <MainDashboardAnalysisCard
              title="Pending"
              Icon={TransactionCountYellowIcon}
              amount={currency?.todayPendingAmount}
              currency={currency?.value}
              count={currency?.todayPending}
              divider
            />

            <MainDashboardAnalysisCard
              title="On-hold"
              Icon={PendingInvoiceIcon}
              amount={currency?.todayOnHoldAmount}
              currency={currency?.value}
              count={currency?.todayOnHold}
              divider
            />

            <MainDashboardAnalysisCard
              title="Cancelled"
              Icon={CancelledIcon}
              amount={currency?.todayCancelledAmount}
              currency={currency?.value}
              count={currency?.todayCancelled}
            />
          </Grid>
        </AdminBox>
        <Grid gap={"4"}>
          {!check ? (
            <Box
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                opacity: 0.3,
              }}
              padding="1rem"
            >
              <Flex gap={"2"} align={"center"}>
                {" "}
                <img
                  style={{
                    width: "20px",
                    height: "20px",
                  }}
                  src="/icons/ArrowsDownUp.png"
                />
                View Rates
              </Flex>
            </Box>
          ) : (
            <Link to="/update-rate-&-fees">
              <Box
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
                padding="1rem"
              >
                <Flex gap={"2"} align={"center"}>
                  {" "}
                  <img
                    style={{
                      width: "20px",
                      height: "20px",
                    }}
                    src="/icons/ArrowsDownUp.png"
                  />
                  View Rates
                </Flex>
              </Box>
            </Link>
          )}

          {!check ? (
            <Box
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                opacity: 0.3,
              }}
              padding="1rem"
            >
              <Flex gap={"2"} align={"center"}>
                {" "}
                <img
                  style={{
                    width: "20px",
                    height: "20px",
                  }}
                  src="/icons/ArrowsDownUp.png"
                />
                View Transfers
              </Flex>
            </Box>
          ) : (
            <Link to="/view-transfers">
              <Box
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
                padding="1rem"
              >
                <Flex gap={"2"} align={"center"}>
                  {" "}
                  <img
                    style={{
                      width: "20px",
                      height: "20px",
                    }}
                    src="/icons/Clock.png"
                  />
                  View Transfers
                </Flex>
              </Box>
            </Link>
          )}

          {!check ? (
            <Box
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                opacity: 0.3,
              }}
              padding="1rem"
            >
              <Flex gap={"2"} align={"center"}>
                {" "}
                <img
                  style={{
                    width: "20px",
                    height: "20px",
                  }}
                  src="/icons/ArrowsDownUp.png"
                />
                Manage Customers
              </Flex>
            </Box>
          ) : (
            <Link to="/customers">
              <Box
                padding="1rem"
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <Flex gap={"2"} align={"center"}>
                  {" "}
                  <img
                    style={{
                      width: "20px",
                      height: "20px",
                    }}
                    src="/icons/users.png"
                  />
                  Manage Customers
                </Flex>
              </Box>
            </Link>
          )}

          {!check ? (
            <Box
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                opacity: 0.3,
              }}
              padding="1rem"
            >
              <Flex gap={"2"} align={"center"}>
                {" "}
                <img
                  style={{
                    width: "20px",
                    height: "20px",
                  }}
                  src="/icons/ArrowsDownUp.png"
                />
                {user_data?.role?.name === "Agent"
                  ? "View Profile"
                  : "Manage Agents"}
              </Flex>
            </Box>
          ) : (
            <Link
              to={
                user_data?.role?.name === "Agent"
                  ? `/agents/${user_data?.userId}?tab=overview`
                  : "/agents"
              }
            >
              <Box
                padding="1rem"
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <Flex gap={"2"} align={"center"}>
                  {" "}
                  <img
                    style={{
                      width: "20px",
                      height: "20px",
                    }}
                    src="/icons/users.png"
                  />
                  {user_data?.role?.name === "Agent"
                    ? "View Profile"
                    : "Manage Agents"}
                </Flex>
              </Box>
            </Link>
          )}
        </Grid>
      </Grid>
      <br />
      <div id="today-logs">
        <TodayLogs />
      </div>
      <br />
      <FundRequestLog />
      <br />
      <PaymentTypeChart
        data={paymentTypeAnalytics}
        currency={currency?.label}
      />
      <br />
      <Grid columns={{ initial: "1", md: "2" }} gap="4">
        <TransactionsTypeChart
          data={transactionTypeAnalytics}
          currency={currency?.label}
        />
        <CustomerTypeChart
          data={channelTypeAnalytics}
          currency={currency?.label}
        />
      </Grid>
      <br />
      <NewCustomerList />
    </DashboardStyle>
  );
}

const DashboardStyle = styled.div`
  color: #000;
`;
