import AppLink from "@/components/bits/AppLink";
import { CurrencyBadge } from "@/components/bits/CurrencyBadge";
import CustomTable from "@/components/bits/CustomTable";
import { ProviderBadge } from "@/components/bits/ProviderBadge";
//import MiniNavBar from "@/components/bits/MiniNavBar";
import SectionHeader from "@/components/bits/SectionHeader";
import { StatusBadge } from "@/components/bits/StatusBadge";
import SwitchInput from "@/components/bits/SwitchInput";
import { handleGetItm } from "@/lib/utils";
import UpdatePayoutProviderModal from "@/Modals/UpdatePayoutProviderModal";
//import { handleGetItm } from "@/lib/utils";
import { ApiServiceAdmin } from "@/service/admin-services";
//import { handleSetItem } from "@/lib/utils";
import { Flex } from "@radix-ui/themes";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useState } from "react";
//import { useNavigate } from "react-router-dom";
import styled from "styled-components";

export default function PayoutProvidersPage() {
  const [open, setOpen] = useState(false);
  const [item, setItem] = useState();
  const { data, isFetching, refetch } = useQuery({
    queryKey: ["GetPayoutProviderQueryTable"],
    queryFn: () => ApiServiceAdmin.GetPayoutProviderQuery(),
  });

  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.TogglePayoutProviderMutation,
    onSuccess: () => {
      refetch();
    },
    onError: () => {
      return;
    },
  });

  const columns = [
    {
      title: "action",
      dataIndex: "id",
      fixed: "left",
      width: 160,
      render: (e) => (
        <div
          onClick={() => {
            console.log(e);
            setItem(handleGetItm(e, "id", data?.data));
            setOpen(true);
          }}
        >
          <AppLink type="norml"> Update Provider</AppLink>
        </div>
      ),
    },
    {
      title: "id",
      dataIndex: "id",
      width: 200,
    },
    {
      title: "payout processor",
      dataIndex: "name",
      render: (e, render) => (
        <ProviderBadge name={e} logo={render.logo} fontSize="12px" />
      ),
      width: 200,
    },
    {
      title: "description",
      dataIndex: "description",
      width: 200,
    },
    {
      title: "currency",
      dataIndex: "payOutProviderSupportedCurrency",
      render: (e) => {
        return (
          <Flex gap={"4"}>
            {e?.map((itm) => {
              return (
                <div
                  style={{
                    marginBottom: "5px",
                  }}
                >
                  <CurrencyBadge currency={itm?.code} />
                </div>
              );
            })}
          </Flex>
        );
      },
      width: 300,
    },
    {
      title: "status",
      dataIndex: "status",
      width: 200,
      render: (e) => <StatusBadge status={e ? "active" : "inactive"} />,
    },
    {
      title: "Date Created",
      dataIndex: "dateCreated",
      width: 200,
    },
    {
      title: "make active",
      dataIndex: "status",
      width: 200,
      render: (e, record) => (
        <SwitchInput
          onChange={() => {
            if (e) {
              mutate({
                objectId: record?.id, //Leave it as 0.
                action: 0, // 0 to deactivate, 1 to acctivate
                payOutClientWalletProviderId: record?.id, //Payout channel ID.
              });
            } else {
              mutate({
                objectId: record?.id, //Leave it as 0.
                action: 1, // 0 to deactivate, 1 to acctivate
                payOutClientWalletProviderId: record?.id, //Payout channel ID.
              });
            }
          }}
          checked={e}
        />
      ),
    },
  ];

  return (
    <DashboardStyle>
      {open && (
        <UpdatePayoutProviderModal
          setOpen={setOpen}
          open={open}
          finished={refetch}
          data={item}
        />
      )}
      <Flex
        align={{ initial: "flex-start", md: "center" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "flex-start", md: "between" }}
      >
        <SectionHeader
          title="Payout Providers"
          description={`This page allows you manage payout providers`}
        />
      </Flex>
      &nbsp;
      <CustomTable
        tableColumns={columns}
        arrayData={data?.data}
        loading={isPending || isFetching}
      />
    </DashboardStyle>
  );
}

const DashboardStyle = styled.div`
  color: #000;
`;
