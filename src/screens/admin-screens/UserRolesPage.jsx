import CustomTable from "@/components/bits/CustomTable";
//import MiniNavBar from "@/components/bits/MiniNavBar";
import SectionHeader from "@/components/bits/SectionHeader";
import { StatusBadge } from "@/components/bits/StatusBadge";
//import { handleGetItm } from "@/lib/utils";
import { ApiServiceAdmin } from "@/service/admin-services";
//import { handleSetItem } from "@/lib/utils";
import { Flex } from "@radix-ui/themes";
import { useQuery } from "@tanstack/react-query";
//import { useNavigate } from "react-router-dom";
import styled from "styled-components";

export default function UserRolesPage() {
  const { data } = useQuery({
    queryKey: ["GetUserRolesQuery"],
    queryFn: () => ApiServiceAdmin.GetUserRolesQuery(),
  });

  //const navigate = useNavigate();
  const columns = [
    {
      title: "role name",
      dataIndex: "name",
    },
    {
      title: "status",
      dataIndex: "status",
      render: (e) => <StatusBadge status={e ? "true" : "false"} />,
    },
  ];

  return (
    <DashboardStyle>
      <Flex
        align={{ initial: "flex-start", md: "center" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "flex-start", md: "between" }}
      >
        <SectionHeader
          title="User Roles"
          description={`This page allows you to manage all user roles`}
        />
      </Flex>
      &nbsp;
      <CustomTable tableColumns={columns} arrayData={data?.data} />
    </DashboardStyle>
  );
}

const DashboardStyle = styled.div`
  color: #000;
`;
