import AppButton from "@/components/bits/AppButton";
import CustomTable from "@/components/bits/CustomTable";
import FormInput from "@/components/bits/FormInput";
import FormInputDate from "@/components/bits/FormInputDate";
import SectionHeader from "@/components/bits/SectionHeader";
import { Flex } from "@radix-ui/themes";
import { LucideDownload, Search } from "lucide-react";
import styled from "styled-components";

export default function AuditTrailPage() {
  return (
    <DashboardStyle>
      <Flex
        align={{ initial: "flex-start", md: "center" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "flex-start", md: "between" }}
      >
        <SectionHeader
          title="Audit Trail"
          description="Monitor any changes made on the system"
        />
        <AppButton
          placeholder="Export CSV"
          width="160px"
          IconLeft={LucideDownload}
        />
      </Flex>
      &nbsp;
      <CustomTable
        topContent={
          <>
            <Flex
              align={{ initial: "flex-start", md: "center" }}
              direction={{ initial: "column", md: "row" }}
              justify={{ initial: "flex-start", md: "between" }}
            >
              <FormInput
                placeholder="Filter by Transaction ID, Amount, Date, e.t.c"
                name="labels"
                width="50%"
                IconLeft={Search}
              />
              <Flex
                align={{ initial: "flex-start", md: "center" }}
                direction={{ initial: "column", md: "row" }}
                gap="2"
              >
                <FormInputDate placeholder="Select Date" width="160px" />
              </Flex>
            </Flex>
          </>
        }
      />
    </DashboardStyle>
  );
}

const DashboardStyle = styled.div`
  color: #000;
`;
