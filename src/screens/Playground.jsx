import AppButton from "@/components/bits/AppButton";
import CustomTable from "@/components/bits/CustomTable";
import FilterTab from "@/components/bits/FilterTab";
import FormInput from "@/components/bits/FormInput";
import FormInputDate from "@/components/bits/FormInputDate";
import MainSelect from "@/components/bits/MainSelect";
import MiniNavBar from "@/components/bits/MiniNavBar";
import SectionHeader from "@/components/bits/SectionHeader";
import ConfirmModal from "@/Modals/ConfirmModal";
import SuccessModal from "@/Modals/SuccessModal";
import { DatePicker } from "@arco-design/web-react";
import { Grid } from "@radix-ui/themes";
import { Calendar } from "lucide-react";
import styled from "styled-components";

export default function Playground() {
  return (
    <DashboardStyle>
      <Grid columns={"2"}>
        <FormInputDate label={"Custom Range"} placeholder="Custom range" />
        <FormInput
          label="Label"
          placeholder="Label"
          formik={{ errors: { labels: "Label is required." } }}
          name="labels"
          IconRight={Calendar}
        />
      </Grid>

      <SectionHeader />
      <SectionHeader />
      <SectionHeader />
      <MiniNavBar
        tab={[
          { name: "Transfer To Whitecrust", tab: "transfer" },
          { name: "Transfer To Other Banks", tab: "other" },
          { name: "Self-Account Transfer", tab: "self" },
        ]}
      />
      <br />
      <FilterTab
        tab={[
          { name: "Today", tab: "today" },
          { name: "Last 7 days", tab: "last7" },
          { name: "Last 30 days", tab: "last30" },
          { name: "Last 90 days", tab: "last90" },
        ]}
      />
      <FormInput
        label="Label"
        placeholder="Label"
        formik={{ errors: { labels: "Label is required." } }}
        name="labels"
        IconRight={Calendar}
      />
      <MainSelect
        label="Main Select"
        placeholder="Main Select"
        formik={{ errors: { mainSel: "Main Select is required." } }}
        name="mainSel"
        options={[
          { label: "dddd", value: "kkkk" },
          { label: "dddccd", value: "kkrrkk" },
          { label: "drtddd", value: "kkwwkk" },
        ]}
      />

      <br />
      <AppButton
        placeholder="Last 7 days"
        width="fit-content"
        outline
        loading={false}
        IconLeft={Calendar}
        disabled={false}
      />
      <br />
      <CustomTable />

      <DatePicker.RangePicker className="range" position="br" />

      <SuccessModal
        open={false}
        description="You have successfully added a new sub account."
      />
      <ConfirmModal
        open={false}
        description="You are about to reactivate this payment link. Are you sure?."
      />
    </DashboardStyle>
  );
}

const DashboardStyle = styled.div`
  color: #000;
`;
