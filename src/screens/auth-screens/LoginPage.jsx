import AppButton from "@/components/bits/AppButton";
import FormInput from "@/components/bits/FormInput";
import FormInputPassword from "@/components/bits/FormInputPassword";
import SuccessModal from "@/Modals/SuccessModal";
import { LoginSchema } from "@/Schema";
import { ApiServiceAuth } from "@/service/auth-services";
import { useMutation } from "@tanstack/react-query";
import { useFormik } from "formik";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

export default function LoginPage() {
  const [success, setSuccess] = useState(false);
  const navigate = useNavigate();

  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAuth.LoginMutation,
    onSuccess: (data) => {
      const user = data?.data;
      //
      const filterAccess = user?.userRoleMenuAccess?.filter(
        (itm) => itm?.menuAccessType?.id >= 2
      );
      const filterAccessSub = filterAccess[0]?.userRoleSubMenuAccess?.filter(
        (itm) => itm?.menuAccessType?.id >= 2
      );
      //
      const hasSubMenus = filterAccessSub?.length;
      //
      const pickHas = filterAccessSub?.[0]?.subMenuName;
      const pickMain = filterAccess?.[0]?.menuName;
      //
      const firstRoute = hasSubMenus
        ? pickHas?.replace(/ /gu, "-")?.toLowerCase()
        : pickMain?.replace(/ /gu, "-")?.toLowerCase();
      window.location.pathname = firstRoute;
    },
    onError: () => {
      return;
    },
  });

  const formik = useFormik({
    initialValues: {
      username: "",
      password: "",
    },
    validationSchema: LoginSchema,
    onSubmit: (values) => {
      mutate(values);
    },
  });

  return (
    <div>
      <SuccessModal
        padding={"60px"}
        open={success}
        finish={() => {
          setSuccess(false);
        }}
        description={
          "Please click on the link sent to your email address to reset password."
        }
        title="Verify your email"
      />
      <FormInput
        label="Email Address"
        placeholder="Enter your email address"
        width="100%"
        name="username"
        disabled={isPending}
        formik={formik}
      />
      <FormInputPassword
        label="Password"
        placeholder="Enter your Password"
        name="password"
        width="100%"
        disabled={isPending}
        formik={formik}
      />
      &nbsp;
      <div>
        Forgot Password?{" "}
        <span
          style={{
            cursor: "pointer",
            color: "#FF6500",
            fontWeight: "600",
          }}
          onClick={() => {
            navigate("/forgot-password");
          }}
        >
          Reset
        </span>
      </div>
      &nbsp;
      <AppButton
        onClick={() => {
          formik.handleSubmit();
        }}
        textColor="#fff"
        placeholder="Sign in"
        loading={isPending}
        disabled={isPending || !formik.isValid}
      />
    </div>
  );
}
