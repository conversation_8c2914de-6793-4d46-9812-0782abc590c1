import AppButton from "@/components/bits/AppButton";
import AuthFooter from "@/components/bits/AuthFooter";
import FormInput from "@/components/bits/FormInput";
import { ApiServiceAuth } from "@/service/auth-services";
import { useMutation } from "@tanstack/react-query";
import { useFormik } from "formik";
import { useNavigate } from "react-router-dom";

import * as Yup from "yup";
const Schema = Yup.object().shape({
  email: Yup.string().required("Email is required"),
});

export default function CreatePasswordPage() {
  const navigate = useNavigate();
  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAuth.ResetPasswordMutation,
    onSuccess: (data) => {
      if (data.message !== "User does not exist") navigate("/");
    },
    onError: () => {
      return;
    },
  });

  const formik = useFormik({
    initialValues: {
      email: "",
    },
    validationSchema: Schema,
    onSubmit: (values) => {
      mutate(values);
    },
  });

  return (
    <div>
      <FormInput
        label="Email Address"
        placeholder="Enter your email address"
        width="100%"
        name="email"
        disabled={isPending}
        formik={formik}
      />
      &nbsp;
      <AppButton
        textColor="#fff"
        placeholder="Proceed"
        loading={isPending}
        onClick={() => {
          formik.handleSubmit();
        }}
        disabled={isPending}
      />
      <AuthFooter
        desc={"Already have a account? "}
        to={"/"}
        toName={"Sign in"}
      />
    </div>
  );
}
