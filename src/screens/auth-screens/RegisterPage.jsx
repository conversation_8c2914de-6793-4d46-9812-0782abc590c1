import AppButton from "@/components/bits/AppButton";
import AuthFooter from "@/components/bits/AuthFooter";
import FormInput from "@/components/bits/FormInput";
import MainSelect from "@/components/bits/MainSelect";

export default function RegisterPage() {
  return (
    <div>
      <MainSelect
        label="Account type"
        placeholder="Select account type"
        name="mainSel"
        options={[
          { label: "dddd", value: "dddd" },
          { label: "dddccd", value: "dddccd" },
          { label: "drtddd", value: "drtddd" },
        ]}
      />
      <FormInput
        label="Business Name"
        placeholder="Enter your business name"
        name="labels"
        width="100%"
      />
      <FormInput
        label="Email Address"
        placeholder="Enter your email address"
        name="labels"
        width="100%"
      />
      <FormInput
        label="First Name"
        placeholder="Enter your first name"
        name="labels"
        width="100%"
      />
      <FormInput
        label="Last Name"
        placeholder="Enter your last name"
        name="labels"
        width="100%"
      />
      &nbsp;
      <AppButton
        textColor="#fff"
        placeholder="Proceed"
        to={"/create-password"}
        loading={false}
        disabled={false}
      />
      <AuthFooter
        desc={"Already have a account? "}
        to={"/"}
        toName={"Sign in"}
      />
    </div>
  );
}
