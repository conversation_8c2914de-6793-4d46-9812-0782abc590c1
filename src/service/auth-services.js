import { Axios } from "./axios-config";

export const ApiServiceAuth = {
  /* AUTH APIS */
  /*  loginMutation: async (body) => {
         const { data } = await Axios.post(`auth/login`, {
             requestType: "inbound",
             data: body,
         });
         localStorage.setItem("token", data?.data?.token);
         localStorage.setItem("user_data", JSON.stringify(data?.data?.userData));
         return data;
     }, */
  LoginMutation: async (body) => {
    const { data } = await Axios.post(`auth`, {
      ...body,
      deviceId: "Tets",
      source: "Web",
    });
    localStorage.setItem("user_id", data?.data?.userId || 0);
    localStorage.setItem("user_data", JSON.stringify(data?.data));
    console.log(data);
    return data;
  },
  LoginBusinessMutation: async (body) => {
    const { data } = await Axios.post(`user/refresh-token`, body);
    localStorage.setItem("token", data?.data?.token);
    console.log(data);
    return data;
  },

  RegisterMutation: async (body) => {
    const { data } = await Axios.post(`auth/register`, body);
    return data;
  },
  VerifyMutation: async (body) => {
    const { data } = await Axios.post(`auth/verification`, body);
    return data;
  },
  ForgotPasswordMutation: async (body) => {
    const { data } = await Axios.post(`auth/forgot-password`, body);
    return data;
  },
  ResetPasswordMutation: async (body) => {
    const { data } = await Axios.post(`InitiateForgotPassword`, body);
    return data;
  },
  ResendOtpMutation: async (body) => {
    const { data } = await Axios.post(`auth/resend-otp`, body);
    return data;
  },
};
