import { Axios } from "./axios-config";

/* const query = new URLSearchParams(window.location.search);

const trxId = query.get("trxId");
const status = query.get("status");
const approved = query.get("approved"); */

export const ApiServiceAdmin = {
  GetUserQuery: async (body) => {
    const { data } = await Axios.get(`getuserdashboard/${body}`);
    localStorage.setItem("user_data", JSON.stringify(data?.data));
    return data;
  },
  GetCustomerQuery: async (body) => {
    const { data } = await Axios.get(`getuserdashboard/${body}`);
    return data;
  },
  GetUserAnalyticsQuery: async (body) => {
    const { data } = await Axios.get(`analytics/${body}`);
    return data;
  },
  GetCurrencyQuery: async () => {
    const { data } = await Axios.get(`getcurrency`);
    return data;
  },
  GetTodaysTransferLogsQuery: async (body) => {
    const { data } = await Axios.get(`gettodaylog/${body}`);
    return data;
  },
  GetTransferListQuery: async (body) => {
    const { data } = await Axios.get(`getusertransactionlog/${body}`);
    return data;
  },
  GetTransferListDetailsQuery: async (body) => {
    const { data } = await Axios.get(`transaction.do?transactionId=${body}`);
    return data;
  },

  GetOtherTransferQuery: async ({ id, body }) => {
    const { data } = await Axios.get(`getusertransactionlog/${id}`, {
      params: body,
    });
    return data;
  },
  SubmitTransferMutation: async (body) => {
    const { data } = await Axios.get(`bosmcc/${body}`);
    return data;
  },
  AddCustomerMutation: async (body) => {
    const { data } = await Axios.post(`signup`, body);
    return data;
  },
  UpdateUserDetailsMutation: async (body) => {
    const { data } = await Axios.post(`updateuserprofile`, body);
    return data;
  },
  GetCustomersQuery: async () => {
    const { data } = await Axios.get(`getuserbyrole/6`);
    return data;
  },
  GetAgentCustomersQuery: async (id) => {
    const { data } = await Axios.get(`getagentcustomers/${id}`);
    return data;
  },
  GetImcompleteCustomersQuery: async () => {
    const { data } = await Axios.get(`getincompleteusersbyrole/6`);
    return data;
  },
  GetActionCustomersQuery: async () => {
    const { data } = await Axios.get(`getactionrequiredusersbyrole/6`);
    return data;
  },
  ValidatePhone: async (body) => {
    const { data } = await Axios.get(`validateuserphonenumber/${body}`);
    return data;
  },
  DisallowMultiCurrencyMutation: async (body) => {
    const { data } = await Axios.post(`disallowusermulticurrency/${body}`);
    return data;
  },
  AllowMultiCurrencyMutation: async (body) => {
    const { data } = await Axios.post(`allowusermulticurrency/${body}`);
    return data;
  },

  DisallowCreditRequestMutation: async (body) => {
    const { data } = await Axios.post(`disallowcreditrequest/${body}`);
    return data;
  },

  AllowCreditRequestMutation: async (body) => {
    const { data } = await Axios.post(`allowcreditrequest/${body}`);
    return data;
  },

  UpdateSpecialRateMutation: async (body) => {
    const { data } = await Axios.post(`updatecustomerspeacialrate`, body);
    return data;
  },
  EditUserDocumentMutation: async (body) => {
    const { data } = await Axios.post(`updateuserkycdocument`, body);
    return data;
  },
  UpdateUserPasswordMutation: async (body) => {
    const { data } = await Axios.post(`updateuserpassword`, body);
    return data;
  },
  AddUserDocumentMutation: async (body) => {
    const { data } = await Axios.post(`adduserkycdocument`, body);
    return data;
  },
  FundCustomerWalletMutation: async (body) => {
    const { data } = await Axios.post(`funduserwallet`, body);
    return data;
  },

  MessageCustomerMutation: async (body) => {
    const { data } = await Axios.post(`messagecustomer `, body);
    return data;
  },
  MessageCustomerByRoleMutation: async (body) => {
    const { data } = await Axios.post(`messagecustomerbyrole `, body);
    return data;
  },

  //Client

  GetChargeTypesQuery: async () => {
    const { data } = await Axios.get(`getpayoutchargetypes`);
    return data;
  },
  GetTransactionTypeQuery: async () => {
    const { data } = await Axios.get(`getpayoutclienttransactiontype`);
    return data;
  },
  FundClientWalletMutation: async (body) => {
    const { data } = await Axios.post(`walletfundingrequest`, body);

    return data;
  },
  UpdateClientFileMutation: async (body) => {
    const { data } = await Axios.post(`updatepayoutclientfile`, body);
    return data;
  },
  ToggleClientMutation: async (body) => {
    const { data } = await Axios.post(`togglepayoutclientaccount`, body);

    return data;
  },
  AddClientChargesMutation: async (body) => {
    const { data } = await Axios.post(`addpayoutclientcharges`, body);

    return data;
  },
  UpdateClientChargesMutation: async (body) => {
    const { data } = await Axios.post(`updatepayoutclientcharges`, body);
    return data;
  },
  ActivateCustomerMutation: async (body) => {
    const { data } = await Axios.post(`reactivateaccount`, body);
    return data;
  },
  DeactivateCustomerMutation: async (body) => {
    const { data } = await Axios.post(`deactivateaccount`, body);
    return data;
  },
  SuspendCustomerMutation: async (body) => {
    const { data } = await Axios.post(`suspendaccount`, body);
    return data;
  },
  AddCommentToIDMutation: async (body) => {
    const { data } = await Axios.post(`addcommenttouserkycdocument`, body);
    return data;
  },
  MarkIdAsVerifiedMutation: async (body) => {
    const { data } = await Axios.post(`markuseridasverified`, body);
    return data;
  },
  UnsuspendCustomerMutation: async (body) => {
    const { data } = await Axios.post(`reactivateaccount`, body);
    return data;
  },
  WatchCustomerMutation: async (body) => {
    const { data } = await Axios.post(`uppdateaccountwatchliststatus`, body);
    return data;
  },
  FileUploadMutation: async (body) => {
    const { data } = await Axios.post(`FileUploadAPI/${body?.id}`, body?.body);
    return data;
  },

  GetUserDocumentTypesQuery: async () => {
    const { data } = await Axios.get(`getuserdocumentstypes`);
    return data;
  },

  GetAgentsQuery: async () => {
    const { data } = await Axios.get(`getuserbyrole/5`);
    return data;
  },

  SendAgentInviteMutation: async (body) => {
    const { data } = await Axios.post(`sendagentinvite`, body);
    return data;
  },
  GetPayoutPartnerQuery: async () => {
    const { data } = await Axios.get(`getpayoutpartner`);
    return data;
  },
  GetPayoutPartnerLogsQuery: async (body) => {
    const { data } = await Axios.get(`getpayoutlogbypayoutpartner`, {
      params: body,
    });
    return data;
  },
  GetPayoutPartnerGatewaysQuery: async (body) => {
    const { data } = await Axios.get(`getpayoutpartnergateways/${body}`);
    return data;
  },

  GetPayoutClientDashboard: async (body) => {
    const { data } = await Axios.get(`getpayoutclientdashboard/${body}`);
    return data;
  },
  GetAgentInviteQuery: async () => {
    const { data } = await Axios.get(`getagentinvite`);
    return data;
  },
  GetBeneficiaryQuery: async (body) => {
    const { data } = await Axios.get(`getuserbeneficiaries`, { params: body });
    return data;
  },
  AddBeneficiaryMutation: async (body) => {
    const { data } = await Axios.post(`adduserbeneficiary`, body);
    return data;
  },
  DeleteBeneficiaryMutation: async (body) => {
    const { data } = await Axios.post(`deleteuserbeneficiary/${body}`);
    return data;
  },
  GetCurrencyRateMetadataQuery: async (body) => {
    const { data } = await Axios.get(`getuseraccountcategory`, {
      params: body,
    });
    return data;
  },
  ProcessClientFundRequestMutation: async (body) => {
    const { data } = await Axios.post(
      `payoutprocesswalletfundingrequest`,
      body
    );
    return data;
  },

  ApproveWithdrawalRequestMutation: async (body) => {
    const { data } = await Axios.post(`processuserwithdrawalrequest`, body);
    return data;
  },
  DeclineWithdrawalRequestMutation: async (body) => {
    const { data } = await Axios.post(`processuserwithdrawalrequest`, body);
    return data;
  },
  AddNewCurrencyRateMetadataMutation: async (body) => {
    const { data } = await Axios.post(`addnewcurrencyratemetadata`, body);
    return data;
  },
  UpdateCurrencyRateMetadataMutation: async (body) => {
    const { data } = await Axios.post(`updatecurrencyratemetadata`, body);
    return data;
  },

  ToggleCurrencyRateMutation: async (body) => {
    const { data } = await Axios.post(`togglecurrencyrateconversion`, body);
    return data;
  },

  AddNewRateMutation: async (body) => {
    const { data } = await Axios.post(`addnewrate`, body);
    return data;
  },
  UpdateRateMutation: async (body) => {
    const { data } = await Axios.post(`updaterate`, body);
    return data;
  },

  GetRateLogsQuery: async () => {
    const { data } = await Axios.get(`getratelogs`);
    return data;
  },
  GetAgentRatesQuery: async (body) => {
    const { data } = await Axios.get(`agentgetrate`, {
      params: body,
    });
    return data;
  },
  GetAllRatesQuery: async () => {
    const { data } = await Axios.get(`getallrates`);
    return data;
  },
  //Send Money

  SendMoneyMutation: async (body) => {
    const { data } = await Axios.post(`sendmoneyviabackoffice`, body);
    return data;
  },

  GetTransferPurposeQuery: async () => {
    const { data } = await Axios.get(`gettransferpurpose`);
    return data;
  },
  //Transaction Processors

  GetPaymentChannelProcessorsQuery: async () => {
    const { data } = await Axios.get(`getpaymentchannelprocessor`);
    return data;
  },
  GetPayoutChannelProcessorsQuery: async () => {
    const { data } = await Axios.get(`getpayoutchannelprocessor`);
    return data;
  },

  GetPaymentProviderQuery: async () => {
    const { data } = await Axios.get(`getpaymentprovider`);
    return data;
  },
  GetPaymentChannelQuery: async () => {
    const { data } = await Axios.get(`getpaymentchannel`);
    return data;
  },

  GetPayoutProviderQuery: async () => {
    const { data } = await Axios.get(`getpayoutprovider`);
    return data;
  },
  GetPayoutChannelQuery: async () => {
    const { data } = await Axios.get(`getpayoutchannel`);
    return data;
  },

  AddPaymentProcessorMutation: async (body) => {
    const { data } = await Axios.post(`addpaymentchannelprocessor`, body);
    return data;
  },

  UpdatePaymentProcessorMutation: async (body) => {
    const { data } = await Axios.post(`updatepaymentchannelprocessor`, body);
    return data;
  },

  TogglePaymentProcessorMutation: async (body) => {
    const { data } = await Axios.post(`togglepaymentchannelprovider`, body);
    return data;
  },
  TogglePaymentChannelMutation: async (body) => {
    const { data } = await Axios.post(`togglepaymentchannel`, body);
    return data;
  },
  TogglePaymentProviderMutation: async (body) => {
    const { data } = await Axios.post(`togglepaymentwalletprovider`, body);
    return data;
  },

  TogglePayoutChannelMutation: async (body) => {
    const { data } = await Axios.post(`togglepayoutchannel`, body);
    return data;
  },

  TogglePayoutProviderMutation: async (body) => {
    const { data } = await Axios.post(`togglepayoutwalletprovider`, body);
    return data;
  },

  TogglePayoutClientProviderMutation: async (body) => {
    const { data } = await Axios.post(`togglepayoutclientwalletprovider`, body);
    /* payOutClientWalletProviderId */
    return data;
  },
  AddPayoutProcessorMutation: async (body) => {
    const { data } = await Axios.post(`addpayoutchannelprocessor`, body);
    return data;
  },

  UpdatePayoutProcessorMutation: async (body) => {
    const { data } = await Axios.post(`updatepayoutchannelprocessor`, body);
    return data;
  },
  UpdatePayoutProviderMutation: async (body) => {
    const { data } = await Axios.post(
      `updatepayoutprovidersupportcurrencies`,
      body
    );
    return data;
  },

  UpdatePaymentProviderMutation: async (body) => {
    const { data } = await Axios.post(
      `updatepaymentprovidersupportcurrencies`,
      body
    );
    return data;
  },

  TogglePayoutProcessorMutation: async (body) => {
    const { data } = await Axios.post(`togglepayoutchannelprovider`, body);
    return data;
  },

  //Masters
  GetBanksQuery: async () => {
    const { data } = await Axios.get(`getbanks`);
    return data;
  },
  NameEnquiry: async (body) => {
    const { data } = await Axios.get(`BankDetailsLookUp`, {
      params: body,
    });
    return data;
  },
  GetCompanyBanksQuery: async () => {
    const { data } = await Axios.get(`getsystemofflinepaymentbanks`);
    return data;
  },
  AddCompanyBankMutation: async (body) => {
    const { data } = await Axios.post(`addsystemofflinepaymentbank`, body);

    return data;
  },
  UpdateCompanyBankMutation: async (body) => {
    const { data } = await Axios.post(`updatesystemofflinepaymentbank`, body);

    return data;
  },
  GetCountriesQuery: async () => {
    const { data } = await Axios.get(`getcountries`);
    return data;
  },
  GetEmploymentStatusQuery: async () => {
    const { data } = await Axios.get(`getemploymentstatus`);
    return data;
  },

  GetAccessRolesQuery: async () => {
    const { data } = await Axios.get(`getroleswithuseraccess/0`);
    return data;
  },
  GetCitiesQuery: async (body) => {
    const { data } = await Axios.get(`getcities`, {
      params: {
        countryId: body,
        citiId: 0,
      },
    });
    return data;
  },

  ToggleAccessMenuMutation: async (body) => {
    const { data } = await Axios.post(`updateuserrolemenuaccess`, body);
    return data;
  },
  ToggleSubAccessMenuMutation: async (body) => {
    const { data } = await Axios.post(`updateuserrolesubmenuaccess`, body);
    return data;
  },

  //Transaction APIS
  GetTransactionDetailsQuery: async (body) => {
    const { data } = await Axios.get(`viewtransactiondetails/${body}`);
    return data;
  },
  GetTransactionCommentsQuery: async (body) => {
    const { data } = await Axios.get(`viewtransactiondetails/${body}`);
    return data;
  },
  MarkTransactionAsSuspiciousMutation: async ({ id, body }) => {
    const { data } = await Axios.post(`marktransactionsuspicious/${id}`, body);
    return data;
  },
  CancelTransactionMutation: async ({ id, body }) => {
    const { data } = await Axios.post(`canceltransaction/${id}`, body);
    return data;
  },
  AddCommentToTransactionMutation: async (body) => {
    const { data } = await Axios.post(`addcommenttotransaction`, body);
    return data;
  },
  ConfirmTransactionMutation: async ({ id, body }) => {
    const { data } = await Axios.post(`confirmtransactionpayment/${id}`, body);
    return data;
  },
  HoldTransactionMutation: async ({ id, body }) => {
    const { data } = await Axios.post(`holdtransaction/${id}`, body);
    return data;
  },
  RevertHoldTransactionMutation: async ({ id, body }) => {
    const { data } = await Axios.post(`revertholdtransaction/${id}`, body);
    return data;
  },

  RevertTransactionMutation: async ({ id, body }) => {
    const { data } = await Axios.post(`revertpayment/${id}`, body);
    return data;
  },
  PayTransactionMutation: async ({ id, body }) => {
    const { data } = await Axios.post(`paytransaction/${id}`, body);
    return data;
  },
  ReleaseTransactionMutation: async ({ id, body }) => {
    const { data } = await Axios.post(`releasetransaction/${id}`, body);
    return data;
  },
  MarkAsPaidTransactionMutation: async ({ id, body }) => {
    const { data } = await Axios.post(`markaspaid/${id}`, body);
    return data;
  },

  ViewTransactionCommentsQuery: async (body) => {
    const { data } = await Axios.get(`viewtransactioncomment/${body}`);
    return data;
  },
  MarkAsReleasedTransactionMutation: async ({ id, body }) => {
    const { data } = await Axios.post(`markasreleased/${id}`, body);
    return data;
  },
  /* ReleaseTransactionMutation: async ({ id, body }) => {
    const { data } = await Axios.post(`marktransactionsuspicious/${id}`, body);
    return data;
  },
  */

  //Master Employee
  GetUserRolesQuery: async () => {
    const { data } = await Axios.get(`getroles`);
    return data;
  },
  GetKycProviderQuery: async () => {
    const { data } = await Axios.get(`getkycprovider`);
    return data;
  },
  GetProfessionQuery: async () => {
    const { data } = await Axios.get(`getprofession`);
    return data;
  },
  GetEmployeeQuery: async () => {
    const { data } = await Axios.get(`getemployees`);
    return data;
  },

  AddProfessionMutation: async (body) => {
    const { data } = await Axios.post(`addprofession`, body);
    return data;
  },
  ChangeKycProviderMutation: async (body) => {
    const { data } = await Axios.post(`activatekycprovider/${body}`);
    return data;
  },

  AddEmployeeMutation: async (body) => {
    const { data } = await Axios.post(`addemployee`, body);
    return data;
  },

  UpdateEmployeeMutation: async (body) => {
    const { data } = await Axios.post(`updateemployeeprofile`, body);
    return data;
  },

  EditProfessionMutation: async (body) => {
    const { data } = await Axios.post(`updateprofession`, body);
    return data;
  },
  //Others

  GetRateQuery: async (body) => {
    const { data } = await Axios.get(`getrate`, {
      params: body,
    });
    return data;
  },
  GetSystemResourcesQuery: async () => {
    const { data } = await Axios.get(`getsystemresources`);
    return data;
  },
  UpdateSystemResourcesMutation: async (body) => {
    const { data } = await Axios.post(
      `updatesystemresourcesnotificationconfiguration`,
      body
    );
    return data;
  },
  GetAgentRateQuery: async (body) => {
    const { data } = await Axios.get(`agentcustomersgetrate`, {
      params: body,
    });
    return data;
  },
};
