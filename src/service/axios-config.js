import { Notification } from "@arco-design/web-react";
import axios from "axios";

export const Axios = axios.create({
  baseURL: import.meta.env.VITE_BASE_URL,
});

Axios.interceptors.request.use((config) => {
  const token = localStorage.getItem("token");

  //config.headers['clientId'] = ''
  //config.headers['X-Requested-With'] = ''
  //config.headers['mode'] = 'cors'

  if (token) config.headers.Authorization = `Bearer ${token}`;

  return config;
});

Axios.interceptors.response.use(
  function (response) {
    if (response?.config?.method !== "get") {
      if (response?.data?.status) {
        Notification.success({
          title: "Success",
          content: response?.data?.message,
        });
      } else if (response?.data?.secure_url) {
        Notification.success({
          title: "Success",
          content: response?.data?.message,
        });
      } else if (response?.data?.transactionRef?.toLowerCase() === "success") {
        Notification.success({
          title: "Success",
          content: response?.data?.message,
        });
      } else {
        Notification.error({
          title: "Error",
          content: response?.data?.message,
        });
        console.log(response?.data?.message, "response");
        return Promise.reject(response?.data);
      }
    }
    if (!response?.data?.status) {
      Notification.error({
        title: "Error",
        content: response?.data?.message,
      });
      console.log(response?.data?.message, "response");
      return Promise.reject(response?.data);
    }
    return response;
  },
  function (error) {
    if (error?.config?.method !== "get")
      Notification.error({
        title: "Error",
        content: error?.response?.data?.message || error?.message,
      });

    if (error?.response?.data?.message === "Invalid token, Expired") {
      localStorage.clear();
      window.location = "/";
    }

    if (401 === error?.response?.status) {
      if (
        window.location.pathname === "/" ||
        window.location.pathname === "/" ||
        window.location.pathname === "/forgot-password" ||
        window.location.pathname === "/register" ||
        window.location.pathname === "/create-account" ||
        window.location.pathname === "/"
      ) {
        return;
      } else {
        window.location = "/";
        localStorage.clear();
        console.log(error?.response?.status);
      }
    } else if (
      "Request failed with status code 500" === error.message ||
      error?.response?.status >= 500
    ) {
      return Promise.reject({
        ...error,
        message: "It's not you, it's us. Try again later.",
      });
    } else {
      return Promise.reject(error);
    }
  }
);
