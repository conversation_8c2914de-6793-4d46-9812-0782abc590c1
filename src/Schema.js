import * as Yup from "yup";
export const LoginSchema = Yup.object().shape({
  username: Yup.string().required("Email is required"),
  password: Yup.string().required("Password is required"),
});

export const RegisterSchema = Yup.object().shape({
  fullName: Yup.string().required("Full name is required"),
  email: Yup.string().required("Email is required"),
  phone: Yup.string().required("Phone Number is required"),
  password: Yup.string()
    .required("Password is required")
    ?.min(8, "Must be 8 characters or more")
    ?.matches(/[a-z]+/, "One lowercase character")
    ?.matches(/[A-Z]+/, "One uppercase character")
    ?.matches(/[@$!%*#?&]+/, "One special character")
    ?.matches(/\d+/, "One number"),
  password_confirmation: Yup.string()
    .required("Confirm Password")
    .oneOf([Yup.ref("password"), null], "Passwords must match"),
});

export const SubAccountSchema = Yup.object().shape({
  fullName: Yup.string().required("Full name is required"),
  email: Yup.string().required("Email is required"),
  phone: Yup.string().required("Phone Number is required"),
  password: Yup.string().required("Password is required"),
  password_confirmation: Yup.string()
    .required("Confirm Password")
    .oneOf([Yup.ref("password"), null], "Passwords must match"),
});

export const CustomerSchema = Yup.object().shape({
  firstName: Yup.string().required("First name is required"),
  lastName: Yup.string().required("Last name is required"),
  email: Yup.string().required("Email is required"),
  phone: Yup.string().required("Phone Number is required"),
});
export const InviteAgentSchema = Yup.object().shape({
  email: Yup.string().required("Email is required"),
  firstName: Yup.string().required("First Name is required"),
});

export const UserSchema = Yup.object().shape({
  email: Yup.string().required("email is required"),
  roleId: Yup.string().required("Role is required"),
});

export const RoleSchema = Yup.object().shape({
  name: Yup.string().required("Name is required"),
  description: Yup.string().required("Description is required"),
  permissions: Yup.array()
    .min(1, "At least one permission must be selected")
    .required("A permission must be selected"),
});

export const CreateCurrencyRateMetadataSchema = (step) => {
  return step === 1
    ? Yup.object().shape({
        name: Yup.string().required("Name is required"),
        description: Yup.string().required("Description is required"),
        currency: Yup.object({
          id: Yup.string().required("Currency is required"),
        }),
        role: Yup.object({
          id: Yup.string().required("Role is required"),
        }),
      })
    : step === 2
    ? Yup.object().shape({
        minTransferLimit: Yup.string().required(
          "Minimum Transfer Limit is required"
        ),
        maxTransferLimit: Yup.string().required(
          "Maximum Transfer Limit is required"
        ),
        allowBelowMinimum: Yup.boolean(),
        allowAboveMaximum: Yup.boolean(),

        belowMinimumChargeType: Yup.lazy((value, context) => {
          return context.parent.allowBelowMinimum
            ? Yup.string().required("Minimum Charge Type is required")
            : Yup.string().notRequired();
        }),

        belowMinimumCharges: Yup.lazy((value, context) => {
          return context.parent.allowBelowMinimum
            ? Yup.string().required("Minimum Charge is required")
            : Yup.string().notRequired();
        }),

        aboveMaximumChargeType: Yup.lazy((value, context) => {
          return context.parent.allowAboveMaximum
            ? Yup.string().required("Maximum Charge Type is required")
            : Yup.string().notRequired();
        }),

        aboveMaximumLimitCharges: Yup.lazy((value, context) => {
          return context.parent.allowAboveMaximum
            ? Yup.string().required("Maximum Charge is required")
            : Yup.string().notRequired();
        }),

        dailyLimit: Yup.string().required("Daily Limit is required"),
        weeklyLimit: Yup.string().required("Weekly Limit is required"),
        monthlyLimit: Yup.string().required("Monthly Limit is required"),
        annualLimit: Yup.string().required("Annual Limit is required"),
        transferBonusThreshold: Yup.string().required(
          "Bonus Threshold is required"
        ),
        bonusRateValue: Yup.string().required("Bonus Rate is required"),
      })
    : Yup.object().shape({
        proofOfAddressAmountThreshold: Yup.string().required(
          "Proof of Payment Threshold is required"
        ),
        sourceOfFundAmountThreshold: Yup.string().required(
          "Source of Fund Threshold is required"
        ),
        kycThreshold: Yup.string().required("Kyc Threshold is required"),
      });
};

export const SendMoneySchema = (step) => {
  return step === 1
    ? Yup.object().shape({
        userBeneficiaryId: Yup.string().required("Beneficiary is required"),
      })
    : Yup.object().shape({
        paymentChannelId: Yup.string().required("This Field is required"),
        payoutChannelId: Yup.string().required("This Field is required"),
        amount: Yup.string().required("Amount is required"),
      });
};

export const PayoutSchema = Yup.object().shape({
  amount: Yup.string().required("Amount is required"),
  reason: Yup.string().required("Reason is required"),
});

export const RefundSchema = Yup.object().shape({
  amount: Yup.string().required("Amount is required"),
  merchantReason: Yup.string().required("Reason is required"),
});

export const FeesSchema = (type) => {
  return type?.toUpperCase() === "PERCENTAGE"
    ? Yup.object().shape({
        minAmount: Yup.string().required("Min Amount is required"),
        maxAmount: Yup.string()
          .required("Maximum amount is required")
          .transform((value) => value.replace(/,/g, "")) // Remove commas
          .test(
            "is-greater",
            "Amount must be greater than minimum amount",
            function (value) {
              return Number(value) > Number(this.parent.minAmount);
            }
          ),
        type: Yup.string().required("Charge Type is required"),
        charge: Yup.string().required("Charge is required"),
        capFee: Yup.string()
          .required("Cap Fee is required")
          .test(
            "is-greater",
            "Cap Fee must be greater than 0",
            function (value) {
              return Number(value) > 0;
            }
          ),
        fixedFee: Yup.string().required("Flat Service Fee is required"),
      })
    : Yup.object().shape({
        minAmount: Yup.string().required("Min Amount is required"),
        maxAmount: Yup.string()
          .required("Maximum amount is required")
          .transform((value) => value.replace(/,/g, "")) // Remove commas
          .test(
            "is-greater",
            "Amount must be greater than minimum amount",
            function (value) {
              return Number(value) > Number(this.parent.minAmount);
            }
          ),
        type: Yup.string().required("Charge Type is required"),
        charge: Yup.string().required("Charge is required"),
        fixedFee: Yup.string().required("Flat Service Fee is required"),
      });
};
