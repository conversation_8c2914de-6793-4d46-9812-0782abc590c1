import { clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import getSymbolFromCurrency from "currency-symbol-map";

export function cn(...inputs) {
  return twMerge(clsx(inputs));
}
export function Gsh2(n) {
  return (n + "").split(".")[0];
}
export function Gsh(n) {
  return (n + "").split(".")[1];
}
export function FormatCurrency(value, currency) {
  console.log(Gsh2(4000.7)?.length > 3, "jllllhkdlsll");
  return currency
    ? Gsh(value)?.length > 3
      ? getSymbolFromCurrency(currency) +
        Gsh2(`${value}`).replace(/\B(?=(\d{3})+(?!\d))/g, ",") +
        "." +
        Gsh(value)
      : getSymbolFromCurrency(currency) +
        `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")
    : Gsh(value)?.length > 3
    ? Gsh2(`${value}`).replace(/\B(?=(\d{3})+(?!\d))/g, ",") + "." + Gsh(value)
    : `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}
export function handleSetItem(row, data, setItem) {
  const itm = data?.find((item) => item?.id === row);
  setItem(itm);
}

export function handleGetItm(value, checkFor, data) {
  const itm = data?.find((item) => item?.[checkFor] === value);
  return itm;
}

export const CFormatter = (num) => {
  return `${num?.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")}`;
};

export const CFormatterNaira = (num) => {
  return `₦${num?.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")}`;
};
export const kFormatter = (val) => {
  return Math.abs(val) > 999999999
    ? `${Math.sign(val) * (Math.abs(val) / 1000000000).toFixed(1)}B`
    : Math.abs(val) > 999999
    ? `${Math.sign(val) * (Math.abs(val) / 1000000).toFixed(1)}M`
    : Math.abs(val) > 999
    ? `${Math.sign(val) * (Math.abs(val) / 1000).toFixed(1)}k`
    : Math.sign(val) * Math.abs(val);
};

export const kFormatterNaira = (val) => {
  return Math.abs(val) > 999999999
    ? `₦ ${Math.sign(val) * (Math.abs(val) / 1000000000).toFixed(1)}B`
    : Math.abs(val) > 999999
    ? `₦${Math.sign(val) * (Math.abs(val) / 1000000).toFixed(1)}M`
    : Math.abs(val) > 999
    ? `₦${Math.sign(val) * (Math.abs(val) / 1000).toFixed(1)}k`
    : `₦${Math.sign(val) * Math.abs(val)}`;
};

export const checkedMatureDate = (e) => {
  let tdate = new Date();
  let year = tdate.getFullYear();
  let month = tdate.getMonth();
  let day = tdate.getDate();
  let newYear = new Date(year - 18, month, day);
  let dob = (e) => new Date(e);

  return dob(e) > newYear;
};

export const sumUp = (array) => {
  if (array?.length) {
    return array?.reduce(function (accumulator, currentValue) {
      return accumulator + currentValue;
    }, 0);
  } else {
    return [0, 0, 0]?.reduce(function (accumulator, currentValue) {
      return accumulator + currentValue;
    }, 0);
  }
};

export function nairaToKobo(naira) {
  if (typeof naira !== "number" || naira < 0) {
    throw new Error("Invalid naira amount. Please provide a positive number.");
  }

  const kobo = naira * 100;
  return Math.round(kobo); // Round to the nearest integer for precise kobo values
}

export const getPercentage = (part, total) => {
  if (total === 0) return 0; // Avoid division by zero
  return (part / total) * 100;
};

export const buildDynamicPathUrl = (baseUrl, pathParams = {}) => {
  const path = Object.entries(pathParams)
    .map(
      ([key, value]) =>
        `${encodeURIComponent(key)}/${encodeURIComponent(value)}`
    )
    .join("/");
  return `${baseUrl}/${path}`;
};

export const buildDynamicQueryUrl = (baseUrl, queryParams = {}) => {
  const query = Object.entries(queryParams)
    .map(
      ([key, value]) =>
        `${encodeURIComponent(key)}=${encodeURIComponent(value)}`
    )
    .join("&");

  return `${baseUrl}${query ? "?" + query : ""}`;
};

export const filterKeys = [
  "limit",
  "sort",
  "search",
  "startDate",
  "endDate",
  "page",
];

export const applyFilters = (data, search, searchKeys = []) => {
  return data?.filter((item) => {
    if (!search) return true; // If no search term, include all

    const lowerSearch = search.toLowerCase();

    return searchKeys.some((key) => {
      const value = item?.[key];
      return value?.toString().toLowerCase().includes(lowerSearch);
    });
  });
};

/* export const mapSubMenus = (subMenus, userRoleSubMenuAccess) => {
  return subMenus
    .map((subMenu) => {
      const matchingSubMenu = userRoleSubMenuAccess.find(
        (sub) => sub.subMenuName === subMenu.name
      );
      if (matchingSubMenu) {
        // If the subMenu has its own subMenus, map those too
        const deeperSubMenus =
          matchingSubMenu.userRoleSuSubbMenuAccess.length > 0
            ? mapSubMenus(
                subMenu.subMenus || [],
                matchingSubMenu.userRoleSuSubbMenuAccess
              )
            : []; // Recursively handle deeper submenus if any
        // Check if the submenu itself is a direct route or has inner menus
        if (subMenu.subMenus && subMenu.subMenus.length > 0) {
          return {
            ...subMenu,
            route: matchingSubMenu.route || "",
            icon: subMenu.icon || null,
            subMenus: deeperSubMenus,
          };
        } else {
          // Direct route for submenus with no further submenus
          return {
            ...subMenu,
            route: matchingSubMenu.route || "",
            icon: subMenu.icon || null,
          };
        }
      }
      return null; // Return null for unmatching submenus
    })
    .filter((subMenu) => subMenu !== null); // Filter out null values
};

export const transformToMegaMenu = (roleBasedMenu, megaMenuLink) => {
  return megaMenuLink.map((menuGroup) => {
    const mappedMenus = menuGroup.menus.map((menu) => {
      // Find the matching API data for this menu
      const apiMenu = roleBasedMenu.find((item) => item.menuName === menu.name);

      if (apiMenu) {
        // Handle the case where the menu has no submenus (direct route)
        if (!menu.subMenus || menu.subMenus.length === 0) {
          return {
            head: menu.head,
            name: menu.name,
            route: menu.route,
            icon: menu.icon,
          };
        }

        // Handle the case where the menu has submenus
        const mappedSubMenus = mapSubMenus(
          menu.subMenus,
          apiMenu.userRoleSubMenuAccess
        );

        return {
          head: menu.head,
          name: menu.name,
          route: menu.route,
          icon: menu.icon,
          subMenus: mappedSubMenus,
        };
      }

      return menu; // Return the menu if no matching API data
    });

    return {
      ...menuGroup,
      menus: mappedMenus,
    };
  });
};
 */

export function filterMenuByApi(link2, apiLinks) {
  // Convert API menus and submenus into a flat lookup map
  const allowedMainMenus = new Set(
    apiLinks.map((item) => item.menuName.toLowerCase())
  );

  const allowedSubMenus = new Map();
  apiLinks.forEach((item) => {
    const subs = item.userRoleSubMenuAccess || [];
    allowedSubMenus.set(
      item.menuName.toLowerCase(),
      new Set(subs.map((sub) => sub.subMenuName.toLowerCase()))
    );
    // Handle sub-sub-menus
    subs.forEach((sub) => {
      if (sub.subMenuName && sub.userRoleSuSubbMenuAccess?.length) {
        allowedSubMenus.set(
          sub.subMenuName.toLowerCase(),
          new Set(
            sub.userRoleSuSubbMenuAccess.map((ssub) =>
              ssub.subSubMenuName.toLowerCase()
            )
          )
        );
      }
    });
  });

  return link2.filter((menu) => {
    const menuName = menu.name?.toLowerCase();
    const menuHead = menu.head?.toLowerCase();

    // Check if menu itself is allowed
    const isMainAllowed =
      allowedMainMenus.has(menuName) || allowedMainMenus.has(menuHead);

    if (!menu.subMenus) {
      return (
        isMainAllowed || (allowedSubMenus.get(menuHead)?.has(menuName) ?? false)
      );
    }

    // If it has submenus, filter them too
    const allowedSubs =
      allowedSubMenus.get(menu.name.toLowerCase()) ||
      allowedSubMenus.get(menuHead);

    if (!allowedSubs) return false;

    const filteredSubMenus = menu.subMenus
      .map((sub) => {
        // Handle sub-sub-menus
        if (sub.subMenus) {
          const allowedSubSubs = allowedSubMenus.get(sub.name.toLowerCase());
          const filtered = sub.subMenus.filter((ssub) =>
            allowedSubSubs?.has(ssub.name.toLowerCase())
          );
          return filtered.length > 0 ? { ...sub, subMenus: filtered } : null;
        }
        return allowedSubs.has(sub.name.toLowerCase()) ? sub : null;
      })
      .filter(Boolean);

    if (filteredSubMenus.length === 0) return false;

    menu.subMenus = filteredSubMenus;
    return true;
  });
}

export const downloadFile = (data, filename = "data") => {
  if (!data.length) return;

  const headers = Object.keys(data[0]);
  const rows = data.map((row) =>
    headers.map((field) => `"${row[field] ?? ""}"`).join(",")
  );

  const csvContent = [headers.join(","), ...rows].join("\n");
  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  const url = URL.createObjectURL(blob);

  const link = document.createElement("a");
  link.href = url;
  link.download = `${filename}.csv`;
  link.click();

  URL.revokeObjectURL(url);
};
