import { Chart as ChartJS, ArcElement, Legend } from "chart.js";

import { Pie } from "react-chartjs-2";
import styled from "styled-components";

ChartJS.register(ArcElement, Legend);

const DonutGauge = ({ optionData, label, colors = ["#029D1B", "#dbdde1"] }) => {
  const data = {
    labels: label || [],
    datasets: [
      {
        label: "Count",
        data: optionData || ["90", "10"],
        backgroundColor: colors,
        hoverOffset: 4,
      },
    ],
  };
  const options = {
    borderRadius: 0,
    cutout: "84%",
    responsive: true,
    maintainAspectRatio: true,

    // maintainAspectRatio: false,
    plugins: {
      tooltip: {
        enabled: false,
      },
      legend: {
        display: false,
        position: "right",
        onClick: null,
      },
    },
  };

  return (
    <>
      <Flexx
        style={{
          width: 240,
          height: 240,
          margin: "20px auto",
        }}
      >
        <div className="total">
          <div>
            <h3>{90}%</h3>
            <p>error rate</p>
          </div>
        </div>
        <div className="container"></div>
        <Pie data={data} options={options} width={600} height={600}></Pie>
      </Flexx>
      <div
        style={{
          color: "#71717a",
          fontSize: "14px",
          textAlign: "center",
          marginTop: "20px 0",
        }}
      >
        January - June 2024
      </div>
    </>
  );
};

const Flexx = styled.div`
  display: flex;
  flex-direction: column;
  height: 400px;
  width: 400px;
  font-size: 20px !important;
  position: relative;
  .time_frame {
    color: #71717a;
    font-size: 14px;
    text-align: center;
    margin: 20px 0;
  }
  .container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: grid;
    border-radius: 999px;
    place-items: center;
    z-index: -1;
    background-color: #dbdde1;
    width: 100%;
    height: 100%;
  }
  .total {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    display: grid;
    border-radius: 999px;
    place-items: center;
    z-index: 0;
    background-color: #ffffff;
    width: 84%;
    height: 84%;
    h3 {
      color: #333333;
      font-size: 40px;
    }
    p {
      color: #5a6376;
      font-size: 14px;
    }
  }
`;

export default DonutGauge;
