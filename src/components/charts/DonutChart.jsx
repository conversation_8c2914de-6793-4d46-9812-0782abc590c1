import { FormatCurrency } from "@/lib/utils";
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from "chart.js";

import { Doughnut } from "react-chartjs-2";
import styled from "styled-components";

ChartJS.register(ArcElement, Tooltip, Legend);

const DonutBorder = ({
  optionData,
  label,
  colors = ["#029D1B", "#332266", "#7272FF", "#9B0303", "#90f6e5", "#0f8bff"],
}) => {
  const data = {
    labels: label || [],
    datasets: [
      {
        label: "Count",
        data: optionData || [],
        backgroundColor: colors,
        hoverOffset: 4,
      },
    ],
  };
  const options = {
    borderRadius: 60,
    cutout: "90%",
    responsive: true,
    maintainAspectRatio: true,

    // maintainAspectRatio: false,
    plugins: {
      tooltip: {
        display: false,
      },
      legend: {
        display: false,
        position: "right",
        onClick: null,
      },
    },
  };

  const doughnutLabel = {
    id: "doughnutLabel",
    afterDatasetsDraw: () => {},
  };
  return (
    <>
      <Flex
        style={{
          width: 260,
          height: 260,
          margin: "20px auto",
        }}
      >
        {" "}
        <div className="total">
          <div>
            <p>Total Sales</p>
            <h4>{100}</h4>
          </div>
        </div>
        <Doughnut
          data={data}
          options={options}
          plugins={[doughnutLabel]}
          width={600}
          height={600}
        ></Doughnut>
      </Flex>
      <div>
        {label?.map((item, index) => {
          return (
            <>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  fontSize: "14px",
                }}
              >
                <svg
                  width="16"
                  height="17"
                  viewBox="0 0 16 17"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  style={{ width: "12px", height: "12px" }}
                >
                  <rect
                    width="15.5585"
                    height="16.7553"
                    rx="2.39362"
                    fill={colors[index]}
                  />
                </svg>
                &nbsp; &nbsp;
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <div>{item}</div>
                  &nbsp; <div>-</div>
                  &nbsp; <div>{FormatCurrency(optionData[index], "ngn")}</div>
                </div>
              </div>
              &nbsp;
            </>
          );
        })}
      </div>
    </>
  );
};

const Flex = styled.div`
  display: flex;
  flex-direction: column;
  height: 400px;
  width: 400px;
  font-size: 20px !important;
  position: relative;

  .total {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    display: grid;
    place-items: center;
    background-color: #dbdde1;
    width: 70%;
    height: 70%;
    border-radius: 999px;
    z-index: 0;
    h4 {
      color: #333333;
    }
    p {
      color: #5a6376;
      font-size: 14px;
    }
  }
`;

export default DonutBorder;
