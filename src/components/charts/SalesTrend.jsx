import Chart from "react-apexcharts";
import { CFormatter } from "@/lib/utils";

function SalesTrend() {
  const chartOptions = {
    chart: {
      type: "area",
      stacked: true,
      toolbar: {
        show: false,
      },
    },

    grid: {
      xaxis: {
        lines: {
          show: true,
        },
      },
      yaxis: {
        lines: {
          show: false,
        },
      },
    },
    legend: {
      show: false,
    },
    yaxis: {
      show: false,
      labels: {
        formatter: function (val) {
          return CFormatter(val);
        },
      },
    },
    xaxis: {
      categories: ["Hiii", "heelo"],
      axisBorder: {
        show: false,
        color: "#78909C",
        offsetX: 0,
        offsetY: 0,
      },
    },
    dataLabels: {
      enabled: false,
    },
    stroke: {
      curve: "smooth",
    },

    colors: ["#FF6500"],
  };

  const series = [
    {
      name: "Transactions",
      data: [200, 400],
    },
  ];

  return (
    <>
      <Chart
        height="100%"
        width="100%"
        type="area"
        options={chartOptions}
        series={series}
      ></Chart>
    </>
  );
}

export default SalesTrend;
