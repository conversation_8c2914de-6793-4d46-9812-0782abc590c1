import { sumUp } from "@/lib/utils";
import { Flex } from "@radix-ui/themes";
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from "chart.js";

import { Pie } from "react-chartjs-2";
import styled from "styled-components";

ChartJS.register(ArcElement, Tooltip, Legend);

const PaymentMethodChart = ({
  optionData,
  label,
  colors = ["#019240", "#FF7200", "#2A94CD", "#8C3FCF", "#F4A462"],
}) => {
  const data = {
    labels: label || [],
    datasets: [
      {
        label: "Count",
        data: optionData || [],
        backgroundColor: colors,
        hoverOffset: 4,
      },
    ],
  };
  const options = {
    borderRadius: 0,
    cutout: "0%",
    responsive: true,
    maintainAspectRatio: true,

    // maintainAspectRatio: false,
    plugins: {
      tooltip: {
        display: false,
      },
      legend: {
        display: false,
        position: "right",
        onClick: null,
      },
    },
  };

  const realVal = (n, index) => {
    return sumUp(optionData) / n[index];
  };

  return (
    <>
      <Flexx
        style={{
          width: 260,
          height: 260,
          margin: "20px auto",
        }}
      >
        <Pie data={data} options={options} width={600} height={600}></Pie>
      </Flexx>
      &nbsp; &nbsp;
      <div>
        {label?.map((item, index) => {
          return (
            <>
              <Flex mb={"4"} maxWidth={"250px"} mx={"auto"} justify={"between"}>
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <div
                    style={{
                      width: "13px",
                      height: "5px",
                      background: colors[index],
                      borderRadius: "100px",
                    }}
                  ></div>
                  &nbsp; &nbsp; <div>{item}</div>
                </div>
                &nbsp; &nbsp;
                <div>{realVal(optionData, index)?.toFixed()}%</div>
              </Flex>
            </>
          );
        })}
      </div>
    </>
  );
};

const Flexx = styled.div`
  display: flex;
  flex-direction: column;
  height: 400px;
  width: 400px;
  font-size: 20px !important;
  position: relative;
`;

export default PaymentMethodChart;
