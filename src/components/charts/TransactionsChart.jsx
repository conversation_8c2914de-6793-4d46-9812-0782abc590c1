import Chart from "react-apexcharts";
import { FormatCurrency } from "@/lib/utils";

function TransactionsChart({
  colors = ["#F04F4F", "#31B550"],
  labels = ["June", "July", "Aug", "Sept", "Oct", "Nov", "Dec"],
  series = [
    {
      name: "Failed",
      data: [200, 400, 500, 200, 400, 300, 240],
    },
    {
      name: "Successful",
      data: [200, 400, 300, 200, 400, 500, 300],
    },
  ],
  currency,
}) {
  const chartOptions = {
    chart: {
      type: "bar",
      stacked: false,
      toolbar: {
        show: false,
      },
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: "16px", // Adjust this value to reduce column width
        distributed: false, // Set to true if you want each bar to have its own color
        barSpacing: 20,
      },
    },

    grid: {
      strokeDashArray: 5,
      strokeColor: '#DCE3EB',
          xaxis: {
        lines: {
          show: false,
        },
      },
      yaxis: {
        lines: {
          show: true,
        },
      },
    },
    legend: {
      show: false,
    },
    yaxis: {
      show: false,
      labels: {
        formatter: function (val) {
          return FormatCurrency(val, currency);
        },
      },
    },
    xaxis: {
      categories: labels,
      axisBorder: {
        show: false,
        color: "#78909C",
        offsetX: 0,
        offsetY: 0,
      },
    },

    dataLabels: {
      enabled: false,
    },
    stroke: {
      colors: ["transparent"],
      width: 5,
    },

    colors: colors,
  };

  return (
    <>
      <Chart
        height="250px"
        width="100%"
        type="bar"
        options={chartOptions}
        series={series}
      ></Chart>
    </>
  );
}

export default TransactionsChart;
