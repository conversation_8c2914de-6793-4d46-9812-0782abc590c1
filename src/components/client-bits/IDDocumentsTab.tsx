import React, { FC, useState } from "react";
import CustomTable from "../bits/CustomTable";
import { StatusBadge } from "../bits/StatusBadge";
import { MenuItem, MenuList } from "../bits/DropDownMenu";
import TransactionDocument from "@/Modals/TransactionDocument";
import { saveAs } from "file-saver";
import IDComments from "@/Modals/IDComments";
import AddCommentToID from "@/Modals/AddCommentToID";
import ConfirmModal from "@/Modals/ConfirmModal";
import { useMutation } from "@tanstack/react-query";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useNavigate } from "react-router-dom";
import { handleGetItm } from "@/lib/utils";
import AppButton from "../bits/AppButton";
import { Download, Pencil, Plus, Trash } from "lucide-react";
import { Flex, Grid } from "@radix-ui/themes";
import SectionHeader from "../bits/SectionHeader";
import DividerHorizontal from "../DividerHorizontal";
import { Notification } from "@arco-design/web-react";
import EditClientID from "@/Modals/EditClientID";

const IDDocumentsTab: FC<any> = ({ data, refetch }) => {
  const [image, setImage] = useState();
  const [item, setItem] = useState({
    name: "",
    file: "",
  });

  const [open, setOpen] = useState(false);
  const [open2, setOpen2] = useState(false);

  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.UpdateClientFileMutation,
    onSuccess: () => {
      refetch();
    },
    onError: () => {
      return;
    },
  });

  const files = [
    {
      name: "formCo7URL",
      file: data?.formCo7URL,
    },
    {
      name: "formCo2URL",
      file: data?.formCo2URL,
    },
    {
      name: "utilityBill",
      file: data?.utilityBill,
    },
  ];

  return (
    <div>
      <SectionHeader
        title={"ID Documents"}
        description="View, edit and update client ID Docs here"
        showBackBtn={undefined}
        align={undefined}
        fontSize="20px"
      />
      {image && (
        <TransactionDocument open={image} setOpen={setImage} data={image} />
      )}
      {open2 && (
        <EditClientID
          open={open2}
          setOpen={setOpen2}
          item={{ ...item, userId: data?.userId }}
          finished={refetch}
        />
      )}
      <ConfirmModal
        trigger={setOpen}
        open={open}
        description={"Are you sure you want to complete this request"}
        confirm={() => {
          mutate({
            objectId: data?.userId,
            action: 0,
            fileName: item?.name,
            fileURL: item?.file,
          });
        }}
        cancel={() => {
          setOpen(false);
        }}
        loading={isPending}
      />
      <DividerHorizontal />
      {files?.map((itm) => {
        return (
          <>
            <Grid columns={"3"}>
              <div> {itm?.name}</div>

              <div
                style={{
                  borderRadius: "15px",
                  border: "1px dashed #E0E0E0",
                  padding: "16px",
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  background: "#FCFEFF",
                }}
              >
                <Flex align={"center"}>
                  <svg
                    width="46"
                    height="46"
                    viewBox="0 0 46 46"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <rect
                      x="3"
                      y="3"
                      width="40"
                      height="40"
                      rx="20"
                      fill="#F2F4F7"
                    />
                    <rect
                      x="3"
                      y="3"
                      width="40"
                      height="40"
                      rx="20"
                      stroke="#F9FAFB"
                      stroke-width="6"
                      stroke-linecap="round"
                    />
                    <g clip-path="url(#clip0_11591_254567)">
                      <path
                        d="M26.3326 26.3332L22.9992 22.9999M22.9992 22.9999L19.6659 26.3332M22.9992 22.9999V30.4999M29.9909 28.3249C30.8037 27.8818 31.4458 27.1806 31.8158 26.3321C32.1858 25.4835 32.2627 24.5359 32.0344 23.6388C31.8061 22.7417 31.2855 21.9462 30.5548 21.3778C29.8241 20.8094 28.925 20.5005 27.9992 20.4999H26.9492C26.697 19.5243 26.2269 18.6185 25.5742 17.8507C24.9215 17.0829 24.1033 16.4731 23.181 16.0671C22.2587 15.661 21.2564 15.4694 20.2493 15.5065C19.2423 15.5436 18.2568 15.8085 17.3669 16.2813C16.477 16.7541 15.7058 17.4225 15.1114 18.2362C14.517 19.05 14.1148 19.9879 13.9351 20.9794C13.7553 21.9709 13.8027 22.9903 14.0736 23.961C14.3445 24.9316 14.8319 25.8281 15.4992 26.5832"
                        stroke="#475467"
                        stroke-width="1.66667"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_11591_254567">
                        <rect
                          width="20"
                          height="20"
                          fill="white"
                          transform="translate(13 13)"
                        />
                      </clipPath>
                    </defs>
                  </svg>

                  <div
                    style={{
                      cursor: "pointer",
                    }}
                    onClick={() => {
                      if (itm?.file) {
                        setImage(itm?.file);
                      } else {
                        Notification.success({
                          content: "No File Uploaded to view",
                        });
                      }
                    }}
                  >
                    {itm?.name}
                  </div>
                </Flex>

                <Flex gap="2">
                  <Pencil
                    onClick={() => {
                      setOpen2(true);
                      setItem(itm);
                    }}
                    size={16}
                  />
                  <Download
                    onClick={() => {
                      if (itm?.file?.includes("pdf")) {
                        fetch(itm?.file)
                          .then((response) => response.blob())
                          .then((blob) => {
                            saveAs(blob, `${itm?.name}.pdf`);
                          });
                      } else {
                        saveAs(itm?.file, `${itm?.name}.png`);
                      }
                    }}
                    size={16}
                  />
                  <Trash
                    color="red"
                    onClick={() => {
                      setOpen(true);
                      setItem(itm);
                    }}
                    size={16}
                  />
                </Flex>
              </div>
            </Grid>
            <DividerHorizontal />{" "}
          </>
        );
      })}
    </div>
  );
};

export default IDDocumentsTab;
