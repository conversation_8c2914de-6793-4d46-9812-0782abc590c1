import React, { FC, useState } from "react";
import CustomTable from "../bits/CustomTable";
import { StatusBadge } from "../bits/StatusBadge";
import { ProviderBadge } from "../bits/ProviderBadge";
import { CurrencyBadge } from "../bits/CurrencyBadge";
import { FormatCurrency } from "@/lib/utils";
import SwitchInput from "../bits/SwitchInput";
import { useMutation } from "@tanstack/react-query";
import { ApiServiceAdmin } from "@/service/admin-services";

const GatewayTab: FC<any> = ({ data, refetch }) => {
  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.TogglePayoutClientProviderMutation,
    onSuccess: () => {
      refetch();
    },
    onError: () => {
      return;
    },
  });
  const columns = [
    {
      title: "Status",
      dataIndex: "status",
      width: 100,
      render: (e) => <StatusBadge status={e} />,
    },

    {
      title: "Provider",
      dataIndex: "providerName",
      width: 100,
    },

    {
      title: "Currency",
      dataIndex: "wallet[currency][code]",
      width: 100,
      render: (e) => <CurrencyBadge currency={e} />,
    },
    {
      title: "Balance",
      dataIndex: "wallet",
      width: 100,
      render: (e) => FormatCurrency(e?.balance, e?.currency?.code),
    },
    {
      title: "Wallet Id",
      dataIndex: "wallet[walletId]",
      width: 100,
    },
    {
      title: "make active",
      dataIndex: "status",
      width: 100,
      render: (e, record) => (
        <SwitchInput
          onChange={() => {
            if (e === "true" || e === "Active") {
              mutate({
                payOutClientWalletProviderId: record?.providerId, //Leave it as 0.
                action: 0, // 0 to deactivate, 1 to acctivate
                objectId: data?.userId,
                // paymentClientWalletProviderId: record?.id, //Payout channel ID.
              });
            } else {
              mutate({
                payOutClientWalletProviderId: record?.providerId, //Leave it as 0.
                action: 1, // 0 to deactivate, 1 to acctivate
                objectId: data?.userId,
                //paymentClientWalletProviderId: record?.id, //Payout channel ID.
              });
            }
          }}
          checked={e === "true" || e === "Active"}
          formik={undefined}
          name={undefined}
          label={undefined}
        />
      ),
    },
  ];

  return (
    <div>
      <CustomTable
        tableColumns={columns}
        arrayData={data?.payOutClientWalletPayOutProviders}
        scroll={{
          x: 1600,
        }}
        Apidata={undefined}
        loading={isPending}
        noData={undefined}
        topContent={undefined}
        setPage={undefined}
        total={undefined}
        currentPage={undefined}
        tableWidth={undefined}
      />
    </div>
  );
};

export default GatewayTab;
