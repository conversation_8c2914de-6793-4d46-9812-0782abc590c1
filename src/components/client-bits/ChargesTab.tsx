import React, { FC, useState } from "react";
import CustomTable from "../bits/CustomTable";
import { StatusBadge } from "../bits/StatusBadge";
import { ProviderBadge } from "../bits/ProviderBadge";
import { CurrencyBadge } from "../bits/CurrencyBadge";
import { FormatCurrency } from "@/lib/utils";
import AppLink from "../bits/AppLink";
import SectionHeader from "../bits/SectionHeader";
import DividerHorizontal from "../DividerHorizontal";
import { Flex } from "@radix-ui/themes";
import AppButton from "../bits/AppButton";
import { Plus } from "lucide-react";
import AddClientCharge from "@/Modals/AddClientCharge";
import EditClientCharge from "@/Modals/EditClientCharge";

const ChargesTab: FC<any> = ({ data, refetch }) => {
  const [open, setOpen] = useState(false);
  const [open2, setOpen2] = useState(false);
  const [item, setItem] = useState({});
  const columns = [
    {
      title: "Action",
      dataIndex: "id",
      width: 140,
      render: (e, record) => (
        <div
          onClick={() => {
            setItem(record);
            setOpen2(true);
          }}
        >
          <AppLink to={undefined} type="nnn">
            Edit Charge
          </AppLink>
        </div>
      ),
    },
    {
      title: "Currency",
      dataIndex: "currency[code]",
      width: 120,
      render: (e) => <CurrencyBadge currency={e} />,
    },
    {
      title: "Charge Type",
      dataIndex: "payoutChargeType[typeName]",
      width: 140,
    },
    {
      title: "Amount",
      dataIndex: "baseValue",
      width: 120,
      render: (e, record) => FormatCurrency(e, record?.currency?.code),
    },

    {
      title: "Min. Fixed Capped Ammount",
      dataIndex: "minimumFixedCapped",
      width: 120,
      render: (e, record) => FormatCurrency(e, record?.currency?.code),
    },
    {
      title: "Max. Fixed Capped Ammount",
      dataIndex: "maximumFixedCapped",
      width: 120,
      render: (e, record) => FormatCurrency(e, record?.currency?.code),
    },
    {
      title: "Date Added",
      dataIndex: "dateCreated",
      width: 120,
    },
    {
      title: "Last Updated",
      dataIndex: "lastUpdated",
      width: 120,
    },
  ];

  return (
    <div>
      {open && (
        <AddClientCharge
          open={open}
          setOpen={setOpen}
          finished={refetch}
          item={{
            userId: data?.userId,
          }}
        />
      )}

      {open2 && (
        <EditClientCharge
          open={open2}
          setOpen={setOpen2}
          finished={refetch}
          item={{
            ...item,
            userId: data?.userId,
          }}
        />
      )}
      <Flex justify={"between"}>
        <SectionHeader
          title={"Charge"}
          description="Manage Charge Types"
          showBackBtn={undefined}
          align={undefined}
          fontSize="20px"
        />

        <AppButton
          placeholder={"New Charge"}
          width="150px"
          onClick={() => {
            setOpen(true);
            setItem({});
          }}
          loading={undefined}
          disabled={undefined}
          outline={undefined}
          secondary={undefined}
          borderColor={undefined}
          style={undefined}
          style2={undefined}
          IconLeft={Plus}
          IconRight={undefined}
          to={undefined}
          roundedFull={undefined}
          radius={undefined}
        />
      </Flex>
      <DividerHorizontal />
      <CustomTable
        tableColumns={columns}
        arrayData={data?.payOutClientCharges}
        scroll={{
          x: 1600,
        }}
        Apidata={undefined}
        loading={undefined}
        noData={undefined}
        topContent={undefined}
        setPage={undefined}
        total={undefined}
        currentPage={undefined}
        tableWidth={undefined}
      />
    </div>
  );
};

export default ChargesTab;
