import React, { FC, useState } from "react";
import CustomTable from "../bits/CustomTable";
import { StatusBadge } from "../bits/StatusBadge";
import { ProviderBadge } from "../bits/ProviderBadge";
import { CurrencyBadge } from "../bits/CurrencyBadge";

const TransactionTab: FC<any> = ({ data }) => {
  const columns = [
    {
      title: "Transaction ref",
      dataIndex: "clientRef",
      fixed: "left",
      width: 140,
    },

    {
      title: "ID",
      dataIndex: "id",
      width: 220,
    },
    {
      title: "Transaction Status",
      dataIndex: "status",
      width: 140,
      render: (e) => <StatusBadge status={e} />,
    },
    {
      title: "Date",
      dataIndex: "dateCreated",
      width: 220,
    },
    {
      title: "Gateway",
      dataIndex: "payOutProvider",
      width: 200,
      render: (e, record) => <ProviderBadge name={e?.name} logo={e?.logo} />,
    },
    {
      title: "Receiver",
      dataIndex: "beneficiary[beneficiaryName]",

      width: 120,
    },
    {
      title: "Bank Name",
      dataIndex: "beneficiary[beneficiaryBank][bankName]",
      width: 120,
    },
    {
      title: "Account Number",
      dataIndex: "beneficiary[beneficiaryBank][accountNumber]",
      width: 200,
    },
    {
      title: "Currency",
      dataIndex: "country[currencyCode]",
      width: 120,
      render: (e) => <CurrencyBadge currency={e} />,
    },
    {
      title: "Amount",
      dataIndex: "Amount",
      width: 120,
    },
    {
      title: "Transfer fee",
      dataIndex: "transferFee",
      width: 120,
    },
  ];

  return (
    <div>
      <CustomTable
        tableColumns={columns}
        arrayData={data?.payOutTransactions}
        scroll={{
          x: 1600,
        }}
        Apidata={undefined}
        loading={undefined}
        noData={undefined}
        topContent={undefined}
        setPage={undefined}
        total={undefined}
        currentPage={undefined}
        tableWidth={undefined}
      />
    </div>
  );
};

export default TransactionTab;
