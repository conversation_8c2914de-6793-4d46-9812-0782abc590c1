import { Flex, Grid } from "@radix-ui/themes";
import React, { FC, useEffect, useState } from "react";
import Box from "../bits/Box";
import GaugeChartComponent from "../GaugeChartComponent";
import Divider from "../Divider";
import AdminBox from "../AdminBox";
import CurrencySelect from "../bits/CurrencySelect";
import { s } from "vite/dist/node/types.d-aGj9QkWt";
import SectionHeader from "../bits/SectionHeader";
import DividerHorizontal from "../DividerHorizontal";
import FormInput from "../bits/FormInput";

const DetailsOverviewTab: FC<any> = ({ data }) => {
  return (
    <div>
      <SectionHeader
        title={"Profile Details"}
        description="Please fill in with necessary information below"
        showBackBtn={undefined}
        align={undefined}
        fontSize="20px"
      />
      <DividerHorizontal />
      <Grid gap={"4"} columns={"2"}>
        <FormInput
          label={"Full Name"}
          defaultValue={undefined}
          value={data?.companyName}
          name={undefined}
          type={undefined}
          placeholder={undefined}
          disabled
          formik={undefined}
          max={undefined}
          IconRight={undefined}
          IconLeft={undefined}
          hint={undefined}
          onChange={undefined}
        />
        <FormInput
          label={"Email Address"}
          value={data?.email}
          name={undefined}
          type={undefined}
          placeholder={undefined}
          disabled
          formik={undefined}
          max={undefined}
          IconRight={undefined}
          IconLeft={undefined}
          hint={undefined}
          onChange={undefined}
        />
        <FormInput
          label={"City"}
          value={data?.city?.name}
          name={undefined}
          type={undefined}
          placeholder={undefined}
          disabled
          formik={undefined}
          max={undefined}
          IconRight={undefined}
          IconLeft={undefined}
          hint={undefined}
          onChange={undefined}
        />
        <FormInput
          label={"Address"}
          value={data?.address}
          name={undefined}
          type={undefined}
          placeholder={undefined}
          disabled
          formik={undefined}
          max={undefined}
          IconRight={undefined}
          IconLeft={undefined}
          hint={undefined}
          onChange={undefined}
        />
      </Grid>
    </div>
  );
};

export default DetailsOverviewTab;
