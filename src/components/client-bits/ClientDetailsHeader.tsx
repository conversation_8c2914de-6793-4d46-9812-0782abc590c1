import { Flex, Grid } from "@radix-ui/themes";
import React, { FC } from "react";
import CurrencyFlagImage from "react-currency-flags";
import { StatusBadge } from "../bits/StatusBadge";

const ClientDetailsHeader: FC<any> = ({ data }) => {
  return (
    <Grid columns={{ md: "2", initial: "1" }}>
      <Grid
        columns={{ md: "1fr 4fr", initial: "1" }}
        gap={"4"}
        style={{
          borderRight: "1px solid #b1b1b1",
          paddingRight: "1rem",
        }}
      >
        <img
          style={{
            width: "130px",
            height: "130px",
            borderRadius: "999px",
            objectFit: "contain",
            border: "1px solid #e6e6e6",
          }}
          src={data?.profileImageURL}
        />
        <div>
          <Flex gap={"2"} align={"center"}>
            <h2>{data?.companyName}</h2>
            {data?.status && <StatusBadge status={data?.status} />}
          </Flex>
          <div
            style={{
              marginBlock: "1rem",
            }}
          >
            <span style={{ color: "#909090" }}>Client ID</span>
            <p>{data?.userId}</p>
          </div>
          <div
            style={{
              marginBlock: "1rem",
            }}
          >
            <span style={{ color: "#909090" }}>Registration Date</span>
            <p>{data?.dateCreated}</p>
          </div>
        </div>
      </Grid>
      <div
        style={{
          paddingInline: "20px",
        }}
      >
        <span style={{ color: "#909090" }}>CONTACT INFORMATION</span>

        <div
          style={{
            marginBlock: "1rem",
          }}
        >
          <span style={{ color: "#909090" }}>Mobile Number</span>
          <p>{data?.phone}</p>
        </div>
        <div
          style={{
            marginBlock: "1rem",
          }}
        >
          <span style={{ color: "#909090" }}>Email Address</span>
          <p>{data?.email}</p>
        </div>
      </div>
    </Grid>
  );
};

export default ClientDetailsHeader;
