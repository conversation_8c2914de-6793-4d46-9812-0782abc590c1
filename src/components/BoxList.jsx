import { Flex } from "@radix-ui/themes";
import DividerHorizontal from "./DividerHorizontal";

export default function BoxList({
  list = [
    { title: "List", value: "list" },
    { title: "List", value: "list" },
  ],
}) {
  return (
    <div
      style={{
        borderRadius: "8px",
        border: "1px solid #ECEFF3",
        background: "#F8FAFB",
        paddingInline: "20px",
        paddingBlock: "8px",
        width: "100%",
      }}
    >
      {list?.map((itm, index) => {
        return (
          <>
            <Flex justify={"between"} align={'center'} py={"3"}>
              <span
                style={{
                  color: "#666D80",
                  fontSize: "14px",
                }}
              >
                {itm?.title}
              </span>
              <span
                style={{
                  color: "#36394A",
                  fontSize: "14px",
                }}
              >
                {itm?.value ? itm?.value : ""}
              </span>
            </Flex>
            {index === list?.length - 1 ? (
              ""
            ) : (
              <DividerHorizontal style={{ margin: 0 }} />
            )}
          </>
        );
      })}
    </div>
  );
}
