import React, { FC } from "react";
import CustomTable from "../bits/CustomTable";
import AppButton from "../bits/AppButton";
import { Flex } from "@radix-ui/themes";
import SectionHeader from "../bits/SectionHeader";
import AdminBox from "../AdminBox";
import TransactionsChart from "../charts/TransactionsChart";

const PaymentTypeChart: FC = ({ data, currency }: any) => {
  const colors = ["#5E5ADB", "#F04043", "#DC964E", "#5196D6"];
  const labels = data?.map((itm) => itm.month);
  const series = [
    {
      name: "Pay By Card",
      data: data?.map((itm) => itm.CardValue),
    },
    {
      name: "Cash",
      data: data?.map((itm) => itm.CashValue),
    },
    {
      name: "Bank Transfer",
      data: data?.map((itm) => itm.ManualBankTransferValue),
    },
    {
      name: "Pay With Bank",
      data: data?.map((itm) => itm.PayWithBankValue),
    },
  ];
  return (
    <div>
      <AdminBox title="Payment Type" description="" sideChild={undefined} style={undefined}>
        <Flex gap={"4"}>
          {series.map((item, idx) => {
            return (
              <Flex
                align={"center"}
                style={{
                  fontSize: "14px",
                }}
              >
                <div
                  style={{
                    width: "13px",
                    height: "5px",
                    background: colors[idx],
                    borderRadius: "100px",
                  }}
                ></div>
                &nbsp; &nbsp;
                <div>{item.name}</div>
              </Flex>
            );
          })}
          {/*  <Flex
            align={"center"}
            style={{
              fontSize: "14px",
            }}
          >
            <div
              style={{
                width: "13px",
                height: "5px",
                background: "#F04F4F",
                borderRadius: "100px",
              }}
            ></div>
            &nbsp; &nbsp;
            <div>Failed</div>
          </Flex> */}
        </Flex>
        <TransactionsChart
          colors={colors}
          series={series}
          labels={labels}
          currency={currency}
        />
      </AdminBox>
    </div>
  );
};

export default PaymentTypeChart;
