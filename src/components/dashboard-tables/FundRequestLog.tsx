import React, { FC, useState } from "react";
import CustomTable from "../bits/CustomTable";
import AppButton from "../bits/AppButton";
import { Flex } from "@radix-ui/themes";
import SectionHeader from "../bits/SectionHeader";
import { useAuth } from "@/context/global.context";
import { StatusBadge } from "../bits/StatusBadge";
import {
  applyFilters,
  downloadFile,
  FormatCurrency,
  handleSetItem,
} from "@/lib/utils";
import { MenuItem, MenuList } from "../bits/DropDownMenu";
import ApproveClientFundRequestModal from "@/Modals/ApproveClientFundRequest";
import DeclineClientFundRequestModal from "@/Modals/DeclineClientFundRequest";
import moment from "moment";
import AppLink from "../bits/AppLink";
import FormInput from "../bits/FormInput";
import { Download, Search } from "lucide-react";
import { DatePicker } from "@arco-design/web-react";

const FundRequestLog: FC = () => {
  const { fundRequestLogs, refetchUserData } = useAuth();

  const [open, setOpen] = useState(false);
  const [open2, setOpen2] = useState(false);

  const userMenu = [
    {
      name: "Approve",
      action: () => {
        setOpen(true);
      },
      index: 1,
    },
    {
      name: "Decline",
      color: "red",
      action: () => {
        setOpen2(true);
      },
      index: 2,
    },
  ];

  const [item, setItem] = useState<any>();

  const columns = [
    {
      title: "Action",
      dataIndex: "id",
      fixed: "left",
      width: 100,
      render: (e, record) =>
        record?.status?.toLowerCase() === "pending" ? (
          <MenuList
            onClick={() => {
              handleSetItem(e, fundRequestLogs, setItem);
            }}
            ActionIcon={undefined}
            ActionElement={undefined}
            iconWidth={undefined}
          >
            {userMenu?.map(({ name, action, index, color }) => {
              return (
                <>
                  <MenuItem
                    name={name}
                    index={index}
                    action={() => {
                      action();
                    }}
                    Icon={undefined}
                    to={undefined}
                    width={"160px"}
                    padding={2}
                    color={color}
                  />
                </>
              );
            })}
          </MenuList>
        ) : (
          "-"
        ),
    },
    {
      title: "Request id",
      dataIndex: "id",
      width: 190,
    },
    /*  {
      title: "Request type",
      dataIndex: "name",
    }, */
    {
      title: "date created",
      dataIndex: "dateCreated",
      render: (e) => moment(e).format("DD/MM/YY, hh:mm:ss"),
      width: 190,
    },
    {
      title: "last updated",
      dataIndex: "lastUpdated",
      render: (e) => moment(e).format("DD/MM/YY, hh:mm:ss"),
      width: 190,
    },
    {
      title: "transaction status",
      dataIndex: "status",
      render: (e) => <StatusBadge status={e} />,
      width: 140,
    },
    /*    {
      title: "requested currency",
      dataIndex: "other1",
      render: () => "Other 1",
    },
    {
      title: "receiving currency",
      dataIndex: "other1",
      render: () => "Other 1",
    }, */
    {
      title: "amount requested",
      dataIndex: "amountRequested",
      render: (e) => FormatCurrency(e),
      width: 190,
    },
    /*    {
      title: "rate",
      dataIndex: "other1",
      render: () => "Other 1",
    },
    {
      title: "fee",
      dataIndex: "other1",
      render: () => "Other 1",
    }, */
    {
      title: "amount paid",
      dataIndex: "amountPaid",
      render: (e) => FormatCurrency(e),
      width: 190,
    },
    {
      title: "user ID",
      dataIndex: "userId",
      width: 200,
      render: (e, record) => (
        <AppLink to={`/customers/${e}?tab=overview`}>{record?.userId}</AppLink>
      ),
    },
    {
      title: "receiver account details",
      dataIndex: "userBeneficiary['beneficiaryName']",
      //render: () => "Other 1",
      width: 200,
    },
    {
      title: "receiver account number",
      dataIndex: "userBeneficiary['beneficiaryBank']['accountNumber']",
      //render: () => "Other 1",
      width: 200,
    },
    {
      title: "receiver bank name",
      dataIndex: "userBeneficiary['beneficiaryBank']['bankName']",
      //render: () => "Other 1",
      width: 200,
    },
    {
      title: "comment",
      dataIndex: "comment",
      width: 290,
    },
    /*    {
      title: "paid by",
      dataIndex: "paidBy",
    },
    {
      title: "release by",
      dataIndex: "other1",
      render: () => "Other 1",
    }, */
  ];
  const [date, setDate] = useState<Date[] | undefined>();

  const [search, setSearch] = useState("");

  const applyFilters = (data, search, start, end) => {
    return data?.filter((item) => {
      const itemDate = new Date(item?.dateCreated?.replace(" ", "T"));

      // Handle date filtering
      let isWithinDateRange = true;
      if (start) {
        const startDate = start;
        isWithinDateRange = itemDate >= startDate;
      }
      if (end) {
        const endDate = end;
        isWithinDateRange = isWithinDateRange && itemDate <= endDate;
      }

      // Handle search filtering (on paymentRef or userId)
      let matchesSearch = true;
      if (search) {
        const lowerSearch = search.toLowerCase();
        const refMatch = item?.id
          ?.toString()
          ?.toLowerCase()
          ?.includes(lowerSearch);
        const userMatch = item?.userId
          ?.toString()
          ?.toLowerCase()
          ?.includes(lowerSearch);
        matchesSearch = refMatch || userMatch;
      }

      return isWithinDateRange && matchesSearch;
    });
  };

  const newData = applyFilters(fundRequestLogs, search, date?.[0], date?.[1]);

  return (
    <div>
      {open && (
        <ApproveClientFundRequestModal
          open={open}
          setOpen={setOpen}
          item={item}
          finished={refetchUserData}
        />
      )}
      {open2 && (
        <DeclineClientFundRequestModal
          open={open2}
          setOpen={setOpen2}
          item={item}
          finished={refetchUserData}
        />
      )}
      <CustomTable
        arrayData={newData}
        tableColumns={columns}
        topContent={
          <>
            <Flex
              align={{ initial: "start", md: "center" }}
              direction={{ initial: "column", md: "row" }}
              justify={{ initial: "start", md: "between" }}
            >
              <SectionHeader
                title="Withdrawal Requests"
                description=""
                showBackBtn={undefined}
                align={undefined}
                fontSize="24px"
              />
            </Flex>
            <Flex justify={"between"} align={"end"}>
              <FormInput
                placeholder={"Search by Request ID or User ID..."}
                background="#fff"
                IconLeft={Search}
                width="400px"
                onChange={(e) => {
                  setSearch(e?.target?.value?.trim());
                }}
                name={undefined}
                type={undefined}
                label={undefined}
                disabled={undefined}
                formik={undefined}
                max={undefined}
                IconRight={undefined}
                hint={undefined}
              />

              <Flex justify={"between"} align={"start"} gap={"4"}>
                <DatePicker.RangePicker
                  className="range"
                  placeholder={["Start Date", "End Date"]}
                  onChange={(e) => {
                    if (e?.length) {
                      setDate([
                        new Date(`${e[0]}T00:00:00`),
                        new Date(`${e[1]}T23:59:59`),
                      ]);
                    } else {
                      setDate(null);
                    }
                    console.log(e, "date");
                  }}
                  style={{
                    marginBottom: "10px",
                  }}
                  position="br"
                />
                {search || date ? (
                  <AppButton
                    width="160px"
                    IconLeft={Download}
                    placeholder="Download"
                    onClick={() => {
                      if (newData) downloadFile(newData, "withdrawals");
                    }}
                    loading={undefined}
                    disabled={undefined}
                    outline={undefined}
                    secondary={undefined}
                    borderColor={undefined}
                    style={undefined}
                    style2={undefined}
                    IconRight={undefined}
                    to={undefined}
                    roundedFull={undefined}
                    radius={undefined}
                  />
                ) : (
                  ""
                )}
              </Flex>
            </Flex>
          </>
        }
        loading={undefined}
        noData={undefined}
        setPage={undefined}
        total={undefined}
        currentPage={undefined}
        Apidata={undefined}
        scroll={{
          x: 1400,
        }}
        tableWidth={undefined}
      />
    </div>
  );
};

export default FundRequestLog;
