import AppLink from "@/components/bits/AppLink";
import CustomTable from "@/components/bits/CustomTable";
//import MiniNavBar from "@/components/bits/MiniNavBar";
import { StatusBadge } from "@/components/bits/StatusBadge";
import { applyFilters, downloadFile, handleGetItm } from "@/lib/utils";
//import { handleSetItem } from "@/lib/utils";
import { useState } from "react";
import { Flex } from "@radix-ui/themes";
import FormInput from "../bits/FormInput";
import { Download, Search } from "lucide-react";
import AppButton from "../bits/AppButton";

export default function CustomerTableListTwo({ data, isLoading, topContent }) {
  //ACTIONS START

  //ACTIONS END

  const columns = [
    {
      title: "Customer ref",
      dataIndex: "userId",
      width: 120,
    },
    {
      title: "status",
      dataIndex: "status",
      render: (e) => <StatusBadge status={e} />,

      width: 120,
    },
    {
      title: "ID Verification",
      dataIndex: "isKYCCompleted",
      render: (e) => <StatusBadge status={e ? "verified" : "not verified"} />,
      width: 200,
    },
    {
      title: "Account type",
      dataIndex: "accountType",
      render: (e) => <div>{e === 1 ? "Individual" : "Business"}</div>,
      width: 170,
    },

    {
      title: "Customer name",
      dataIndex: "userId",
      width: 200,
      render: (e) => (
        <AppLink to={`/customers/${e}?tab=overview`}>
          {handleGetItm(e, "userId", data?.data)?.firstName}
          {handleGetItm(e, "userId", data?.data)?.surName}
        </AppLink>
      ),
    },

    {
      title: "email",
      dataIndex: "email",
      width: 120,
    },
    {
      title: "Address",
      dataIndex: "address",
      width: 120,
    },
    {
      title: "Mobile Number",
      dataIndex: "phone",
      width: 160,
    },

    {
      title: "date",
      dataIndex: "dateCreated",
      width: 200,
    },
    {
      title: "Email Verified",
      dataIndex: "isEmailVerified",
      render: (e) => <StatusBadge status={e ? "True" : "False"} />,
      width: 140,
    },
  ];

  const [search, setSearch] = useState("");

  const newData = applyFilters(data?.data, search, [
    "userId",
    "firstName",
    "surName",
    "address",
    "email",
    "phone",
    "status",
  ]);

  return (
    <div>
      <CustomTable
        topContent={
          <>
            {topContent}
            <Flex justify={"between"} align={"center"}>
              <FormInput
                placeholder={"Search by Name, Customer ref or Address"}
                background="#fff"
                IconLeft={Search}
                width="400px"
                onChange={(e) => {
                  setSearch(e?.target?.value?.trim());
                }}
              />

              {search && (
                <AppButton
                  width="160px"
                  IconLeft={Download}
                  placeholder="Download"
                  onClick={() => {
                    if (newData) downloadFile(newData, "customers");
                  }}
                />
              )}
            </Flex>
          </>
        }
        tableColumns={columns}
        loading={isLoading}
        arrayData={newData?.length ? newData : data?.data}
        scroll={{
          x: 1600,
        }}
      />
    </div>
  );
}
