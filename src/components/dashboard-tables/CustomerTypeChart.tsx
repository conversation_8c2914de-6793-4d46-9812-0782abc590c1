import React, { <PERSON> } from "react";
import CustomTable from "../bits/CustomTable";
import AppButton from "../bits/AppButton";
import { Flex } from "@radix-ui/themes";
import SectionHeader from "../bits/SectionHeader";
import AdminBox from "../AdminBox";
import TransactionsChart from "../charts/TransactionsChart";

const CustomerTypeChart: FC = ({ data, currency }: any) => {
  const colors = ["#FB923C", "#CBC7C6", "#7694E0"];
  const labels = data?.map((itm) => itm.month);
  const series = [
    {
      name: "Backoffice",
      data: data?.map((itm) => itm.backOfficeTransactionValue),
    },
    {
      name: "Web",
      data: data?.map((itm) => itm.webTransactionValue),
    },
    {
      name: "App",
      data: data?.map((itm) => itm.appTransactionValue),
    },
  ];
  return (
    <div>
      <AdminBox title="Channels" description="" sideChild={undefined}>
        <Flex gap={"4"}>
          {series.map((item, idx) => {
            return (
              <Flex
                align={"center"}
                style={{
                  fontSize: "14px",
                }}
              >
                <div
                  style={{
                    width: "13px",
                    height: "5px",
                    background: colors[idx],
                    borderRadius: "100px",
                  }}
                ></div>
                &nbsp; &nbsp;
                <div>{item.name}</div>
              </Flex>
            );
          })}
        </Flex>
        <TransactionsChart
          colors={colors}
          series={series}
          labels={labels}
          currency={currency}
        />
      </AdminBox>
    </div>
  );
};

export default CustomerTypeChart;
