import AppLink from "@/components/bits/AppLink";
import { CurrencyBadge } from "@/components/bits/CurrencyBadge";
import CustomTable from "@/components/bits/CustomTable";
import { MenuItem, MenuList } from "@/components/bits/DropDownMenu";
//import MiniNavBar from "@/components/bits/MiniNavBar";
import { StatusBadge } from "@/components/bits/StatusBadge";
import { applyFilters, downloadFile, handleGetItm } from "@/lib/utils";
//import { handleSetItem } from "@/lib/utils";
import SwitchInput from "../bits/SwitchInput";
import { useMutation } from "@tanstack/react-query";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useState } from "react";
import UpdateCustomerRateModal from "@/Modals/UpdateCustomerRate";
import MessageCustomerModal from "@/Modals/MessageCustomer";
import FundCustomerModal from "@/Modals/FundCustomerWallet";
import DepleteCustomerWalletModal from "@/Modals/DepleteCustomerWallet";
import { Flex } from "@radix-ui/themes";
import FormInput from "../bits/FormInput";
import { CopyIcon, Download, Search } from "lucide-react";
import { useAuth } from "@/context/global.context";
import AppButton from "../bits/AppButton";
import { Notification } from "@arco-design/web-react";

export default function CustomerTableList({
  data,
  isLoading,
  refetch,
  topContent,
}) {
  const { user_data } = useAuth();
  const [updateRateModal, setUpdateRateModal] = useState(false);
  const [messageCustomerModal, setMessageCustomerModal] = useState(false);
  const [fundCustomerModal, setFundCustomerModal] = useState(false);
  const [depleteCustomerModal, setDepleteCustomerModal] = useState(false);

  const { mutate: activateUser, isPending: isPendingActivate } = useMutation({
    mutationFn: ApiServiceAdmin.ActivateCustomerMutation,
    onSuccess: () => {
      refetch();
    },
    onError: () => {
      return;
    },
  });

  const { mutate: deactivateUser, isPending: isPendingDeactivate } =
    useMutation({
      mutationFn: ApiServiceAdmin.DeactivateCustomerMutation,
      onSuccess: () => {
        refetch();
      },
      onError: () => {
        return;
      },
    });

  const { mutate: suspendUser, isPending: isPendingSuspend } = useMutation({
    mutationFn: ApiServiceAdmin.SuspendCustomerMutation,
    onSuccess: () => {
      refetch();
    },
    onError: () => {
      return;
    },
  });

  const { mutate: watchUser, isPending: isPendingWatch } = useMutation({
    mutationFn: ApiServiceAdmin.WatchCustomerMutation,
    onSuccess: () => {
      refetch();
    },
    onError: () => {
      return;
    },
  });

  const [item, setItem] = useState();

  const userMenu = [
    {
      name: "Update Rate",
      status: ["active", "inactive", "on hold"],
      action: () => {
        setUpdateRateModal(true);
      },
    },

    {
      name: "Suspend Customer",
      status: ["active", "on hold"],
      action: () => {
        suspendUser({
          userId: item?.userId,
        });
      },
    },
    {
      name: "Unsuspend Customer",
      status: ["suspended"],
      action: () => {
        activateUser({
          userId: item?.userId,
        });
      },
    },
    {
      name: "Watch Customer",
      status: ["active", "inactive", "on hold"],
      action: () => {
        watchUser({
          userId: item?.userId,
          watchListStatus: true,
        });
      },
    },
    {
      name: "Unwatch Customer",
      status: ["active", "inactive", "on hold"],
      action: () => {
        watchUser({
          userId: item?.userId,
          watchListStatus: false,
        });
      },
    },
    {
      name: "Message Customer",
      status: ["active", "inactive", "on hold"],
      action: () => {
        setMessageCustomerModal(true);
      },
    },
    {
      name: "Fund Wallet",
      status: ["active", "inactive", "on hold"],
      action: () => {
        setFundCustomerModal(true);
      },
    },
    {
      name: "Deplete Wallet",
      status: ["active", "inactive", "on hold"],

      action: () => {
        setDepleteCustomerModal(true);
      },
    },
    {
      name: "Activate Customer",
      status: ["inactive", "on hold"],
      action: () => {
        activateUser({
          userId: item?.userId,
        });
      },
    },
    {
      name: "Deactivate Customer",
      status: ["active", "on hold"],
      action: () => {
        deactivateUser({
          userId: item?.userId,
        });
      },
    },
  ];

  const userMenuAgent = [
    {
      name: "Update Rate",
      status: ["active", "inactive", "on hold"],
      action: () => {
        setUpdateRateModal(true);
      },
    },
    {
      name: "Watch Customer",
      status: ["active", "inactive", "on hold"],
      action: () => {
        watchUser({
          userId: item?.userId,
          watchListStatus: true,
        });
      },
    },
    {
      name: "Unwatch Customer",
      status: ["active", "inactive", "on hold"],
      action: () => {
        watchUser({
          userId: item?.userId,
          watchListStatus: false,
        });
      },
    },
    {
      name: "Message Customer",
      status: ["active", "inactive", "on hold"],
      action: () => {
        setMessageCustomerModal(true);
      },
    },

    {
      name: "Activate Customer",
      status: ["inactive", "on hold"],
      action: () => {
        activateUser({
          userId: item?.userId,
        });
      },
    },
    {
      name: "Deactivate Customer",
      status: ["active", "on hold"],
      action: () => {
        deactivateUser({
          userId: item?.userId,
        });
      },
    },
  ];

  //ACTIONS START

  const { mutate: allowMultiCurrency, isPending: isPendingAllow } = useMutation(
    {
      mutationFn: ApiServiceAdmin.AllowMultiCurrencyMutation,
      onSuccess: () => {
        refetch();
      },
      onError: () => {
        return;
      },
    }
  );

  const { mutate: disallowMultiCurrency, isPending: isPendingDisallow } =
    useMutation({
      mutationFn: ApiServiceAdmin.DisallowMultiCurrencyMutation,
      onSuccess: () => {
        refetch();
      },
      onError: () => {
        return;
      },
    });

  //ACTIONS END

  const menus = user_data?.role?.name === "Agent" ? userMenuAgent : userMenu;

  const columns = [
    {
      title: "Action",
      dataIndex: "userId",
      fixed: "left",
      width: 100,
      render: (e) => {
        const user = handleGetItm(e, "userId", data?.data);

        return (
          <MenuList
            onClick={() => {
              setItem(user);
            }}
            ActionIcon={undefined}
            ActionElement={undefined}
            iconWidth={undefined}
          >
            {!user?.watchListStatus
              ? menus
                  ?.filter((itm) => itm.name !== "Unwatch Customer")
                  ?.filter((itm) => {
                    return itm?.status?.includes(user?.status?.toLowerCase());
                  })
                  ?.map(({ name, action, index, color, to }) => (
                    <MenuItem
                      key={index}
                      name={name}
                      index={index}
                      action={() => {
                        action(e);
                      }}
                      Icon={undefined}
                      to={to && to(e)}
                      width={"200px"}
                      padding={2}
                      color={color}
                    />
                  ))
              : menus
                  ?.filter((itm) => itm.name !== "Watch Customer")
                  ?.filter((itm) => {
                    return itm?.status?.includes(user?.status?.toLowerCase());
                  })
                  ?.map(({ name, action, index, color, to }) => (
                    <MenuItem
                      key={index}
                      name={name}
                      index={index}
                      action={() => {
                        action(e);
                      }}
                      Icon={undefined}
                      to={to && to(e)}
                      width={"200px"}
                      padding={2}
                      color={color}
                    />
                  ))}
          </MenuList>
        );
      },
    },
    /*  {
      title: "Send Money",
      dataIndex: "userId",
      fixed: "left",
      width: 140,
      render: (e) => <AppLink to={`/send-money/${e}`}>Send Money</AppLink>,
    }, */

    {
      title: "Customer Category",
      dataIndex: "accountType",
      render: (e) => <div>{e === 1 ? "Individual" : "Business"}</div>,
      width: 170,
    },

    {
      title: "Customer ref",
      dataIndex: "userId",
      width: 120,
    },
    {
      title: "Customer name",
      dataIndex: "userId",
      width: 250,
      render: (e) => (
        <AppLink to={`/customers/${e}?tab=overview`}>
          {handleGetItm(e, "userId", data?.data)?.firstName}{" "}
          {handleGetItm(e, "userId", data?.data)?.surName}
        </AppLink>
      ),
    },
    {
      title: "MARKETER",
      dataIndex: "agentId",
      render: (e, record) =>
        e ? (
          <AppLink to={`/agents/${e}?tab=overview`}>
            {record?.agentName}
          </AppLink>
        ) : (
          "N/A"
        ),
      width: 250,
    },
    {
      title: "status",
      dataIndex: "status",
      render: (e) => <StatusBadge status={e} />,

      width: 120,
    },
    {
      title: "ID Verification",
      dataIndex: "isKYCCompleted",
      render: (e) => <StatusBadge status={e ? "verified" : "not verified"} />,
      width: 200,
    },
    {
      title: "Multi Currency Trading",
      dataIndex: "userId",
      width: 200,
      render: (e) => (
        <SwitchInput
          checked={
            handleGetItm(e, "userId", data?.data)?.allowMultiCurrencyTrading
          }
          onChange={() => {
            if (
              handleGetItm(e, "userId", data?.data)?.allowMultiCurrencyTrading
            ) {
              disallowMultiCurrency(e);
            } else {
              allowMultiCurrency(e);
            }
          }}
        />
      ),
    },
    {
      title: "Customer email",
      dataIndex: "email",
      width: 120,
    },
    {
      title: "Address",
      dataIndex: "address",
      width: 120,
    },
    {
      title: "Mobile Number",
      dataIndex: "phone",
      width: 160,
    },
    {
      title: "Country",
      dataIndex: "country",
      render: (e) => (
        <CurrencyBadge currency={e?.currencyCode} name={e?.name} />
      ),
      width: 200,
    },
    {
      title: "date",
      dataIndex: "dateCreated",
      width: 200,
    },
    {
      title: "Email Verified",
      dataIndex: "isEmailVerified",
      render: (e) => <StatusBadge status={e ? "True" : "False"} />,
      width: 140,
    },
    {
      title: "Copy Payment Link",
      dataIndex: "paymentLink",
      width: 200,
      render: (e) => (
        <div
          onClick={() => {
            navigator.clipboard.writeText(e);
            Notification.success({
              title: "Copied",
              content: "Payment Link successfully copied",
            });
          }}
        >
          <AppLink type="normal" color="#FF7434">
            Payment Link <CopyIcon color="#FF7434" size={14} />
          </AppLink>
        </div>
      ),
    },
  ];

  const [search, setSearch] = useState("");

  const newData = applyFilters(data?.data, search, [
    "userId",
    "firstName",
    "surName",
    "address",
    "email",
    "phone",
    "status",
  ]);

  return (
    <div>
      {updateRateModal && (
        <UpdateCustomerRateModal
          open={updateRateModal}
          setOpen={setUpdateRateModal}
          item={item}
          finished={refetch}
        />
      )}
      {messageCustomerModal && (
        <MessageCustomerModal
          open={messageCustomerModal}
          setOpen={setMessageCustomerModal}
          item={item}
          finished={refetch}
          type={"single"}
          userRoleId={6}
        />
      )}
      {fundCustomerModal && (
        <FundCustomerModal
          open={fundCustomerModal}
          setOpen={setFundCustomerModal}
          item={item}
          finished={refetch}
        />
      )}
      {depleteCustomerModal && (
        <DepleteCustomerWalletModal
          open={depleteCustomerModal}
          setOpen={setDepleteCustomerModal}
          item={item}
          finished={refetch}
        />
      )}

      <CustomTable
        topContent={
          <>
            {topContent}
            <Flex justify={"between"} align={"center"}>
              <FormInput
                placeholder={"Search by Name, Customer ref or Address"}
                background="#fff"
                IconLeft={Search}
                width="400px"
                onChange={(e) => {
                  setSearch(e?.target?.value?.trim());
                }}
              />
              {search && (
                <AppButton
                  width="160px"
                  IconLeft={Download}
                  placeholder="Download"
                  onClick={() => {
                    if (newData) downloadFile(newData, "customers");
                  }}
                />
              )}
            </Flex>
          </>
        }
        tableColumns={columns}
        loading={
          isLoading ||
          isPendingAllow ||
          isPendingDisallow ||
          isPendingActivate ||
          isPendingDeactivate ||
          isPendingSuspend ||
          isPendingWatch
        }
        arrayData={newData?.length ? newData : data?.data}
        scroll={{
          x: 1600,
        }}
      />
    </div>
  );
}
