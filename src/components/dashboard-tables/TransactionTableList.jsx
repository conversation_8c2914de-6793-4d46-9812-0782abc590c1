import AppLink from "@/components/bits/AppLink";
import { CurrencyBadge } from "@/components/bits/CurrencyBadge";
import CustomTable from "@/components/bits/CustomTable";
import { MenuItem, MenuList } from "@/components/bits/DropDownMenu";
//import MiniNavBar from "@/components/bits/MiniNavBar";
import { StatusBadge } from "@/components/bits/StatusBadge";
import { downloadFile, FormatCurrency, handleGetItm } from "@/lib/utils";
import BeneficiaryDetails from "@/Modals/BeneficiaryDetails";
import TransactionLocation from "@/Modals/TransactionLocation";
import TransactionReceipt from "@/Modals/TransactionReceipt";
//import { handleSetItem } from "@/lib/utils";
import { Flex } from "@radix-ui/themes";
import { Download, Eye, Search } from "lucide-react";
import moment from "moment";
import { useState } from "react";
import FormInput from "../bits/FormInput";
import ConfirmModal from "@/Modals/ConfirmModal";
import UseTransactionTableHook from "@/hooks/UseTransactionTableHook";
import ViewTransactionComments from "@/Modals/ViewTransactionComments";
import AddCommentToTransactionModal from "@/Modals/AddCommentToTransaction";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/context/global.context";
import { saveAs } from "file-saver";
import TransactionDocument from "@/Modals/TransactionDocument";
import { DatePicker } from "@arco-design/web-react";
import AppButton from "../bits/AppButton";

/**
 * @typedef { string | "view_comments"
 *          | "suspicious"
 *          | "cancel"
 *          | "add_comment"
 *          | "confirm"
 *          | "hold"
 *          | "revert_hold"
 *          | "release"
 *          | "revert"
 *          | "pay"
 *          | "paid"
 *          | "released" } TypeFace
 */

/** @type {TypeFace} */

export default function TransactionTableList({
  data,
  recall,
  isLoading,
  topContent,
}) {
  const { user_data, user_id } = useAuth();
  const [modalType, setModalType] = useState(/** @type {TypeFace} */ (""));

  const [viewCommentModal, setViewCommentModal] = useState(false);
  const [addCommentModal, setAddCommentModal] = useState(false);

  const { callMenuByType, isPending, open, setOpen } =
    UseTransactionTableHook(recall);

  const { menuAccessRoutes } = useAuth();
  const menu = menuAccessRoutes?.find((itm) => itm?.accessId === 10);
  const access = menu?.menuAccessType?.id;

  const navigate = useNavigate();

  const userMenu = [
    {
      name: "View Details",
      action: (e) => {
        navigate(`/transfer/${e}`, {
          state: handleGetItm(e, "paymentRef", data),
        });
      },
      index: 1,
      forPaymentStatus: [
        "cancelled",
        "deposited",
        "processed",
        "pending",
        "on-hold",
        "suspended",
      ],
      forCollectionStatus: ["received"],
    },
    {
      name: "View Comments",
      action: () => {
        setViewCommentModal(true);
      },
      index: 2,
      forPaymentStatus: [
        "cancelled",
        "deposited",
        "processed",
        "pending",
        "on-hold",
        "suspended",
      ],
      forCollectionStatus: ["received"],
    },
    {
      name: "Mark as Suspicious",
      action: () => {
        setModalType("suspicious");
        setOpen(true);
      },
      index: 3,
      forPaymentStatus: ["on-hold", "pending", "suspended"],
    },
    {
      name: "Cancel Transaction",
      action: () => {
        setModalType("cancel");
        setOpen(true);
      },
      index: 4,
      forPaymentStatus: ["pending", "suspended"],
    },
    {
      name: "Add Comment to Transaction",
      action: () => {
        setAddCommentModal(true);
      },
      index: 5,
      forPaymentStatus: [
        "cancelled",
        "deposited",
        "processed",
        "pending",
        "on-hold",
        "suspended",
      ],
      forCollectionStatus: ["received"],
    },
    {
      name: "Confirm Transaction",
      action: () => {
        setModalType("confirm");
        setOpen(true);
      },
      index: 6,
      forPaymentStatus: ["pending", "suspended"],
      role: [4],
    },
    {
      name: "Hold Transaction",
      action: () => {
        setModalType("hold");
        setOpen(true);
      },
      index: 7,
      forPaymentStatus: ["processed", "pending"],
      role: [4],
    },
    {
      name: "Revert Hold Transaction",
      action: () => {
        setModalType("revert_hold");
        setOpen(true);
      },
      index: 8,
      forPaymentStatus: ["on-hold"],
      role: [4],
    },
    {
      name: "Release Transaction",
      action: () => {
        setModalType("release");
        setOpen(true);
      },
      index: 9,
      forPaymentStatus: ["deposited"],
      role: [14, 16],
    },
    {
      name: "Revert Payment",
      action: () => {
        setModalType("revert");
        setOpen(true);
      },
      index: 10,
      forPaymentStatus: ["processed"],
    },
    {
      name: "Pay Transaction",
      action: () => {
        setModalType("pay");
        setOpen(true);
      },
      index: 11,
      forPaymentStatus: ["deposited"],
      forCollectionStatus: [""],
      role: [15, 16],
    },
    {
      name: "Mark as Paid",
      action: () => {
        setModalType("paid");
        setOpen(true);
      },
      index: 12,
      forPaymentStatus: ["deposited"],
      forCollectionStatus: [""],
      role: [15, 16],
    },
    {
      name: "Mark as Released",
      action: () => {
        setModalType("released");
        setOpen(true);
      },
      index: 13,
      forPaymentStatus: ["deposited"],
      role: [14, 16],
    },
  ];
  /*  const userMenuMain = [
    {
      name: "View Details",
      action: (e) => {
        navigate(`/transfer/${e}`, {
          state: handleGetItm(e, "paymentRef", data),
        });
      },
      index: 1,
      forPaymentStatus: [
        "cancelled",
        "deposited",
        "processed",
        "pending",
        "on-hold",
        "suspended",
      ],
      forCollectionStatus: ["received"],
    },
    {
      name: "View Comments",
      action: () => {
        setViewCommentModal(true);
      },
      index: 2,
      forPaymentStatus: [
        "cancelled",
        "deposited",
        "processed",
        "pending",
        "on-hold",
        "suspended",
      ],
      forCollectionStatus: ["received"],
    },

    {
      name: "Add Comment to Transaction",
      action: () => {
        setAddCommentModal(true);
      },
      index: 5,
      forPaymentStatus: [
        "cancelled",
        "deposited",
        "processed",
        "pending",
        "on-hold",
        "suspended",
      ],
      forCollectionStatus: ["received"],
    },
  ]; */

  const userMenu2 = [
    {
      name: "View Details",
      action: (e) => {
        navigate(`/transfer/${e}`, {
          state: handleGetItm(e, "paymentRef", data),
        });
      },
      index: 1,
      forPaymentStatus: [
        "cancelled",
        "deposited",
        "processed",
        "pending",
        "on-hold",
        "suspended",
      ],
      forCollectionStatus: ["received"],
    },
    {
      name: "View Comments",
      action: () => {
        setViewCommentModal(true);
      },
      index: 2,
      forPaymentStatus: [
        "cancelled",
        "deposited",
        "processed",
        "pending",
        "on-hold",
        "suspended",
      ],
      forCollectionStatus: ["received"],
    },
    {
      name: "Mark as Suspicious",
      action: () => {
        setModalType("suspicious");
        setOpen(true);
      },
      index: 3,
      forPaymentStatus: ["on-hold", "pending", "suspended"],
    },
    {
      name: "Cancel Transaction",
      action: () => {
        setModalType("cancel");
        setOpen(true);
      },
      index: 4,
      forPaymentStatus: ["pending", "suspended"],
    },
    {
      name: "Add Comment to Transaction",
      action: () => {
        setAddCommentModal(true);
      },
      index: 5,
      forPaymentStatus: [
        "cancelled",
        "deposited",
        "processed",
        "pending",
        "on-hold",
        "suspended",
      ],
      forCollectionStatus: ["received"],
    },

    {
      name: "Hold Transaction",
      action: () => {
        setModalType("hold");
        setOpen(true);
      },
      index: 7,
      forPaymentStatus: ["processed", "pending"],
      role: [4],
    },
    {
      name: "Revert Hold Transaction",
      action: () => {
        setModalType("revert_hold");
        setOpen(true);
      },
      index: 8,
      forPaymentStatus: ["on-hold"],
      role: [4],
    },

    {
      name: "Revert Payment",
      action: () => {
        setModalType("revert");
        setOpen(true);
      },
      index: 10,
      forPaymentStatus: ["processed"],
    },
  ];
  const [receipt, setReceipt] = useState();
  const [beneficiaryDetails, setBeneficiaryDetails] = useState();
  const [transactionLocation, setTransactionLocation] = useState();

  const [item, setItem] = useState();
  const [image, setImage] = useState();

  const menus =
    access === 2
      ? []
      : user_data?.role?.name === "Agent"
      ? userMenu2
      : userMenu;

  const columns = [
    {
      title: "Action",
      dataIndex: "paymentRef",
      fixed: "left",
      width: 80,
      render: (e, record) => {
        const paymentStatus = record?.paymentStatus?.toLowerCase();
        const collectionStatus = record?.collectStatus?.toLowerCase();

        return (
          <div
            style={{
              position: "relative",
            }}
          >
            {record?.transactionCommentCount ? (
              <div
                style={{
                  width: "6px",
                  height: "6px",
                  borderRadius: "50%",
                  background: "red",
                  position: "absolute",
                  top: "-15px",
                  right: "20px",
                }}
              ></div>
            ) : (
              ""
            )}
            <MenuList
              onClick={() => {
                setItem(record);
              }}
              ActionIcon={undefined}
              ActionElement={undefined}
              iconWidth={undefined}
            >
              {menus
                ?.filter((itm) => {
                  // Robust normalization for status matching
                  function normalizeStatus(str) {
                    return (str || "").toLowerCase().replace(/[\s_-]+/g, ""); // removes spaces, underscores, dashes
                  }
                  const paymentStatusNorm = normalizeStatus(paymentStatus);
                  const collectionStatusNorm =
                    normalizeStatus(collectionStatus);
                  const forPaymentStatusNorms = (
                    itm.forPaymentStatus || []
                  ).map(normalizeStatus);
                  const forCollectionStatusNorms = (
                    itm.forCollectionStatus || []
                  ).map(normalizeStatus);

                  const paymentMatch =
                    forPaymentStatusNorms.includes(paymentStatusNorm);
                  const collectionMatch =
                    forCollectionStatusNorms.includes(collectionStatusNorm);

                  // Debug logging
                  console.log({
                    menuName: itm.name,
                    paymentStatus,
                    paymentStatusNorm,
                    forPaymentStatus: itm.forPaymentStatus,
                    forPaymentStatusNorms,
                    paymentMatch,
                    collectionStatus,
                    collectionStatusNorm,
                    forCollectionStatus: itm.forCollectionStatus,
                    forCollectionStatusNorms,
                    collectionMatch,
                    userRole: user_data?.role?.id,
                    menuRoles: itm.role,
                    roleMatch:
                      !itm.role || itm.role.includes(user_data?.role?.id),
                  });

                  // Show if either matches
                  return paymentMatch || collectionMatch;
                })
                // Only filter by role if not super admin (role id 0)
                ?.filter((itm) => {
                  if (user_data?.role?.id === 0) return true;
                  return !itm.role || itm.role.includes(user_data?.role?.id);
                })
                ?.map(({ name, action, index, color, to }) => (
                  <MenuItem
                    key={index}
                    name={name}
                    index={index}
                    action={() => {
                      action(e);
                    }}
                    Icon={undefined}
                    to={to && to(e)}
                    width={"200px"}
                    padding={2}
                    color={color}
                  />
                ))}
            </MenuList>
          </div>
        );
      },
    },

    {
      title: "transaction status",
      dataIndex: "paymentStatus",
      render: (e) => <StatusBadge status={e} />,
      width: 180,
    },
    {
      title: "collection status",
      dataIndex: "collectStatus",
      render: (e) => <StatusBadge status={e} />,
      width: 170,
    },
    {
      title: "payment date",
      dataIndex: "paymentDate",
      render: (e) => moment(e).format("DD/MM/YY, hh:mm:ss"),
      width: 180,
    },
    {
      title: "Transaction Ref",
      dataIndex: "paymentRef",
      render: (e) => (
        <div
          onClick={() => {
            navigate(
              `/transfer/${e}`,

              {
                state: handleGetItm(e, "paymentRef", data),
              }
            );
          }}
        >
          <AppLink type="normal">{e}</AppLink>
        </div>
      ),
      width: 160,
    },
    {
      title: "Sender name",
      dataIndex: "userId",
      width: 220,
      render: (e, record) => (
        <AppLink to={`/customers/${e}?tab=overview`}>
          {record?.senderName}
        </AppLink>
      ),
    },
    {
      title: "Marketer",
      dataIndex: "senderAgentId",
      render: (e, record) =>
        e ? (
          <AppLink to={`/agents/${e}?tab=overview`}>
            {record?.senderAgent}
          </AppLink>
        ) : (
          "N/A"
        ),

      width: 260,
    },
    {
      title: "amount",
      dataIndex: "paymentRef",
      width: 140,
      render: (e) =>
        FormatCurrency(
          handleGetItm(e, "paymentRef", data).paymentAmount,
          handleGetItm(e, "paymentRef", data).senderCurrency
        ),
    },
    {
      title: "fee",
      dataIndex: "paymentRef",
      width: 100,
      render: (e) =>
        FormatCurrency(
          handleGetItm(e, "paymentRef", data).transitionFee,
          handleGetItm(e, "paymentRef", data).senderCurrency
        ),
    },
    {
      title: "rate",
      dataIndex: "paymentRef",
      width: 120,
      render: (e) =>
        FormatCurrency(
          handleGetItm(e, "paymentRef", data).rate,
          handleGetItm(e, "paymentRef", data).beneficiaryCurrency
        ),
    },
    /*  {
      title: "Sender",
      dataIndex: "senderName",
      width: 260,
    }, */

    {
      title: "forex amount",
      dataIndex: "paymentRef",
      width: 140,
      render: (e) =>
        FormatCurrency(
          handleGetItm(e, "paymentRef", data).receivedAmount,
          handleGetItm(e, "paymentRef", data).beneficiaryCurrency
        ),
    },
    {
      title: "Agent fee",
      dataIndex: "agentFee",
      width: 140,
      /*    render: (e) =>
        FormatCurrency(
          handleGetItm(e, "paymentRef", data).receivedAmount,
          handleGetItm(e, "paymentRef", data).beneficiaryCurrency
        ), */
    },
    {
      title: "Agent margin",
      dataIndex: "agentMargin",
      width: 140,
      /*  render: (e) =>
        FormatCurrency(
          handleGetItm(e, "paymentRef", data).receivedAmount,
          handleGetItm(e, "paymentRef", data).beneficiaryCurrency
        ), */
    },
    {
      title: "AGENT COMMISSION",
      dataIndex: "paymentRef",
      width: 160,
      render: (e) =>
        FormatCurrency(
          handleGetItm(e, "paymentRef", data).senderAgentCommission,
          handleGetItm(e, "paymentRef", data).beneficiaryCurrency
        ),
    },
    {
      title: "Beneficiary",
      dataIndex: "paymentRef",
      width: 340,
      render: (e) => (
        <Flex gap={"3"}>
          <div
            style={{
              color: "#5a6376",
            }}
          >
            {
              handleGetItm(e, "paymentRef", data)?.userBeneficiary?.[
                "beneficiaryName"
              ]
            }
          </div>
          <div
            onClick={() => {
              setBeneficiaryDetails(handleGetItm(e, "paymentRef", data));
            }}
          >
            <AppLink type="normal">View details</AppLink>
          </div>
        </Flex>
      ),
    },
    {
      title: "Document",
      dataIndex: "paymentRef",
      width: 220,
      render: (e) => {
        const itm = handleGetItm(e, "paymentRef", data);
        return !itm?.transactionAttachedDocumentURL ? (
          "-"
        ) : (
          <Flex gap={"3"} align={"center"}>
            <div
              style={{
                color: "#5a6376",
              }}
            >
              {itm?.transactionAttachedDocumentName}
            </div>
            <span>
              <Eye
                onClick={() => {
                  if (itm?.transactionAttachedDocumentURL?.includes("pdf")) {
                    window.open(itm?.transactionAttachedDocumentURL, "_blank");
                  } else {
                    setImage(itm?.transactionAttachedDocumentURL);
                  }
                }}
                size={15}
                color="#5a6376"
              />
            </span>
            <span>
              <Download
                onClick={() => {
                  if (itm?.transactionAttachedDocumentURL?.includes("pdf")) {
                    fetch(itm?.transactionAttachedDocumentURL)
                      .then((response) => response.blob())
                      .then((blob) => {
                        saveAs(
                          blob,
                          `${itm?.transactionAttachedDocumentName}.pdf`
                        );
                      });
                  } else {
                    saveAs(
                      itm?.transactionAttachedDocumentURL,
                      `${itm?.transactionAttachedDocumentName}.png`
                    );
                  }
                }}
                size={15}
                color="#5a6376"
              />
            </span>
          </Flex>
        );
      },
    },
    {
      title: "Customer type",
      dataIndex: "senderAgentId",
      width: 200,
      render: (e) => (e === 0 ? "DIR-TRX" : "AGENT-TRX"),
    },

    {
      title: "COUNTRY",
      dataIndex: "userBeneficiary[beneficiaryCountry][currencyCode]",
      render: (e) => <CurrencyBadge currency={e} />,
      width: 100,
    },
    {
      title: "Mobile",
      dataIndex: "",
      render: (e) => (e ? "-" : "-"),
      width: 100,
    },
    {
      title: "TRX LOCATION",
      dataIndex: "transactionLocation",
      render: (e) => {
        return (
          <div>
            <div
              onClick={() => {
                setTransactionLocation(e);
              }}
              style={{
                cursor: "pointer",
                width: "fit-content",
              }}
            >
              <CurrencyBadge currency={e?.currency?.code} />
            </div>
          </div>
        );
      },
      width: 140,
    },
    {
      title: "Source",
      dataIndex: "transactionSource",
      width: 140,
    },
    {
      title: "PAYMENT TYPE",
      dataIndex: "paymentType",
      width: 170,
    },
    {
      title: "COLLECTION TYPE",
      dataIndex: "collectionType",
      width: 170,
    },
    {
      title: "paid by",
      dataIndex: "",
      render: (e) => (e ? "-" : "-"),
      width: 160,
    },
    {
      title: "released by",
      dataIndex: "releasedBy",
      width: 150,
      render: (e) => (e ? `${e.firstName} ${e.surName}` : "-"),
    },
    {
      title: "Payout Comment",
      dataIndex: "payoutProviderMessage",
      width: 230,
    },
  ];

  const [date, setDate] = useState();

  const [search, setSearch] = useState("");

  const applyFilters = (data, search, start, end) => {
    return data?.filter((item) => {
      const itemDate = new Date(item?.paymentDate?.replace(" ", "T"));

      // Handle date filtering
      let isWithinDateRange = true;
      if (start) {
        const startDate = start;
        isWithinDateRange = itemDate >= startDate;
      }
      if (end) {
        const endDate = end;
        isWithinDateRange = isWithinDateRange && itemDate <= endDate;
      }

      // Search across all columns (all values in the row)
      let matchesSearch = true;
      if (search) {
        const lowerSearch = search.toLowerCase();
        matchesSearch = Object.values(item).some((val) =>
          typeof val === "string" || typeof val === "number"
            ? val.toString().toLowerCase().includes(lowerSearch)
            : typeof val === "object" && val !== null
            ? Object.values(val)
                .map((v) => (v ? v.toString().toLowerCase() : ""))
                .some((v) => v.includes(lowerSearch))
            : false
        );
      }

      return isWithinDateRange && matchesSearch;
    });
  };

  const newData = applyFilters(data, search, date?.[0], date?.[1]);

  console.log(newData, date, "newData");

  return (
    <div>
      <ConfirmModal
        trigger={setOpen}
        open={open}
        description={"Are you sure you want to complete this request"}
        confirm={() => {
          callMenuByType(modalType, item?.paymentRef, { userId: user_id });
        }}
        cancel={() => {
          setOpen(false);
        }}
        loading={isPending}
      />
      <TransactionDocument open={image} setOpen={setImage} data={image} />
      <TransactionReceipt open={receipt} setOpen={setReceipt} data={receipt} />
      <AddCommentToTransactionModal
        open={addCommentModal}
        setOpen={setAddCommentModal}
        item={item}
        finished={undefined}
      />
      {viewCommentModal && (
        <ViewTransactionComments
          open={viewCommentModal}
          setOpen={setViewCommentModal}
          data={item}
        />
      )}
      <BeneficiaryDetails
        open={beneficiaryDetails}
        setOpen={setBeneficiaryDetails}
        data={beneficiaryDetails}
      />
      <TransactionLocation
        open={transactionLocation}
        setOpen={setTransactionLocation}
        data={transactionLocation}
      />
      <CustomTable
        topContent={
          <>
            {topContent}
            <Flex justify={"between"} align={"end"}>
              <FormInput
                placeholder={"Search by Transaction ref or Customer ref..."}
                background="#fff"
                IconLeft={Search}
                width="400px"
                onChange={(e) => {
                  setSearch(e?.target?.value?.trim());
                }}
              />

              <Flex justify={"between"} align={"start"} gap={"4"}>
                <DatePicker.RangePicker
                  className="range"
                  placeholder={["Start Date", "End Date"]}
                  onChange={(e) => {
                    if (e?.length) {
                      setDate([
                        new Date(`${e[0]}T00:00:00`),
                        new Date(`${e[1]}T23:59:59`),
                      ]);
                    } else {
                      setDate();
                    }
                    console.log(e, "date");
                  }}
                  style={{
                    marginBottom: "10px",
                  }}
                  position="br"
                />
                {search || date ? (
                  <AppButton
                    width="160px"
                    IconLeft={Download}
                    placeholder="Download"
                    onClick={() => {
                      if (newData) downloadFile(newData, "transactions");
                    }}
                  />
                ) : (
                  ""
                )}
              </Flex>
            </Flex>
          </>
        }
        tableColumns={columns}
        loading={isLoading}
        arrayData={newData}
        scroll={{
          x: 1600,
        }}
        rowClassName={(record) =>
          record.isDuplicate ? "highlight-duplicate-row" : ""
        }
      />
    </div>
  );
}
