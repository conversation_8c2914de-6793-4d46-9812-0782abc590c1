import React, { <PERSON> } from "react";
import CustomTable from "../bits/CustomTable";
import AppButton from "../bits/AppButton";
import { Flex } from "@radix-ui/themes";
import SectionHeader from "../bits/SectionHeader";
import { useQuery } from "@tanstack/react-query";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useAuth } from "@/context/global.context";
import TransactionTableList from "./TransactionTableList";

const TodayLogs: FC = () => {
  const { user_id, refetchUserData } = useAuth();

  const { data, isLoading, refetch } = useQuery({
    queryKey: ["GetTodaysTransferLogsQuery"],
    queryFn: () => ApiServiceAdmin.GetTodaysTransferLogsQuery(user_id),
  });

  const transfers = data?.data;

  console.log(transfers);

  return (
    <div>
      <TransactionTableList
        recall={() => {
          refetchUserData();
          refetch();
        }}
        data={transfers}
        isLoading={isLoading}
        topContent={
          <>
            <Flex
              align={{ initial: "start", md: "center" }}
              direction={{ initial: "column", md: "row" }}
              justify={{ initial: "start", md: "between" }}
            >
              <SectionHeader
                title="Today's Transfer Log"
                description=""
                showBackBtn={undefined}
                align={undefined}
                fontSize="24px"
              />

              <AppButton
                placeholder="View all"
                width="100px"
                outline
                onClick={undefined}
                loading={undefined}
                disabled={undefined}
                secondary={undefined}
                borderColor={undefined}
                style={undefined}
                style2={undefined}
                IconLeft={undefined}
                IconRight={undefined}
                to={"/view-transfers"}
                roundedFull={undefined}
                radius={undefined}
              />
            </Flex>
          </>
        }
      />
    </div>
  );
};

export default TodayLogs;
