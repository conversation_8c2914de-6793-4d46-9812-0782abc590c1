import React, { <PERSON> } from "react";
import CustomTable from "../bits/CustomTable";
import AppButton from "../bits/AppButton";
import { Flex } from "@radix-ui/themes";
import SectionHeader from "../bits/SectionHeader";
import CustomerTableList from "./CustomerTableList";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/context/global.context";

const NewCustomerList: FC = () => {
  const { user_id } = useAuth();
  const { data, isLoading, isFetching, refetch } = useQuery({
    queryKey: ["GetCustomersQuery"],
    queryFn: () =>
      user_id
        ? ApiServiceAdmin.GetAgentCustomersQuery(user_id)
        : ApiServiceAdmin.GetCustomersQuery(),
  });
  return (
    <div>
      <CustomerTableList
        topContent={
          <>
            <Flex
              align={{ initial: "start", md: "center" }}
              direction={{ initial: "column", md: "row" }}
              justify={{ initial: "start", md: "between" }}
            >
              <SectionHeader
                title="New Customer List"
                description=""
                showBackBtn={undefined}
                align={undefined}
                fontSize="24px"
              />

              <AppButton
                placeholder="View all"
                width="100px"
                outline
                onClick={undefined}
                loading={undefined}
                disabled={undefined}
                secondary={undefined}
                borderColor={undefined}
                style={undefined}
                style2={undefined}
                IconLeft={undefined}
                IconRight={undefined}
                to={"/customers"}
                roundedFull={undefined}
                radius={undefined}
              />
            </Flex>
          </>
        }
        data={data}
        isLoading={isLoading || isFetching}
        refetch={refetch}
      />
    </div>
  );
};

export default NewCustomerList;
