import React, { FC } from "react";
import CustomTable from "../bits/CustomTable";
import AppButton from "../bits/AppButton";
import { Flex } from "@radix-ui/themes";
import SectionHeader from "../bits/SectionHeader";
import AdminBox from "../AdminBox";
import TransactionsChart from "../charts/TransactionsChart";

const TransactionsTypeChart: FC = ({ data, currency }: any) => {
  const colors = ["#12B76A", "#4945C4", "#E0BE2D", "#D94040", "#FEF0C7"];
  const labels = data?.map((itm) => itm.month);
  const series = [
    {
      name: "Deposited",
      data: data?.map((itm) => itm.successfulValue),
    },
    {
      name: "Processed",
      data: data?.map((itm) => itm.processingValue),
    },
    {
      name: "Pending",
      data: data?.map((itm) => itm.pendingValue),
    },
    {
      name: "Cancelled",
      data: data?.map((itm) => itm.cancelledValue),
    },
    {
      name: "On Hold",
      data: data?.map((itm) => itm.onHoldValue),
    },
  ];
  return (
    <div>
      <AdminBox title="Transactions" description="" sideChild={undefined} style={undefined}>
        <Flex gap={"4"}>
          {series.map((item, idx) => {
            return (
              <Flex
                align={"center"}
                style={{
                  fontSize: "14px",
                }}
              >
                <div
                  style={{
                    width: "13px",
                    height: "5px",
                    background: colors[idx],
                    borderRadius: "100px",
                  }}
                ></div>
                &nbsp; &nbsp;
                <div>{item.name}</div>
              </Flex>
            );
          })}
        </Flex>
        <TransactionsChart
          colors={colors}
          series={series}
          labels={labels}
          currency={currency}
        />
      </AdminBox>
    </div>
  );
};

export default TransactionsTypeChart;
