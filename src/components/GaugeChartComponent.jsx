import GaugeChart from "react-gauge-chart";
export default function GaugeChartComponent({ data }) {
  return (
    <div>
      <GaugeChart
        nrOfLevels={3}
        colors={["#6AB36C", "#FDA333", "#EB615E"]}
        arcWidth={0.23}
        arcsLength={[0.2, 0.2, 0.2]}
        cornerRadius={0}
        style={{ width: "100%" }}
        textColor="#fd9c0a0"
        hideText={false}
        percent={data?.userAssesmentScore / 33}
        marginInPercent={0}
        arcPadding={0}
      />

      <div
        style={{
          fontSize: "32px",
          color:
            data?.userRiskLevel === "Low"
              ? "#37d744"
              : data?.userRiskLevel === "Medium"
              ? "#FFC371"
              : "#ff6363",
          textTransform: "uppercase",
          fontWeight: 600,
          textAlign: "center",
        }}
      >
        {data?.userAssesmentScore} / 33
        <br />
        {data?.userRiskLevel}
      </div>
    </div>
  );
}
