import React, { FC, useState } from "react";
import CustomTable from "../bits/CustomTable";
import AppButton from "../bits/AppButton";
import { Flex } from "@radix-ui/themes";
import SectionHeader from "../bits/SectionHeader";
import { StatusBadge } from "../bits/StatusBadge";
import AppLink from "../bits/AppLink";
import { useNavigate } from "react-router-dom";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/context/global.context";
import { Download, Search } from "lucide-react";
import { downloadFile } from "@/lib/utils";
import { DatePicker } from "@arco-design/web-react";
import FormInput from "../bits/FormInput";

const ClientListTable: FC = ({ topContent, data }: any) => {
  const route = useNavigate();
  const { user_id } = useAuth();

  const { data: innerData, isLoading } = useQuery({
    queryKey: ["GetPayoutClientTableDashboard"],
    queryFn: () => ApiServiceAdmin.GetPayoutClientDashboard(user_id),
  });
  const columns = [
    {
      title: "Action",
      dataIndex: "userId",
      render: (e, record) => (
        <div
          onClick={() => {
            localStorage.setItem("client", JSON.stringify(record));
            route(`/client/${e}?tab=profile`);
          }}
        >
          <AppLink to={undefined} onClick={undefined}>
            View Details
          </AppLink>
        </div>
      ),
    },
    {
      title: "client id",
      dataIndex: "userId",
    },
    {
      title: "id verification",
      dataIndex: "isKYCCompleted",
      render: (e) => <StatusBadge status={e ? "verified" : "not verified"} />,
    },
    {
      title: "status",
      dataIndex: "status",
      render: (e) => <StatusBadge status={e} />,
    },
    {
      title: "name",
      dataIndex: "companyName",
    },
    {
      title: "address",
      dataIndex: "address",
    },
    {
      title: "email",
      dataIndex: "email",
    },
    {
      title: "phone",
      dataIndex: "phone",
    },
    {
      title: "date added",
      dataIndex: "dateCreated",
    },
  ];

  const [date, setDate] = useState<[Date, Date] | undefined>();

  const [search, setSearch] = useState("");

  const applyFilters = (data, search, start, end) => {
    return data?.filter((item) => {
      const itemDate = new Date(item?.dateCreated?.replace(" ", "T"));

      // Handle date filtering
      let isWithinDateRange = true;
      if (start) {
        const startDate = start;
        isWithinDateRange = itemDate >= startDate;
      }
      if (end) {
        const endDate = end;
        isWithinDateRange = isWithinDateRange && itemDate <= endDate;
      }

      // Handle search filtering (on paymentRef or userId)
      let matchesSearch = true;
      if (search) {
        const lowerSearch = search.toLowerCase();
        const emailMatch = item?.email
          ?.toString()
          ?.toLowerCase()
          ?.includes(lowerSearch);
        const refMatch = item?.userId
          ?.toString()
          ?.toLowerCase()
          ?.includes(lowerSearch);
        const userMatch = item?.companyName
          ?.toString()
          ?.toLowerCase()
          ?.includes(lowerSearch);
        const client = item?.address
          ?.toString()
          ?.toLowerCase()
          ?.includes(lowerSearch);
        matchesSearch = refMatch || userMatch || client || emailMatch;
      }

      return isWithinDateRange && matchesSearch;
    });
  };

  const newData = applyFilters(
    innerData?.data?.allPayoutClients,
    search,
    date && date?.[0],
    date && date?.[1]
  );

  return (
    <div>
      <CustomTable
        Apidata={undefined}
        tableColumns={columns}
        topContent={
          topContent ? (
            topContent
          ) : (
            <>
              <Flex
                align={{ initial: "start", md: "center" }}
                direction={{ initial: "column", md: "row" }}
                justify={{ initial: "start", md: "between" }}
              >
                <SectionHeader
                  title="Clients"
                  description=""
                  showBackBtn={undefined}
                  align={undefined}
                  fontSize="24px"
                />
              </Flex>

              <Flex justify={"between"} align={"end"}>
                <FormInput
                  placeholder={"Search by User Id, Name or Address..."}
                  background="#fff"
                  IconLeft={Search}
                  width="400px"
                  onChange={(e) => {
                    setSearch(e?.target?.value?.trim());
                  }}
                  name={undefined}
                  type={undefined}
                  label={undefined}
                  disabled={undefined}
                  formik={undefined}
                  max={undefined}
                  IconRight={undefined}
                  hint={undefined}
                />

                <Flex justify={"between"} align={"start"} gap={"4"}>
                  <DatePicker.RangePicker
                    className="range"
                    placeholder={["Start Date", "End Date"]}
                    onChange={(e) => {
                      if (e?.length) {
                        setDate([
                          new Date(`${e[0]}T00:00:00`),
                          new Date(`${e[1]}T23:59:59`),
                        ]);
                      } else {
                        setDate(undefined);
                      }
                      console.log(e, "date");
                    }}
                    style={{
                      marginBottom: "10px",
                    }}
                    position="br"
                  />
                  {search || date ? (
                    <AppButton
                      width="160px"
                      IconLeft={Download}
                      placeholder="Download"
                      onClick={() => {
                        if (newData) downloadFile(newData, "logs");
                      }}
                      loading={undefined}
                      disabled={undefined}
                      outline={undefined}
                      secondary={undefined}
                      borderColor={undefined}
                      style={undefined}
                      style2={undefined}
                      IconRight={undefined}
                      to={undefined}
                      roundedFull={undefined}
                      radius={undefined}
                    />
                  ) : (
                    ""
                  )}
                </Flex>
              </Flex>
            </>
          )
        }
        loading={isLoading}
        noData={undefined}
        arrayData={
          newData?.length ? newData : innerData?.data?.allPayoutClients
        }
        setPage={undefined}
        total={undefined}
        currentPage={undefined}
        scroll={{
          x: 1400,
        }}
        tableWidth={undefined}
      />
    </div>
  );
};

export default ClientListTable;
