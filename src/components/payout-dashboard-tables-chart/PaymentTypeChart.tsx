import React, { <PERSON> } from "react";
import CustomTable from "../bits/CustomTable";
import AppButton from "../bits/AppButton";
import { Flex } from "@radix-ui/themes";
import SectionHeader from "../bits/SectionHeader";
import AdminBox from "../AdminBox";
import TransactionsChart from "../charts/TransactionsChart";

const PaymentTypeChart: FC = () => {
  const colors = ["#5E5ADB", "#F04043", "#DC964E", "#5196D6"];
  const labels = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "June",
    "July",
    "Aug",
    "Sept",
    "Oct",
    "Nov",
    "Dec",
  ];
  const series = [
    {
      name: "Pay By Card",
      data: [200, 400, 500, 200, 400, 200, 400, 500, 200, 400, 300, 240],
    },
    {
      name: "Cash",
      data: [200, 400, 300, 200, 400, 200, 400, 300, 200, 400, 500, 300],
    },
    {
      name: "Bank Transfer",
      data: [200, 400, 500, 200, 400, 200, 400, 500, 200, 400, 300, 240],
    },
    {
      name: "Pay With Bank",
      data: [200, 400, 300, 200, 400, 200, 400, 300, 200, 400, 500, 300],
    },
  ];
  return (
    <div>
      <AdminBox title="Payment Type" description="" sideChild={undefined}>
        <Flex gap={"4"}>
          {series.map((item, idx) => {
            return (
              <Flex
                align={"center"}
                style={{
                  fontSize: "14px",
                }}
              >
                <div
                  style={{
                    width: "13px",
                    height: "5px",
                    background: colors[idx],
                    borderRadius: "100px",
                  }}
                ></div>
                &nbsp; &nbsp;
                <div>{item.name}</div>
              </Flex>
            );
          })}
          {/*  <Flex
            align={"center"}
            style={{
              fontSize: "14px",
            }}
          >
            <div
              style={{
                width: "13px",
                height: "5px",
                background: "#F04F4F",
                borderRadius: "100px",
              }}
            ></div>
            &nbsp; &nbsp;
            <div>Failed</div>
          </Flex> */}
        </Flex>
        <TransactionsChart colors={colors} series={series} labels={labels} />
      </AdminBox>
    </div>
  );
};

export default PaymentTypeChart;
