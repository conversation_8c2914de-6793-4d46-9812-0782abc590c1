import React, { FC, useState } from "react";
import CustomTable from "../bits/CustomTable";
import AppButton from "../bits/AppButton";
import { Flex } from "@radix-ui/themes";
import SectionHeader from "../bits/SectionHeader";
import { StatusBadge } from "../bits/StatusBadge";
import {
  CFormatterNaira,
  downloadFile,
  handleGetItm,
  handleSetItem,
} from "@/lib/utils";
import { CurrencyBadge } from "../bits/CurrencyBadge";
import { DotsHorizontalIcon } from "@radix-ui/react-icons";
import { MenuItem, MenuList } from "../bits/DropDownMenu";
import ApproveClientFundRequestModal from "@/Modals/ApproveClientFundRequest";
import ConfirmModal from "@/Modals/ConfirmModal";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useMutation } from "@tanstack/react-query";
import { DatePicker } from "@arco-design/web-react";
import FormInput from "../bits/FormInput";
import { Download, Search } from "lucide-react";

const ClientFundRequestLog: FC = ({ data, refetch }: any) => {
  const [open, setOpen] = useState(false);
  const [type, setType] = useState(false);

  const userMenu = [
    {
      name: "Approve",
      action: () => {
        setOpen(true);
        setType(true);
      },
      index: 1,
    },
    {
      name: "Decline",
      color: "red",
      action: () => {
        setOpen(true);
        setType(false);
      },
      index: 2,
    },
  ];
  const [item, setItem] = useState<any>();
  const columns = [
    {
      title: "Action",
      dataIndex: "id",
      fixed: "left",
      width: 100,
      render: (e, record) =>
        record?.status?.toLowerCase() === "pending" ? (
          <MenuList
            onClick={() => {
              handleSetItem(e, data, setItem);
            }}
            ActionIcon={undefined}
            ActionElement={undefined}
            iconWidth={undefined}
          >
            {userMenu?.map(({ name, action, index, color }) => {
              return (
                <>
                  <MenuItem
                    name={name}
                    index={index}
                    action={() => {
                      action();
                    }}
                    Icon={undefined}
                    to={undefined}
                    width={"160px"}
                    padding={2}
                    color={color}
                  />
                </>
              );
            })}
          </MenuList>
        ) : (
          "-"
        ),
    },
    {
      title: "Request status",
      dataIndex: "status",
      render: (e) => <StatusBadge status={e} />,
    },
    {
      title: "client id",
      dataIndex: "userId",
    },
    {
      title: "transaction ref",
      dataIndex: "id",
    },
    {
      title: "client",
      dataIndex: "clientName",
    },
    {
      title: "gateway",
      dataIndex: "userWallet['name']",
    },
    {
      title: "currency",
      dataIndex: "userWallet['currency']['code']",
      render: (e) => <CurrencyBadge currency={e} />,
    },
    {
      title: "amount",
      dataIndex: "amountRequested",
      render: (e) => CFormatterNaira(e),
    },
    {
      title: "Balance Before Request",
      dataIndex: "balanceBeforeRequest",
      render: (e) => CFormatterNaira(e),
      width: 170,
    },
    {
      title: "date",
      dataIndex: "dateCreated",
    },
  ];

  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.ProcessClientFundRequestMutation,
    onSuccess: () => {
      setOpen(false);
      refetch();
    },
    onError: () => {
      return;
    },
  });

  const [date, setDate] = useState<[Date, Date] | undefined>();

  const [search, setSearch] = useState("");

  const applyFilters = (data, search, start, end) => {
    return data?.filter((item) => {
      const itemDate = new Date(item?.dateCreated?.replace(" ", "T"));

      // Handle date filtering
      let isWithinDateRange = true;
      if (start) {
        const startDate = start;
        isWithinDateRange = itemDate >= startDate;
      }
      if (end) {
        const endDate = end;
        isWithinDateRange = isWithinDateRange && itemDate <= endDate;
      }

      // Handle search filtering (on paymentRef or userId)
      let matchesSearch = true;
      if (search) {
        const lowerSearch = search.toLowerCase();
        const refMatch = item?.id
          ?.toString()
          ?.toLowerCase()
          ?.includes(lowerSearch);
        const userMatch = item?.userWallet?.name
          ?.toString()
          ?.toLowerCase()
          ?.includes(lowerSearch);
        const client = item?.clientName
          ?.toString()
          ?.toLowerCase()
          ?.includes(lowerSearch);
        matchesSearch = refMatch || userMatch || client;
      }

      return isWithinDateRange && matchesSearch;
    });
  };

  const newData = applyFilters(
    data,
    search,
    date && date?.[0],
    date && date?.[1]
  );

  return (
    <div>
      <ConfirmModal
        trigger={setOpen}
        open={open}
        description={"Are you sure you want to complete this request"}
        confirm={() => {
          mutate({
            updatedBy: 0,
            objectId: item?.id,
            action: type ? 1 : 0,
          });
        }}
        cancel={() => {
          setOpen(false);
        }}
        loading={isPending}
      />

      <CustomTable
        arrayData={newData?.length ? newData : data}
        tableColumns={columns}
        topContent={
          <>
            <SectionHeader
              title="Client Fund Request Log"
              description=""
              showBackBtn={undefined}
              align={undefined}
              fontSize="24px"
            />

            <Flex justify={"between"} align={"end"}>
              <FormInput
                placeholder={"Search by Transaction ref, Client or Gateway..."}
                background="#fff"
                IconLeft={Search}
                width="400px"
                onChange={(e) => {
                  setSearch(e?.target?.value?.trim());
                }}
                name={undefined}
                type={undefined}
                label={undefined}
                disabled={undefined}
                formik={undefined}
                max={undefined}
                IconRight={undefined}
                hint={undefined}
              />

              <Flex justify={"between"} align={"start"} gap={"4"}>
                <DatePicker.RangePicker
                  className="range"
                  placeholder={["Start Date", "End Date"]}
                  onChange={(e) => {
                    if (e?.length) {
                      setDate([
                        new Date(`${e[0]}T00:00:00`),
                        new Date(`${e[1]}T23:59:59`),
                      ]);
                    } else {
                      setDate(undefined);
                    }
                    console.log(e, "date");
                  }}
                  style={{
                    marginBottom: "10px",
                  }}
                  position="br"
                />
                {search || date ? (
                  <AppButton
                    width="160px"
                    IconLeft={Download}
                    placeholder="Download"
                    onClick={() => {
                      if (newData) downloadFile(newData, "logs");
                    }}
                    loading={undefined}
                    disabled={undefined}
                    outline={undefined}
                    secondary={undefined}
                    borderColor={undefined}
                    style={undefined}
                    style2={undefined}
                    IconRight={undefined}
                    to={undefined}
                    roundedFull={undefined}
                    radius={undefined}
                  />
                ) : (
                  ""
                )}
              </Flex>
            </Flex>
          </>
        }
        loading={undefined}
        noData={undefined}
        Apidata={undefined}
        setPage={undefined}
        total={undefined}
        currentPage={undefined}
        scroll={{
          x: 1400,
        }}
        tableWidth={undefined}
      />
    </div>
  );
};

export default ClientFundRequestLog;
