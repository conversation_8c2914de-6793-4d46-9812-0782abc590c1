import React, { FC, useState } from "react";
import CustomTable from "../bits/CustomTable";
import { Flex } from "@radix-ui/themes";
import SectionHeader from "../bits/SectionHeader";
import { StatusBadge } from "../bits/StatusBadge";
import { ProviderBadge } from "../bits/ProviderBadge";
import { CurrencyBadge } from "../bits/CurrencyBadge";
import { FormatCurrency } from "@/lib/utils";
import FormInput from "../bits/FormInput";
import { Search } from "lucide-react";
import { DatePicker } from "@arco-design/web-react";

const PayoutTransactionLogs: FC = ({ data }: any) => {
  const columns = [
    {
      title: "transaction ref",
      dataIndex: "clientRef",
      width: 200,
    },
    {
      title: "date",
      dataIndex: "dateCreated",
      width: 200,
    },
    /*     {
      title: "ID",
      dataIndex: "payoutClientId",
      width: 200,
    }, */
    {
      title: "wallet id",
      width: 200,
      dataIndex: "walletId",
    },
    {
      title: "transaction status",
      dataIndex: "status",
      render: (e) => <StatusBadge status={e} />,
      width: 200,
    },
    {
      title: "Wallet Balance",
      dataIndex: "walletBalance",
      render: (e, record) => FormatCurrency(e, record?.currency?.code),
      width: 200,
    },
    {
      title: "Receiver",
      dataIndex: "beneficiary[beneficiaryName]",
      width: 200,
    },
    {
      title: "Bank Name",
      dataIndex: "beneficiary[beneficiaryBank][bankName]",
      width: 200,
    },
    {
      title: "client id",
      dataIndex: "payoutClientApp[id]",
      width: 200,
    },
    /*     {
      title: "client",
      dataIndex: "payoutClientApp[appName]",
      width: 200,
    }, */
    {
      title: "gateway",
      dataIndex: "payOutProvider[name]",
      render: (e, record) => <ProviderBadge name={e} logo={record?.logo} />,
      width: 200,
    },
    {
      title: "Transaction type",
      dataIndex: "transactionType",
      render: (e) => <StatusBadge status={e?.toLowerCase()} />,
      width: 200,
    },
    {
      title: "Balance before",
      dataIndex: "Amount",
      render: (e, record) => FormatCurrency(e, record?.currency?.code),
      width: 200,
    },

    {
      title: "Account Number",
      dataIndex: "beneficiary[beneficiaryBank][accountNumber]",
      width: 200,
    },
    {
      title: "Transfer fee",
      dataIndex: "transferFee",
      render: (e, record) => FormatCurrency(e, record?.currency?.code),
      width: 200,
    },
    {
      title: "currency",
      dataIndex: "currency",
      render: (e) => <CurrencyBadge currency={e?.code} />,
      width: 200,
    },
  ];
  const [date, setDate] = useState<[Date, Date] | undefined>();

  const [search, setSearch] = useState("");

  const applyFilters = (data, search, start, end) => {
    return data?.filter((item) => {
      const itemDate = new Date(item?.dateCreated?.replace(" ", "T"));

      // Handle date filtering
      let isWithinDateRange = true;
      if (start) {
        const startDate = start;
        isWithinDateRange = itemDate >= startDate;
      }
      if (end) {
        const endDate = end;
        isWithinDateRange = isWithinDateRange && itemDate <= endDate;
      }

      // Handle search filtering (on paymentRef or userId)
      let matchesSearch = true;
      if (search) {
        const lowerSearch = search.toLowerCase();
        const refMatch = item?.clientRef
          ?.toString()
          ?.toLowerCase()
          ?.includes(lowerSearch);
        const userMatch = item?.clientRef
          ?.toString()
          ?.toLowerCase()
          ?.includes(lowerSearch);
        matchesSearch = refMatch || userMatch;
      }

      return isWithinDateRange && matchesSearch;
    });
  };

  const newData = applyFilters(data, search, date?.[0], date?.[1]);

  console.log(newData, "newData");

  return (
    <div>
      <CustomTable
        Apidata={undefined}
        tableColumns={columns}
        topContent={
          <>
            <SectionHeader
              title="Payout Transaction Logs"
              description=""
              showBackBtn={undefined}
              align={undefined}
              fontSize="24px"
            />
            <Flex justify={"between"} align={"end"}>
              <FormInput
                placeholder={"Search..."}
                background="#fff"
                IconLeft={Search}
                width="400px"
                onChange={(e) => {
                  setSearch(e?.target?.value?.trim());
                }}
                name={undefined}
                type={undefined}
                label={undefined}
                disabled={undefined}
                formik={undefined}
                max={undefined}
                IconRight={undefined}
                hint={undefined}
              />

              <DatePicker.RangePicker
                className="range"
                placeholder={["Start Date", "End Date"]}
                onChange={(e) => {
                  if (e?.length) {
                    setDate([
                      new Date(`${e[0]}T00:00:00`),
                      new Date(`${e[1]}T23:59:59`),
                    ]);
                  } else {
                    setDate(undefined);
                  }
                  console.log(e, "date");
                }}
                style={{
                  marginBottom: "10px",
                }}
                position="br"
              />
            </Flex>
          </>
        }
        loading={undefined}
        noData={undefined}
        arrayData={newData?.length ? newData : data}
        setPage={undefined}
        total={undefined}
        currentPage={undefined}
        scroll={{
          x: 1400,
        }}
        tableWidth={undefined}
      />
    </div>
  );
};

export default PayoutTransactionLogs;
