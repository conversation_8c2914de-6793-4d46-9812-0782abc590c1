import React, { <PERSON> } from "react";
import CustomTable from "../bits/CustomTable";
import AppButton from "../bits/AppButton";
import { Flex } from "@radix-ui/themes";
import SectionHeader from "../bits/SectionHeader";
import AdminBox from "../AdminBox";
import TransactionsChart from "../charts/TransactionsChart";

const TransactionsTypeChart: FC = ({ data, currency }: any) => {
  const colors = ["#12B76A", "#E0BE2D", "#D94040"];
  const labels = data?.map((itm) => itm.month);
  const series = [
    {
      name: "Successful",
      data: data?.map((itm) => itm.successfulValue),
    },

    {
      name: "Pending",
      data: data?.map((itm) => itm.pendingValue),
    },
    {
      name: "Failed",
      data: data?.map((itm) => itm.failedValue),
    },
  ];
  return (
    <div>
      <AdminBox title="Transactions" description="" sideChild={undefined}>
        <Flex gap={"4"}>
          {series.map((item, idx) => {
            return (
              <Flex
                align={"center"}
                style={{
                  fontSize: "14px",
                }}
              >
                <div
                  style={{
                    width: "13px",
                    height: "5px",
                    background: colors[idx],
                    borderRadius: "100px",
                  }}
                ></div>
                &nbsp; &nbsp;
                <div>{item.name}</div>
              </Flex>
            );
          })}
        </Flex>
        <TransactionsChart
          colors={colors}
          series={series}
          labels={labels}
          currency={currency}
        />
      </AdminBox>
    </div>
  );
};

export default TransactionsTypeChart;
