import Box from "./bits/Box";

export default function BoxListHorizontal({
  list = [
    { title: "List", value: "list" },
    { title: "List", value: "list" },
  ],
}) {
  return (
    <Box
      background={"#F9FAFB"}
      style={{
        display: "grid",
        gridTemplateColumns: "1fr 1fr 1fr 1fr",
      }}
    >
      {list?.map((itm, idx) => (
        <div
          style={{
            textAlign: "center",
            width: "100%",
            borderRight: idx === list?.length - 1 ? "" : "1px solid #E9EDF5",
            paddingInline: "8px",
          }}
        >
          <div>
            <p
              style={{
                color: "#5A6376",
                width: "100%",
                fontSize: "14px",
              }}
            >
              {itm?.title}
            </p>
            <div
              style={{
                textAlign: "center",
                width: "100%",
                marginTop: "1rem",
              }}
            >
              {itm?.value}
            </div>
          </div>
        </div>
      ))}
    </Box>
  );
}
