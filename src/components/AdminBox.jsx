import { Flex } from "@radix-ui/themes";
import Box from "./bits/Box";
import SectionHeader from "./bits/SectionHeader";

export default function AdminBox({
  children,
  sideChild,
  title = "",
  description = "",
  style,
}) {
  return (
    <Box style={style}>
      <Flex
        align={{ initial: "flex-start", md: "flex-start" }}
        direction={{ initial: "column", md: "row" }}
        justify={{ initial: "flex-start", md: "between" }}
      >
        <SectionHeader
          title={title}
          description={description}
          fontSize="20px"
        />
        {sideChild}
      </Flex>
      {children}
    </Box>
  );
}
