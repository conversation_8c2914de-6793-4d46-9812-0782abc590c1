import React, { <PERSON> } from "react";
import TransactionTableList from "../dashboard-tables/TransactionTableList";
import { useQuery } from "@tanstack/react-query";
import { ApiServiceAdmin } from "@/service/admin-services";

const TransactionTab: FC<any> = ({ data }) => {
  const {
    data: transfers,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["GetTransferListUserQuery"],
    queryFn: () => ApiServiceAdmin.GetTransferListQuery(data?.userId),
  });
  return (
    <div>
      <TransactionTableList
        recall={refetch}
        data={transfers?.data}
        isLoading={isLoading}
        topContent={undefined}
      />
    </div>
  );
};

export default TransactionTab;
