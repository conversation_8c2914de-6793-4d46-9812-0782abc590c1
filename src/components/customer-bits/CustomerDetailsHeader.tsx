import { Flex, Grid } from "@radix-ui/themes";
import React, { FC } from "react";
import CurrencyFlagImage from "react-currency-flags";
import { StatusBadge } from "../bits/StatusBadge";
import AppButton from "../bits/AppButton";
import { useNavigate } from "react-router-dom";
import { Pencil } from "lucide-react";
import { useAuth } from "@/context/global.context";

const CustomerDetailsHeader: FC<any> = ({ data }) => {
  const { menuAccessRoutes } = useAuth();
  const menu = menuAccessRoutes?.find((itm) => itm?.menuId === 3);
  const access = menu?.menuAccessType?.id;
  const navigate = useNavigate();
  return (
    <Grid columns={{ md: "2", initial: "1" }}>
      <Grid
        columns={{ md: "1fr 4fr 1fr", initial: "1" }}
        gap={"4"}
        style={{
          borderRight: "1px solid #b1b1b1",
          paddingRight: "1rem",
        }}
      >
        <img
          style={{
            width: "130px",
            height: "130px",
            borderRadius: "999px",
            objectFit: "contain",
            border: "1px solid #e6e6e6",
          }}
          src={data?.profileImageURL}
        />
        <div>
          <Flex gap={"2"} align={"center"}>
            <h2>
              {data?.firstName} {data?.surName}
            </h2>
            {data?.country?.currencyCode && (
              <CurrencyFlagImage
                currency={data?.country?.currencyCode}
                style={{
                  height: "20px",
                  width: "20px",
                  display: "grid",
                  placeItems: "center",
                  border: "1px solid #cecece",
                  borderRadius: "999px",
                  overflow: "hidden",
                }}
                size="sm"
              />
            )}
          </Flex>
          <div
            style={{
              marginBlock: "1rem",
            }}
          >
            <span style={{ color: "#909090" }}>Reference</span>
            <p>{data?.reference}</p>
          </div>

          <div
            style={{
              marginBlock: "1rem",
            }}
          >
            <span style={{ color: "#909090" }}>Registration Date</span>
            <p>{data?.dateCreated}</p>
          </div>

          <div
            style={{
              marginBlock: "1rem",
            }}
          >
            <span style={{ color: "#909090" }}>DOB:</span>
            <p>{data?.dob}</p>
          </div>
        </div>
        <div
          style={{
            textAlign: "right",
          }}
        >
          <StatusBadge status={data?.status} />
          <div
            style={{
              marginBottom: "14px",
            }}
          ></div>
          <StatusBadge
            status={data?.isKYCCompleted ? "verified" : "not verified"}
          />
          <br />
          <br />
          {access === 2 ? (
            ""
          ) : (
            <AppButton
              placeholder={"Edit"}
              onClick={() => {
                navigate(`/edit-user`, {
                  state: data,
                });
              }}
              loading={undefined}
              disabled={undefined}
              outline={undefined}
              secondary={undefined}
              borderColor={undefined}
              style={undefined}
              style2={undefined}
              IconLeft={Pencil}
              IconRight={undefined}
              to={undefined}
              roundedFull={undefined}
              radius={undefined}
            />
          )}
        </div>
      </Grid>
      <div
        style={{
          paddingInline: "20px",
        }}
      >
        <div>
          <span style={{ color: "#909090" }}>Address</span>
          <p>{data?.address}</p>
        </div>
        <div
          style={{
            marginBlock: "1rem",
          }}
        >
          <span style={{ color: "#909090" }}>Mobile Number</span>
          <p>{data?.phone}</p>
        </div>
        <div
          style={{
            marginBlock: "1rem",
          }}
        >
          <span style={{ color: "#909090" }}>Email Address</span>
          <p>{data?.email}</p>
        </div>

        <div
          style={{
            marginBlock: "1rem",
          }}
        >
          <span style={{ color: "#909090" }}>Face Confidence Match Score</span>
          <div
            style={{
              borderRadius: "7px",
              overflow: "hidden",
              background: "#d8d8d8",
              width: "350px",
              fontWeight: "700",
              marginTop: "0.5rem",
            }}
          >
            <div
              style={{
                padding: "4px 10px",
                borderRadius: "7px",
                background:
                  data?.faceMatchConfidenceScore < 50
                    ? "#ff6363"
                    : data?.faceMatchConfidenceScore < 50
                    ? "#ff6363"
                    : data?.faceMatchConfidenceScore > 75
                    ? "#37d744"
                    : "#d7ac37",
                color: "white",
                width: `${data?.faceMatchConfidenceScore}%`,
                fontWeight: "700",
              }}
            >
              <div
                style={{
                  color: "#fff",
                  width: "100px",
                }}
              >
                {data?.faceMatchConfidenceScore}%
              </div>
            </div>
          </div>
        </div>
      </div>
    </Grid>
  );
};

export default CustomerDetailsHeader;
