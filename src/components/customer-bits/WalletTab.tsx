import React, { FC, useEffect, useState } from "react";
import TransactionTableList from "../dashboard-tables/TransactionTableList";
import { useQuery } from "@tanstack/react-query";
import { ApiServiceAdmin } from "@/service/admin-services";
import moment from "moment";
import CustomTable from "../bits/CustomTable";
import { applyFilters, FormatCurrency } from "@/lib/utils";
import { Flex, Grid } from "@radix-ui/themes";
import FormInput from "../bits/FormInput";
import { Search, Wallet } from "lucide-react";
import { StatusBadge } from "../bits/StatusBadge";
import Box from "../bits/Box";
import { CurrencyBadge } from "../bits/CurrencyBadge";
import AppButton from "../bits/AppButton";
import FundCustomerModal from "@/Modals/FundCustomerWallet";
import DepleteCustomerWalletModal from "@/Modals/DepleteCustomerWallet";
import MainSelect from "../bits/MainSelect";

const WalletTab: FC<any> = ({ data, refetch }) => {
  const columns = [
    {
      title: "Transaction ID",
      dataIndex: "id",
      width: 200,
    },
    {
      title: "Date Created",
      dataIndex: "dateCreated",
      width: 200,
    },
    {
      title: "Currenncy",
      dataIndex: "currency",
      render: (e) => <CurrencyBadge currency={e} />,
      width: 200,
    },
    {
      title: "Note",
      dataIndex: "note",
      width: 200,
    },
    {
      title: "Amount",
      dataIndex: "amount",
      render: (e, record) => FormatCurrency(e, record?.currency),
      width: 200,
    },
    {
      title: "Request Type",
      dataIndex: "requestType",
      render: (e, record) => (
        <Flex align={"center"} gap={"2"}>
          {e === "Credit" ? (
            <svg
              width="12"
              height="12"
              viewBox="0 0 12 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10.898 1.89828L2.85773 9.93781H8.8125C8.96168 9.93781 9.10476 9.99708 9.21025 10.1026C9.31574 10.2081 9.375 10.3511 9.375 10.5003C9.375 10.6495 9.31574 10.7926 9.21025 10.8981C9.10476 11.0036 8.96168 11.0628 8.8125 11.0628H1.5C1.35082 11.0628 1.20774 11.0036 1.10225 10.8981C0.996763 10.7926 0.9375 10.6495 0.9375 10.5003V3.18781C0.9375 3.03863 0.996763 2.89555 1.10225 2.79007C1.20774 2.68458 1.35082 2.62531 1.5 2.62531C1.64918 2.62531 1.79226 2.68458 1.89775 2.79007C2.00324 2.89555 2.0625 3.03863 2.0625 3.18781V9.14258L10.102 1.10234C10.1543 1.05008 10.2163 1.00863 10.2846 0.980342C10.3529 0.952058 10.4261 0.9375 10.5 0.9375C10.5739 0.9375 10.6471 0.952058 10.7154 0.980342C10.7837 1.00863 10.8457 1.05008 10.898 1.10234C10.9502 1.15461 10.9917 1.21665 11.02 1.28493C11.0483 1.35322 11.0628 1.4264 11.0628 1.50031C11.0628 1.57422 11.0483 1.64741 11.02 1.71569C10.9917 1.78398 10.9502 1.84602 10.898 1.89828Z"
                fill="#12B76A"
              />
            </svg>
          ) : (
            <svg
              width="12"
              height="12"
              viewBox="0 0 12 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M11.0628 1.5V8.8125C11.0628 8.96168 11.0036 9.10476 10.8981 9.21025C10.7926 9.31574 10.6495 9.375 10.5003 9.375C10.3511 9.375 10.2081 9.31574 10.1026 9.21025C9.99708 9.10476 9.93781 8.96168 9.93781 8.8125V2.85773L1.89828 10.898C1.79273 11.0035 1.64958 11.0628 1.50031 11.0628C1.35105 11.0628 1.20789 11.0035 1.10234 10.898C0.996796 10.7924 0.9375 10.6493 0.9375 10.5C0.9375 10.3507 0.996796 10.2076 1.10234 10.102L9.14258 2.0625H3.18781C3.03863 2.0625 2.89555 2.00324 2.79007 1.89775C2.68458 1.79226 2.62531 1.64918 2.62531 1.5C2.62531 1.35082 2.68458 1.20774 2.79007 1.10225C2.89555 0.996763 3.03863 0.9375 3.18781 0.9375H10.5003C10.6495 0.9375 10.7926 0.996763 10.8981 1.10225C11.0036 1.20774 11.0628 1.35082 11.0628 1.5Z"
                fill="#FB3748"
              />
            </svg>
          )}

          <div
            style={{
              color: e === "Credit" ? "#12B76A" : "#FB3748",
            }}
          >
            {e}
          </div>
        </Flex>
      ),
      width: 200,
    },
    {
      title: "Transaction Status",
      dataIndex: "status",
      width: 200,
      render: (e) => <StatusBadge status={e} />,
    },
    {
      title: "date",
      dataIndex: "dateCreated",
      width: 200,
    },
  ];

  const wallets = data?.wallet?.map((itm) => {
    return {
      ...itm,

      value: itm?.walletId,
    };
  });
  const [fundCustomerModal, setFundCustomerModal] = useState(false);
  const [depleteCustomerModal, setDepleteCustomerModal] = useState(false);
  const [wallet, setWallet] = useState({ walletId: "" });
  useEffect(() => {
    setWallet(wallets?.[0]);
  }, [data?.wallet]);
  return (
    <div>
      {fundCustomerModal && (
        <FundCustomerModal
          open={fundCustomerModal}
          setOpen={setFundCustomerModal}
          item={{ userId: data?.userId }}
          finished={refetch}
        />
      )}
      {depleteCustomerModal && (
        <DepleteCustomerWalletModal
          open={depleteCustomerModal}
          setOpen={setDepleteCustomerModal}
          item={{ userId: data?.userId }}
          finished={refetch}
        />
      )}
      <Grid columns={{ initial: "1", md: "3" }} gap={"6"}>
        {data?.wallet && !data?.wallet.length ? (
          <div>No Selected gateways</div>
        ) : (
          data?.wallet &&
          data?.wallet.map((itm) => {
            return (
              <Box
                style={{
                  textAlign: "center",
                  color: "#5A6376",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                }}
              >
                <CurrencyBadge
                  currency={itm?.currency?.code}
                  name={itm?.name}
                />
                <h1 style={{ marginBlock: 20 }}>
                  {FormatCurrency(itm?.balance, itm?.currency?.code)}
                </h1>

                <Flex gap={"4"}>
                  <AppButton
                    placeholder="Fund Wallet"
                    width="130px"
                    outline={undefined}
                    onClick={() => {
                      setFundCustomerModal(true);
                    }}
                    color="#039855"
                    textColor="white"
                    loading={undefined}
                    disabled={undefined}
                    secondary={undefined}
                    borderColor={undefined}
                    style={undefined}
                    style2={undefined}
                    IconLeft={Wallet}
                    IconRight={undefined}
                    to={undefined}
                    roundedFull={undefined}
                    radius={undefined}
                  />
                  <AppButton
                    placeholder="Deplete Wallet"
                    width="160px"
                    outline={undefined}
                    onClick={() => {
                      setDepleteCustomerModal(true);
                    }}
                    color="#F04438"
                    textColor="white"
                    loading={undefined}
                    disabled={undefined}
                    secondary={undefined}
                    borderColor={undefined}
                    style={undefined}
                    style2={undefined}
                    IconLeft={Wallet}
                    IconRight={undefined}
                    to={undefined}
                    roundedFull={undefined}
                    radius={undefined}
                  />
                </Flex>
              </Box>
            );
          })
        )}
      </Grid>
      <br />
      <CustomTable
        tableColumns={columns}
        arrayData={data?.walletTransactions?.filter(
          (itm) => itm?.walletId === wallet?.walletId
        )}
        scroll={{
          x: 1600,
        }}
        Apidata={undefined}
        noData={undefined}
        setPage={undefined}
        total={undefined}
        currentPage={undefined}
        tableWidth={undefined}
        loading={undefined}
        topContent={
          <div
            style={{
              width: "fit-content",
              marginLeft: "auto",
            }}
          >
            <MainSelect
              label=""
              name="id"
              width="250px"
              options={wallets}
              value={wallet}
              onChange={(e) => {
                setWallet(e);
              }}
              elementValue={(e) => {
                return (
                  <Flex gap={"3"} align={"center"}>
                    <CurrencyBadge
                      currency={e?.currency?.code}
                      name={`${e?.currency?.code} (${FormatCurrency(
                        e?.balance,
                        e?.currency?.code
                      )})`}
                      fontSize="14px"
                    />
                  </Flex>
                );
              }}
              defaultValue={undefined}
              disabled={undefined}
              formik={undefined}
              hint={undefined}
              placeholder={undefined}
              cutBorder={undefined}
              isSearchable={undefined}
            />
          </div>
        }
      />
    </div>
  );
};

export default WalletTab;
