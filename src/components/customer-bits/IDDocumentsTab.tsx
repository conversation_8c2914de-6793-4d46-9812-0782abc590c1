import React, { FC, useState } from "react";
import CustomTable from "../bits/CustomTable";
import { StatusBadge } from "../bits/StatusBadge";
import { MenuItem, MenuList } from "../bits/DropDownMenu";
import TransactionDocument from "@/Modals/TransactionDocument";
import { saveAs } from "file-saver";
import IDComments from "@/Modals/IDComments";
import AddCommentToID from "@/Modals/AddCommentToID";
import ConfirmModal from "@/Modals/ConfirmModal";
import { useMutation } from "@tanstack/react-query";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useNavigate } from "react-router-dom";
import { handleGetItm } from "@/lib/utils";
import AppButton from "../bits/AppButton";
import { Download, Plus } from "lucide-react";
import { Flex } from "@radix-ui/themes";

const IDDocumentsTab: FC<any> = ({ data, refetch }) => {
  const [item, setItem] = useState({
    verificationStatus: "",
    id: "",
  });
  const [image, setImage] = useState();
  const [comments, setComments] = useState();
  const [addCommentModal, setAddCommentModal] = useState(false);
  const navigate = useNavigate();

  const userMenu = [
    {
      name: "View ID Front Document",
      status: ["active", "inactive"],
      action: (e) => {
        if (e?.documentFrontPageURL?.includes("pdf")) {
          window.open(e?.documentFrontPageURL, "_blank");
        } else {
          setImage(e?.documentFrontPageURL);
        }
      },
      index: 1,
    },
    {
      name: "Download Front ID",
      status: ["active", "inactive"],
      action: (e) => {
        if (e?.documentFrontPageURL?.includes("pdf")) {
          fetch(e?.documentFrontPageURL)
            .then((response) => response.blob())
            .then((blob) => {
              saveAs(blob, `${data?.firstName}-${data?.surName}.pdf`);
            });
        } else {
          saveAs(
            e?.documentFrontPageURL,
            `${data?.firstName}-${data?.surName}.png`
          );
        }
      },
      index: 2,
    },
    {
      name: "Edit Document",
      action: (e) => {
        navigate(`/edit-document/${e?.id}`, {
          state: {
            ...handleGetItm(e?.id, "id", data?.userKYCDocuments),
            userId: data?.userId,
          },
        });
      },
      index: 5,
    },
    {
      name: "View Comments",
      status: ["active", "inactive"],
      action: (e) => {
        setComments(e?.documentComments);
      },
      index: 3,
    },
    {
      name: "Add Comments",
      status: ["active", "inactive"],
      action: (e) => {
        setAddCommentModal(true);
      },
      index: 4,
    },
    {
      name: "Verify Document",
      action: (e) => {
        setOpen(true);
      },
      index: 5,
    },

    {
      name: "Cancel Document",
      action: () => {
        setOpen(true);
      },
      index: 6,
      color: "red",
    },
  ];
  const columns = [
    {
      title: "Action",
      dataIndex: "id",
      fixed: "left",
      width: 100,
      render: (e, record) => {
        return (
          <div
            style={{
              position: "relative",
            }}
          >
            {record?.documentComments?.length ? (
              <div
                style={{
                  width: "6px",
                  height: "6px",
                  borderRadius: "50%",
                  background: "red",
                  position: "absolute",
                  top: "-15px",
                  right: "20px",
                }}
              ></div>
            ) : (
              ""
            )}
            <MenuList
              onClick={() => {
                setItem({ ...record, userId: data?.userId });
              }}
              ActionIcon={undefined}
              ActionElement={undefined}
              iconWidth={undefined}
            >
              {record?.verificationStatus === "Not Verified"
                ? userMenu
                    ?.filter((itm) => itm.name !== "Cancel Document")
                    ?.map(({ name, action, index, color }) => (
                      <MenuItem
                        key={index}
                        name={name}
                        index={index}
                        action={() => {
                          action(record);
                        }}
                        Icon={undefined}
                        to={undefined}
                        width={"200px"}
                        padding={2}
                        color={color}
                      />
                    ))
                : record?.verificationStatus === "Pending"
                ? userMenu
                    ?.filter((itm) => itm.name !== "Cancel Document")
                    ?.map(({ name, action, index, color }) => (
                      <MenuItem
                        key={index}
                        name={name}
                        index={index}
                        action={() => {
                          action(record);
                        }}
                        Icon={undefined}
                        to={undefined}
                        width={"200px"}
                        padding={2}
                        color={color}
                      />
                    ))
                : userMenu
                    ?.filter((itm) => itm.name !== "Verify Document")
                    ?.map(({ name, action, index, color }) => (
                      <MenuItem
                        key={index}
                        name={name}
                        index={index}
                        action={() => {
                          action(record);
                        }}
                        Icon={undefined}
                        to={undefined}
                        width={"200px"}
                        padding={2}
                        color={color}
                      />
                    ))}
            </MenuList>
          </div>
        );
      },
    },
    {
      title: "ID",
      dataIndex: "id",
      fixed: "left",
      width: 140,
    },

    {
      title: "ID Name",
      dataIndex: "nameOnTheDocument",
      width: 220,
    },
    {
      title: "Verified Status",
      dataIndex: "verificationStatus",
      width: 140,
      render: (e) => <StatusBadge status={e} />,
    },
    {
      title: "ID Type",
      dataIndex: "documentType[name]",
      width: 220,
    },
    {
      title: "ID Number",
      dataIndex: "documentNumber",
      width: 200,
    },
    {
      title: "Place of Issue",
      dataIndex: "placeIssued",

      width: 120,
    },
    {
      title: "Expiry Date",
      dataIndex: "expiryDate",
      width: 120,
    },
    {
      title: "Date Uploaded",
      dataIndex: "dateUploaded",
      width: 200,
    },
    {
      title: "Uploaded By",
      dataIndex: "uploadedBy",
      width: 120,
    },
    {
      title: "Verified By",
      dataIndex: "verifiedBy",
      width: 120,
    },
    {
      title: "Verified Date",
      dataIndex: "dateIssued",
      width: 120,
    },
  ];

  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.MarkIdAsVerifiedMutation,
    onSuccess: () => {
      setOpen(false);
      refetch();
    },
    onError: () => {
      return;
    },
  });

  const [open, setOpen] = useState(false);
  const downloadFile = () => {
    fetch(data?.idVerificationReportURL)
      .then((response) => response.blob())
      .then((blob) => {
        const url = window.URL.createObjectURL(blob);
        const a = window.document.createElement("a");
        a.href = url;
        a.download = "GBG-PDF.pdf";
        window.document.body.appendChild(a);
        a.click();
        a.remove();
      })
      .catch((error) => console.error("Error downloading the file", error));
  };
  return (
    <div>
      <TransactionDocument open={image} setOpen={setImage} data={image} />
      {comments ? (
        <IDComments open={comments} setOpen={setComments} data={comments} />
      ) : (
        ""
      )}
      {addCommentModal && (
        <AddCommentToID
          open={addCommentModal}
          setOpen={setAddCommentModal}
          item={item}
          finished={refetch}
        />
      )}
      <ConfirmModal
        trigger={setOpen}
        open={open}
        description={"Are you sure you want to complete this request"}
        confirm={() => {
          mutate({
            userId: data?.userId,
            documentVerificationStatusToggle:
              (item && item?.verificationStatus === "Not Verified") ||
              item?.verificationStatus === "Pending"
                ? 1
                : 0,
            userKYCDocument: {
              id: item && item?.id,
            },
          });
        }}
        cancel={() => {
          setOpen(false);
        }}
        loading={isPending}
      />

      <CustomTable
        tableColumns={columns}
        arrayData={data?.userKYCDocuments}
        scroll={{
          x: 1600,
        }}
        Apidata={undefined}
        loading={undefined}
        noData={undefined}
        topContent={
          <Flex gap={"4"}>
            <AppButton
              IconLeft={Download}
              placeholder="Download ID Verification Report"
              width="280px"
              loading={false}
              disabled={false}
              to={undefined}
              onClick={() => {
                downloadFile();
              }}
              outline
              secondary={undefined}
              borderColor={undefined}
              style={undefined}
              style2={{
                marginLeft: "auto",
              }}
              IconRight={undefined}
              roundedFull={undefined}
              radius={undefined}
            />
            <AppButton
              IconLeft={Plus}
              placeholder="Add New Document"
              width="180px"
              loading={false}
              disabled={false}
              to={undefined}
              onClick={() => {
                navigate(`/add-document`, {
                  state: {
                    userId: data?.userId,
                  },
                });
              }}
              outline={undefined}
              secondary={undefined}
              borderColor={undefined}
              style={undefined}
              style2={undefined}
              IconRight={undefined}
              roundedFull={undefined}
              radius={undefined}
            />
          </Flex>
        }
        setPage={undefined}
        total={undefined}
        currentPage={undefined}
        tableWidth={undefined}
        rowClassName={undefined}
      />
    </div>
  );
};

export default IDDocumentsTab;
