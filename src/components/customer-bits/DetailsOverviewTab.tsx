import { Flex, Grid } from "@radix-ui/themes";
import React, { FC, useEffect, useState } from "react";
import Box from "../bits/Box";
import GaugeChartComponent from "../GaugeChartComponent";
import Divider from "../Divider";
import AdminBox from "../AdminBox";
import CurrencySelect from "../bits/CurrencySelect";
import { s } from "vite/dist/node/types.d-aGj9QkWt";
import { FormatCurrency } from "@/lib/utils";
import moment from "moment";
import CustomTable from "../bits/CustomTable";
import { StatusBadge } from "../bits/StatusBadge";
import AppLink from "../bits/AppLink";

const DetailsOverviewTab: FC<any> = ({ data }) => {
  const currencyKeys = data && Object.keys(data?.transactionVolume);

  const currencyOptionsDashboard = currencyKeys?.map((itm) => {
    return {
      label: itm,
      value: itm,
      ...data?.transactionVolume[itm],
    };
  });
  const [currency, setCurrency] = useState(currencyOptionsDashboard?.[0]);

  useEffect(() => {
    if (currencyOptionsDashboard?.length > 0) {
      setCurrency(currencyOptionsDashboard?.[0]);
    } else {
      setCurrency(undefined);
    }
  }, []);
  console.log(currency, "currency");
  const day = new Date();
  const today = new Date().setDate(day.getDate());
  const last30Day = new Date().setDate(day.getDate() - 30);
  const last60Day = new Date().setDate(day.getDate() - 60);
  const last90Day = new Date().setDate(day.getDate() - 90);
  const last120Day = new Date().setDate(day.getDate() - 120);

  const [showRiskTable, setShowRiskTable] = useState(false);

  const riskTable = data?.userRiskAssessment;

  const columns = [
    {
      title: "CUSTOMER ONBOARDING RISK",
      dataIndex: "riskFactor[name]",
      width: 200,
    },
    {
      title: "DESCRIPTION",
      dataIndex: "riskFactor[description]",
      width: 200,
    },
    {
      title: "STATUS",
      dataIndex: "riskLevel[name]",
      render: (e) => <StatusBadge status={e} />,
      width: 200,
    },
  ];

  return showRiskTable ? (
    <div>
      <div
        onClick={() => {
          setShowRiskTable(false);
        }}
        style={{
          marginBottom: "10px",
          width: "fit-content",
        }}
      >
        <AppLink type="nooo" to={undefined}>
          Go Back
        </AppLink>
      </div>
      <CustomTable
        topContent={undefined}
        tableColumns={columns}
        arrayData={riskTable}
        loading={undefined}
        scroll={undefined}
        Apidata={undefined}
        noData={undefined}
        setPage={undefined}
        total={undefined}
        currentPage={undefined}
        tableWidth={undefined}
        rowClassName={undefined}
      />
    </div>
  ) : (
    <div>
      {currencyOptionsDashboard?.length ? (
        <div
          style={{
            width: "fit-content",
            marginLeft: "auto",
          }}
        >
          <CurrencySelect
            setCurrency={setCurrency}
            currency={currency}
            options={currencyOptionsDashboard}
          />
        </div>
      ) : (
        ""
      )}
      <Grid columns={{ md: "1fr 2fr", initial: "1" }} gap={"4"}>
        <Box
          background={undefined}
          style={{
            height: "100%",
          }}
        >
          <Flex justify={"between"} align={"center"}>
            <h3>Customer Risk Chart</h3>
            <div
              onClick={() => {
                setShowRiskTable(true);
              }}
            >
              <AppLink type="nooo" to={undefined}>
                View Risk Analysis
              </AppLink>
            </div>
          </Flex>
          <br />
          <br />
          <br />
          <GaugeChartComponent data={data} />
          <br />
          <br />
          <br />
        </Box>
        <div>
          <Card
            data={{
              value: currency?.lastThirtyDays,
              count: currency?.lastThirtyDaysCount,
              fee: currency?.lastThirtyDaysTransitionFee,
              currency: currency?.label,
              from: moment(today).format("DD-MM-YYYY"),
              to: moment(last30Day).format("DD-MM-YYYY"),
              monthCount: 1,
            }}
          />
          <br />
          <Card
            data={{
              value: currency?.lastSixtyDays,
              count: currency?.lastSixtyDaysCount,
              fee: currency?.lastSixtyDaysTransitionFee,
              currency: currency?.label,
              from: moment(today).format("DD-MM-YYYY"),
              to: moment(last60Day).format("DD-MM-YYYY"),
              monthCount: 2,
            }}
          />
          <br />
          <Card
            data={{
              value: currency?.lastNinetyDays,
              count: currency?.lastNinetyDaysCount,
              fee: currency?.lastNinetyDaysTransitionFee,
              currency: currency?.label,
              from: moment(today).format("DD-MM-YYYY"),
              to: moment(last90Day).format("DD-MM-YYYY"),
              monthCount: 3,
            }}
          />
          <br />
          <Card
            data={{
              value: currency?.lastOneTwentyDays,
              count: currency?.lastOneTwentyDaysCount,
              fee: currency?.lastOneTwentyDaysTransitionFee,
              currency: currency?.label,
              from: moment(today).format("DD-MM-YYYY"),
              to: moment(last120Day).format("DD-MM-YYYY"),
              monthCount: 4,
            }}
          />
        </div>
      </Grid>
      <br />
      <AdminBox
        title="Lifetime Summary"
        sideChild={undefined}
        style={undefined}
      >
        <Box
          background={"#F9FAFB"}
          style={{
            display: "grid",
            gridTemplateColumns: "1fr 1fr 1fr",
            gap: "3rem",
          }}
        >
          <Flex gap={"4"} justify={"between"} align={"center"}>
            <div
              style={{
                textAlign: "center",
              }}
            >
              <h3>
                {FormatCurrency(currency?.allTimeTransferAmount)}{" "}
                {currency?.label}
              </h3>
              <div
                style={{
                  marginTop: "1rem",
                }}
              >
                (Fees:{" "}
                {FormatCurrency(
                  currency?.lastOneTwentyDaysTransitionFee +
                    currency?.lastNinetyDaysTransitionFee +
                    currency?.lastSixtyDaysTransitionFee +
                    currency?.lastThirtyDaysTransitionFee
                )}
                {currency?.label})
              </div>
            </div>
            <Divider />
          </Flex>
          <Flex gap={"4"} justify={"between"} align={"center"}>
            <div
              style={{
                textAlign: "center",
              }}
            >
              <h3>
                {FormatCurrency(
                  currency?.allTimeTransferCount
                  /* currency?.lastOneTwentyDaysCount +
                    currency?.lastNinetyDaysCount +
                    currency?.lastSixtyDaysCount +
                    currency?.lastThirtyDaysCount */
                )}
              </h3>
              <div
                style={{
                  marginTop: "1rem",
                }}
              >
                Total Transfer count{" "}
              </div>
            </div>
            <Divider />
          </Flex>
          <Flex gap={"4"} justify={"between"} align={"center"}>
            <div
              style={{
                textAlign: "center",
              }}
            >
              <h3>{FormatCurrency(currency?.allTimeTransferAmount)} </h3>
              <div
                style={{
                  marginTop: "1rem",
                }}
              >
                Avg. Transaction Value
              </div>
            </div>
          </Flex>
        </Box>
      </AdminBox>
    </div>
  );
};

const Card = ({ data }) => {
  return (
    <Box
      background={"#F9FAFB"}
      style={{
        display: "grid",
        gridTemplateColumns: "1.4fr 1fr 1fr 1fr",
        gap: "3rem",
      }}
    >
      <Flex gap={"4"} justify={"between"} align={"center"}>
        <div>
          <h3>Roll-over {data?.monthCount} month</h3>
          <div
            style={{
              marginBlock: "1rem",
            }}
          >
            From: {data?.from}
          </div>
          <div>To: {data?.to}</div>
        </div>
        <Divider />
      </Flex>
      <Flex gap={"4"} justify={"between"} align={"center"}>
        <div
          style={{
            textAlign: "center",
          }}
        >
          <h3>
            {FormatCurrency(data?.value)} {data?.currency}
          </h3>
          <div
            style={{
              marginTop: "1rem",
            }}
          >
            (Fees: {FormatCurrency(data?.fee)}
            {data?.currency})
          </div>
        </div>
        <Divider />
      </Flex>
      <Flex gap={"4"} justify={"between"} align={"center"}>
        <div
          style={{
            textAlign: "center",
          }}
        >
          <h3>{FormatCurrency(data?.count)}</h3>
          <div
            style={{
              marginTop: "1rem",
            }}
          >
            Transfer Count
          </div>
        </div>
        <Divider />
      </Flex>
      <Flex gap={"4"} justify={"between"} align={"center"}>
        <div
          style={{
            textAlign: "center",
          }}
        >
          <h3>{FormatCurrency(data?.value)}</h3>
          <div
            style={{
              marginTop: "1rem",
            }}
          >
            Avg. Transaction Value
          </div>
        </div>
      </Flex>
    </Box>
  );
};

export default DetailsOverviewTab;
