import React, { FC, useState } from "react";
import TransactionTableList from "../dashboard-tables/TransactionTableList";
import { useQuery } from "@tanstack/react-query";
import { ApiServiceAdmin } from "@/service/admin-services";
import moment from "moment";
import CustomTable from "../bits/CustomTable";
import { applyFilters } from "@/lib/utils";
import { Flex } from "@radix-ui/themes";
import FormInput from "../bits/FormInput";
import { Search } from "lucide-react";

const BeneficiaryTab: FC<any> = ({ data }) => {
  const { data: beneficiary, isFetching } = useQuery({
    queryKey: ["GetBeneficiaryUserQuery"],
    queryFn: () =>
      ApiServiceAdmin.GetBeneficiaryQuery({
        userId: data?.userId,
        beneficiaryId: 0,
      }),
  });

  const columns = [
    {
      title: "Customer ID",
      dataIndex: "id",
      width: 200,
    },
    {
      title: "Beneficiary Name",
      dataIndex: "beneficiaryName",
      width: 200,
    },
    {
      title: "Beneficiary Bank",
      dataIndex: "beneficiaryBank[bankName]",
      width: 200,
    },
    {
      title: "Beneficiary Account No",
      dataIndex: "beneficiaryBank[accountNumber]",
      width: 200,
    },
    {
      title: "Mobile Number",
      dataIndex: "beneficiaryPhoneNumber",
      width: 200,
    },
    {
      title: "date",
      dataIndex: "dateCreated",
      render: (e) => moment(e).format("DD/MM/YY, hh:mm:ss"),
      width: 200,
    },
  ];

  const [search, setSearch] = useState("");

  const newData = applyFilters(beneficiary?.data, search, [
    "id",
    "beneficiaryName",
  ]);
  return (
    <div>
      <CustomTable
        topContent={
          <Flex justify={"between"} align={"end"}>
            <FormInput
              placeholder={"Search by Name or ID"}
              background="#fff"
              IconLeft={Search}
              width="400px"
              onChange={(e) => {
                setSearch(e?.target?.value?.trim());
              }}
              name={undefined}
              type={undefined}
              defaultValue={undefined}
              label={undefined}
              disabled={undefined}
              formik={undefined}
              max={undefined}
              IconRight={undefined}
              hint={undefined}
            />
          </Flex>
        }
        tableColumns={columns}
        arrayData={newData?.length ? newData : data?.data}
        loading={isFetching}
        scroll={{
          x: 1600,
        }}
        Apidata={undefined}
        noData={undefined}
        setPage={undefined}
        total={undefined}
        currentPage={undefined}
        tableWidth={undefined}
      />
    </div>
  );
};

export default BeneficiaryTab;
