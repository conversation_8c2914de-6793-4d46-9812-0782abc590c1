import { Flex, Grid } from "@radix-ui/themes";
import React, { FC } from "react";
import CurrencyFlagImage from "react-currency-flags";

const ClientDetailsHeader: FC<any> = ({ data }) => {
  return (
    <Grid columns={{ md: "2", initial: "1" }}>
      <Grid
        columns={{ md: "1fr 4fr", initial: "1" }}
        gap={"4"}
        style={{
          borderRight: "1px solid #b1b1b1",
          paddingRight: "1rem",
        }}
      >
        <img
          style={{
            width: "130px",
            height: "130px",
            borderRadius: "999px",
            objectFit: "contain",
            border: "1px solid #e6e6e6",
          }}
          src={data?.profileImageURL}
        />
        <div>
          <Flex gap={"2"} align={"center"}>
            <h2>
              {data?.firstName} {data?.surName}
            </h2>
            {data?.country?.currencyCode && (
              <CurrencyFlagImage
                currency={data?.country?.currencyCode}
                style={{
                  height: "20px",
                  width: "20px",
                  display: "grid",
                  placeItems: "center",
                  border: "1px solid #cecece",
                  borderRadius: "999px",
                  overflow: "hidden",
                }}
                size="sm"
              />
            )}
          </Flex>
          <div
            style={{
              marginBlock: "1rem",
            }}
          >
            <span style={{ color: "#909090" }}>Reference</span>
            <p>{data?.reference}</p>
          </div>
          <div
            style={{
              marginBlock: "1rem",
            }}
          >
            <span style={{ color: "#909090" }}>Registration Date</span>
            <p>{data?.dateCreated}</p>
          </div>
          <div
            style={{
              marginBlock: "1rem",
            }}
          >
            <span style={{ color: "#909090" }}>DOB:</span>
            <p>{data?.dob}</p>
          </div>
        </div>
      </Grid>
      <div
        style={{
          paddingInline: "20px",
        }}
      >
        <div>
          <span style={{ color: "#909090" }}>Address</span>
          <p>{data?.address}</p>
        </div>
        <div
          style={{
            marginBlock: "1rem",
          }}
        >
          <span style={{ color: "#909090" }}>Mobile Number</span>
          <p>{data?.phone}</p>
        </div>
        <div
          style={{
            marginBlock: "1rem",
          }}
        >
          <span style={{ color: "#909090" }}>Email Address</span>
          <p>{data?.email}</p>
        </div>
      </div>
    </Grid>
  );
};

export default ClientDetailsHeader;
