import { Flex } from "@radix-ui/themes";
import React, { FC } from "react";
import DividerHorizontal from "../DividerHorizontal";

const AuditTab: FC<any> = ({ data }) => {
  return (
    <div>
      {data?.userActivities?.map((item, index) => (
        <>
          <Flex gap={"4"} py={"3"}>
            <svg
              width="40"
              height="40"
              viewBox="0 0 40 40"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M20.0007 3.33398C10.7957 3.33398 3.33398 10.7957 3.33398 20.0007C3.33398 29.2057 10.7957 36.6673 20.0007 36.6673C29.2057 36.6673 36.6673 29.2057 36.6673 20.0007C36.6673 10.7957 29.2057 3.33398 20.0007 3.33398ZM14.1673 15.834C14.1673 15.0679 14.3182 14.3094 14.6114 13.6017C14.9045 12.8939 15.3342 12.2509 15.8759 11.7092C16.4175 11.1675 17.0606 10.7378 17.7683 10.4447C18.4761 10.1515 19.2346 10.0007 20.0007 10.0007C20.7667 10.0007 21.5252 10.1515 22.233 10.4447C22.9407 10.7378 23.5838 11.1675 24.1254 11.7092C24.6671 12.2509 25.0968 12.8939 25.3899 13.6017C25.6831 14.3094 25.834 15.0679 25.834 15.834C25.834 17.3811 25.2194 18.8648 24.1254 19.9588C23.0315 21.0527 21.5477 21.6673 20.0007 21.6673C18.4536 21.6673 16.9698 21.0527 15.8759 19.9588C14.7819 18.8648 14.1673 17.3811 14.1673 15.834ZM30.4306 28.3073C29.183 29.8767 27.597 31.1439 25.7909 32.0143C23.9849 32.8847 22.0055 33.3359 20.0007 33.334C17.9958 33.3359 16.0164 32.8847 14.2104 32.0143C12.4043 31.1439 10.8183 29.8767 9.57065 28.3073C12.2723 26.369 15.959 25.0007 20.0007 25.0007C24.0423 25.0007 27.729 26.369 30.4306 28.3073Z"
                fill="#B7B7B7"
              />
            </svg>

            <div>
              <div
                style={{
                  color: "#000000",
                  fontSize: "14px",
                }}
              >
                {item?.activity}
              </div>
              <div
                style={{
                  color: "#36394A",
                  fontSize: "14px",
                }}
              >
                {item?.activityTime}
              </div>
            </div>
          </Flex>
          {index === data?.userActivities?.length - 1 ? (
            ""
          ) : (
            <DividerHorizontal style={{ margin: 0 }} />
          )}
        </>
      ))}
      {data?.userActivities?.length === 0 && <p>No audit logs available.</p>}
    </div>
  );
};

export default AuditTab;
