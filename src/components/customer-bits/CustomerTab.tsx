import React, { FC, useState } from "react";
import TransactionTableList from "../dashboard-tables/TransactionTableList";
import { useQuery } from "@tanstack/react-query";
import { ApiServiceAdmin } from "@/service/admin-services";
import moment from "moment";
import CustomTable from "../bits/CustomTable";
import { applyFilters } from "@/lib/utils";
import { Flex } from "@radix-ui/themes";
import FormInput from "../bits/FormInput";
import { Search } from "lucide-react";
import CustomerTableList from "../dashboard-tables/CustomerTableList";

const CustomerTab: FC<any> = ({ data }) => {
  const {
    data: list,
    isLoading,
    isFetching,
    refetch,
  } = useQuery({
    queryKey: ["GetAgentCustomersQuery"],
    queryFn: () => ApiServiceAdmin.GetAgentCustomersQuery(data?.userId),
  });

  return (
    <div>
      <CustomerTableList
        topContent={undefined}
        data={list}
        isLoading={isLoading || isFetching}
        refetch={refetch}
      />
    </div>
  );
};

export default CustomerTab;
