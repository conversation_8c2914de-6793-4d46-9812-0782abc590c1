import { Flex, Grid } from "@radix-ui/themes";
import React, { FC } from "react";
import CurrencyFlagImage from "react-currency-flags";
import { CurrencyBadge } from "../bits/CurrencyBadge";

const SendMoneyHeader: FC<any> = ({ data, receiver }) => {
  return (
    <Grid columns={{ md: "2", initial: "1" }}>
      <Grid
        columns={{ md: "1fr 4fr", initial: "1" }}
        gap={"4"}
        style={{
          paddingRight: "1rem",
        }}
      >
        <img
          style={{
            width: "130px",
            height: "130px",
            borderRadius: "999px",
            objectFit: "contain",
            border: "1px solid #e6e6e6",
          }}
          src={data?.profileImageURL}
        />
        <div>
          <h2>Sender Details:</h2>

          <div
            style={{
              marginBlock: "1rem",
            }}
          >
            <span style={{ color: "#909090" }}>Reference</span>
            <p>{data?.reference}</p>
          </div>
          <div
            style={{
              marginBlock: "1rem",
            }}
          >
            <span style={{ color: "#909090" }}>Name</span>
            <p>
              {data?.firstName} {data?.surName}
            </p>
          </div>
          <div
            style={{
              marginBlock: "1rem",
            }}
          >
            <span style={{ color: "#909090" }}>Mobile No</span>
            <p>{data?.phone}</p>
          </div>

          <div
            style={{
              marginBlock: "1rem",
            }}
          >
            <span style={{ color: "#909090" }}>Address</span>
            <p>{data?.address}</p>
          </div>
        </div>
      </Grid>
      <Grid
        columns={{ md: "1fr 4fr", initial: "1" }}
        gap={"4"}
        style={{
          paddingRight: "1rem",
        }}
      >
        <svg
          width="71"
          height="71"
          viewBox="0 0 71 71"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          style={{
            width: "130px",
            height: "130px",
          }}
        >
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M35.5002 0.0839844C15.9395 0.0839844 0.0834961 15.94 0.0834961 35.5006C0.0834961 55.0613 15.9395 70.9173 35.5002 70.9173C55.0608 70.9173 70.9168 55.0613 70.9168 35.5006C70.9168 15.94 55.0608 0.0839844 35.5002 0.0839844ZM23.1043 26.6465C23.1043 25.0186 23.425 23.4067 24.0479 21.9028C24.6709 20.3989 25.5839 19.0324 26.735 17.8813C27.886 16.7302 29.2526 15.8172 30.7565 15.1942C32.2604 14.5713 33.8723 14.2507 35.5002 14.2507C37.128 14.2507 38.7399 14.5713 40.2438 15.1942C41.7478 15.8172 43.1143 16.7302 44.2653 17.8813C45.4164 19.0324 46.3295 20.3989 46.9524 21.9028C47.5754 23.4067 47.896 25.0186 47.896 26.6465C47.896 29.9341 46.59 33.087 44.2653 35.4117C41.9407 37.7363 38.7877 39.0423 35.5002 39.0423C32.2126 39.0423 29.0597 37.7363 26.735 35.4117C24.4103 33.087 23.1043 29.9341 23.1043 26.6465ZM57.6639 53.1523C55.0127 56.4872 51.6424 59.1801 47.8045 61.0297C43.9666 62.8793 39.7605 63.838 35.5002 63.834C31.2398 63.838 27.0337 62.8793 23.1958 61.0297C19.3579 59.1801 15.9876 56.4872 13.3364 53.1523C19.0775 49.0334 26.9116 46.1256 35.5002 46.1256C44.0887 46.1256 51.9229 49.0334 57.6639 53.1523Z"
            fill="#D3D0D0"
          />
        </svg>

        <div>
          <h2>Receiver Details:</h2>

          <div
            style={{
              marginBlock: "1rem",
            }}
          >
            <span style={{ color: "#909090" }}>Name</span>
            <p>{receiver?.beneficiaryName}</p>
          </div>
          <div
            style={{
              marginBlock: "1rem",
            }}
          >
            <span style={{ color: "#909090" }}>Date Created</span>
            <p>{receiver?.dateCreated}</p>
          </div>
          <Flex
            style={{
              marginBlock: "1rem",
            }}
            gap={"4"}
          >
            <div>
              <span style={{ color: "#909090" }}>Mobile No</span>
              <p>{receiver?.phone}</p>
            </div>

            <div>
              <span style={{ color: "#909090" }}>Country</span>
              <CurrencyBadge
                currency={receiver?.beneficiaryCountry?.currencyCode}
                name={receiver?.beneficiaryCountry?.name}
              />
            </div>
          </Flex>

          <Flex
            style={{
              marginBlock: "1rem",
            }}
            gap={"4"}
          >
            <div>
              <span style={{ color: "#909090" }}>Bank Name</span>
              <p>{receiver?.beneficiaryBank?.bankName}</p>
            </div>

            <div>
              <span style={{ color: "#909090" }}>Account Number</span>
              <p>{receiver?.beneficiaryBank?.accountNumber}</p>
            </div>
          </Flex>
        </div>
      </Grid>
    </Grid>
  );
};

export default SendMoneyHeader;
