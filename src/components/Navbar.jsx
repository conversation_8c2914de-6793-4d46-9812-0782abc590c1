import styled from "styled-components";
import NavItem from "./bits/Navbar/NavItem";
import DashboardIcon from "@/assets/icons/nav-icons/DashboardIcon";
import TransactionIcon from "@/assets/icons/nav-icons/TransactionIcon";
import CustomerIcon from "@/assets/icons/nav-icons/CustomerIcon";
import AgentsIcon from "@/assets/icons/nav-icons/AgentsIcon";
import SendmoneyIcon from "@/assets/icons/nav-icons/SendmoneyIcon";
import ClockIcon from "@/assets/icons/nav-icons/ClockIcon";
import CustomerIcon2 from "@/assets/icons/nav-icons/CustomerIcon2";
import CustomerIcon3 from "@/assets/icons/nav-icons/CustomerIcon3";
import RateIcon from "@/assets/icons/nav-icons/RateIcon";
import MasterIcon from "@/assets/icons/nav-icons/MasterIcon";
import { useAuth } from "@/context/global.context";

export default function Navbar({ logo, className, onClick }) {
  const { menuAccessRoutes } = useAuth();

  const link2 = [
    {
      head: "dashboard",
      name: "Dashboard",
      icon: DashboardIcon,
      subMenus: [
        {
          name: "System Monitor",
          route: "/system-monitor",
        },
        {
          name: "Dashboard",
          route: "/dashboard",
        },
        {
          name: "Payout Partner",
          route: "/payout-partner",
        },
        {
          name: "Payout Dashboard",
          route: "/payout-dashboard",
        },
      ],
    },

    {
      head: "main",
      name: "Agent",
      icon: AgentsIcon,
      subMenus: [
        {
          name: "Pending Invitation",
          route: "/pending-invitations",
        },
        {
          name: "Agent",
          route: "/agents",
        },
      ],
    },
    {
      head: "customer",
      name: "Manage Customer",
      icon: CustomerIcon,
      subMenus: [
        {
          name: "Customers",
          route: "/customers",
          icon: CustomerIcon,
        },
        {
          name: "Action Required",
          route: "/action-required",
          icon: CustomerIcon2,
        },
        {
          name: "Incomplete Registration",
          route: "/incomplete-registration",
          icon: CustomerIcon3,
        },
      ],
    },
    {
      head: "main",
      name: "Send Money",
      route: "/send-money",
      icon: SendmoneyIcon,
    },
    {
      head: "main",
      name: "Beneficiary",
      route: "/beneficiary",
      icon: CustomerIcon,
    },
    {
      head: "main",
      name: "Transaction Processor",
      icon: ClockIcon,
      subMenus: [
        {
          name: "Payment Channel Processor",
          route: "/payment-channel-processor",
        },
        {
          name: "Payout Channel Processor",
          route: "/payout-channel-processor",
        },
      ],
    },

    {
      head: "rate",
      name: "Currency Rate Metadata",
      route: "/currency-rate-metadata",
      icon: RateIcon,
    },
    {
      head: "rate",
      name: "Update Rate & Fees",
      route: "/update-rate-&-fees",
      icon: RateIcon,
    },
    {
      head: "transfers",
      name: "Transfer",
      icon: TransactionIcon,
      subMenus: [
        {
          name: "View Transfers",
          route: "/view-transfers",
        },
        {
          name: "Incomplete Transfers",
          route: "/incomplete-transfers",
        },
        {
          name: "Incomplete Pay With Bank Transfers",
          route: "/incomplete-pay-with-bank-transfers",
        },
        {
          name: "Payment Check",
          route: "/payment-check",
        },
      ],
    },

    {
      head: "configuration",
      name: "Master",
      icon: MasterIcon,
      subMenus: [
        {
          name: "Banks",
          route: "/banks",
        },
        {
          name: "Collection Type",
          route: "/collection-type",
        },
        {
          name: "Company Bank",
          route: "/company-banks",
        },
        {
          name: "Country",
          route: "/country",
        },
        {
          name: "Employee",
          subMenus: [
            {
              name: "User Role",
              route: "/user-role",
            },
            {
              name: "User Access",
              route: "/user-access",
            },
            {
              name: "Employee Master",
              route: "/employee-master",
            },
          ],
        },
        {
          name: "KYC Provider",
          route: "/kyc-provider",
        },
        {
          name: "Payment Channels",
          route: "/payment-channels",
        },
        {
          name: "Payment Provider",
          route: "/payment-provider",
        },
        {
          name: "Payout Channels",
          route: "/payout-channels",
        },
        {
          name: "Payout Provider",
          route: "/payout-provider",
        },
        {
          name: "Profession Master",
          route: "/profession-master",
        },
        {
          name: "Wallet Provider",
          route: "/wallet-provider",
        },
      ],
    },
  ];

  console.log(menuAccessRoutes, "menuAccessRoutes");

  const userAccessedLinks = link2?.filter(
    (itm) =>
      menuAccessRoutes?.map((it) => it.menuName)?.includes(itm?.name) &&
      menuAccessRoutes?.find((it) => it?.menuName === itm?.name)?.menuAccessType
        ?.id >= 2
  );

  const menuAccessFilteredWithUserAccessLinks = menuAccessRoutes?.filter(
    (itm) => userAccessedLinks?.map((it) => it.name)?.includes(itm?.menuName)
  );

  const formattedLinks = link2
    ?.filter(
      (itm) =>
        menuAccessRoutes?.map((it) => it.menuName)?.includes(itm?.name) &&
        menuAccessRoutes?.find((it) => it?.menuName === itm?.name)
          ?.menuAccessType?.id >= 2
    )
    ?.map((itm, idx) => {
      return {
        ...itm,
        subMenus: itm?.subMenus?.filter(
          (itm) =>
            menuAccessFilteredWithUserAccessLinks?.[idx]?.userRoleSubMenuAccess
              ?.map((ac) => ac?.subMenuName)
              ?.includes(itm?.name) &&
            menuAccessFilteredWithUserAccessLinks?.[
              idx
            ]?.userRoleSubMenuAccess?.find(
              (ac) => ac?.subMenuName === itm?.name
            )?.menuAccessType?.id >= 2
        ),
      };
    });
  console.log(menuAccessFilteredWithUserAccessLinks, formattedLinks, "forma");

  return (
    <>
      <NavbarStyle className={className}>
        {logo && logo}
        <br />

        <NavGroupTitle>{"Dashboard"}</NavGroupTitle>
        {formattedLinks
          ?.filter((itm) => itm.head === "dashboard")
          ?.map((menu) => {
            return (
              <NavItem
                onClick={onClick}
                Icon={menu.icon}
                name={menu.name}
                route={menu.route}
                menu={menu.subMenus?.map((subMenu) => {
                  return (
                    <NavItem
                      onClick={onClick}
                      name={subMenu.name}
                      route={subMenu.route}
                      menu={subMenu.subMenus?.map((subMenu) => {
                        return (
                          <NavItem
                            onClick={onClick}
                            name={subMenu.name}
                            route={subMenu.route}
                          />
                        );
                      })}
                    />
                  );
                })}
              />
            );
          })}
        <br />

        <NavGroupTitle>{"Main"}</NavGroupTitle>
        {formattedLinks
          ?.filter((itm) => itm.head === "main")
          ?.map((menu) => {
            return (
              <NavItem
                onClick={onClick}
                Icon={menu.icon}
                name={menu.name}
                route={menu.route}
                menu={menu.subMenus?.map((subMenu) => {
                  return (
                    <NavItem
                      onClick={onClick}
                      name={subMenu.name}
                      route={subMenu.route}
                      menu={subMenu.subMenus?.map((subMenu) => {
                        return (
                          <NavItem
                            onClick={onClick}
                            name={subMenu.name}
                            route={subMenu.route}
                          />
                        );
                      })}
                    />
                  );
                })}
              />
            );
          })}
        <br />

        <NavGroupTitle>{"Customers"}</NavGroupTitle>
        {formattedLinks
          ?.filter((itm) => itm.head === "customer")
          ?.map((menu) => {
            return (
              <NavItem
                onClick={onClick}
                Icon={menu.icon}
                name={menu.name}
                route={menu.route}
                menu={menu.subMenus?.map((subMenu) => {
                  return (
                    <NavItem
                      onClick={onClick}
                      name={subMenu.name}
                      route={subMenu.route}
                      menu={subMenu.subMenus?.map((subMenu) => {
                        return (
                          <NavItem
                            onClick={onClick}
                            name={subMenu.name}
                            route={subMenu.route}
                          />
                        );
                      })}
                    />
                  );
                })}
              />
            );
          })}
        <br />

        <NavGroupTitle>{"Rate"}</NavGroupTitle>
        {formattedLinks
          ?.filter((itm) => itm.head === "rate")
          ?.map((menu) => {
            return (
              <NavItem
                onClick={onClick}
                Icon={menu.icon}
                name={menu.name}
                route={menu.route}
                menu={menu.subMenus?.map((subMenu) => {
                  return (
                    <NavItem
                      onClick={onClick}
                      name={subMenu.name}
                      route={subMenu.route}
                      menu={subMenu.subMenus?.map((subMenu) => {
                        return (
                          <NavItem
                            onClick={onClick}
                            name={subMenu.name}
                            route={subMenu.route}
                          />
                        );
                      })}
                    />
                  );
                })}
              />
            );
          })}
        <br />

        <NavGroupTitle>{"Transfer"}</NavGroupTitle>
        {formattedLinks
          ?.filter((itm) => itm.head === "transfers")
          ?.map((menu) => {
            return (
              <NavItem
                onClick={onClick}
                Icon={menu.icon}
                name={menu.name}
                route={menu.route}
                menu={menu.subMenus?.map((subMenu) => {
                  return (
                    <NavItem
                      onClick={onClick}
                      name={subMenu.name}
                      route={subMenu.route}
                      menu={subMenu.subMenus?.map((subMenu) => {
                        return (
                          <NavItem
                            onClick={onClick}
                            name={subMenu.name}
                            route={subMenu.route}
                          />
                        );
                      })}
                    />
                  );
                })}
              />
            );
          })}
        <br />

        <NavGroupTitle>{"Configuration"}</NavGroupTitle>
        {formattedLinks
          ?.filter((itm) => itm.head === "configuration")
          ?.map((menu) => {
            return (
              <NavItem
                onClick={onClick}
                Icon={menu.icon}
                name={menu.name}
                route={menu.route}
                menu={menu.subMenus?.map((subMenu) => {
                  return (
                    <NavItem
                      onClick={onClick}
                      name={subMenu.name}
                      route={subMenu.route}
                      menu={subMenu.subMenus?.map((subMenu) => {
                        return (
                          <NavItem
                            onClick={onClick}
                            name={subMenu.name}
                            route={subMenu.route}
                          />
                        );
                      })}
                    />
                  );
                })}
              />
            );
          })}

        <br />
        <br />
        <br />
        {/*     <NavItem
          Icon={CustomerSupportIcon}
          onClick={onClick}
          name="Customer support"
          route="/customer-support?tab=all"
        /> */}
      </NavbarStyle>
    </>
  );
}

const NavbarStyle = styled.div`
  background-color: #04070b;
  height: 100vh;
  width: 100%;
  position: relative;
  padding: 22px;
  overflow: hidden;
  overflow-y: scroll;
`;

const NavGroupTitle = styled.div`
  color: #ffffff99;
  font-size: 12px;
  text-transform: uppercase;
  margin-left: 18px;
  margin-bottom: 8px;
`;
