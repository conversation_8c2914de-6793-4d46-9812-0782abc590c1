export default function NoiseAuthBg(props) {
  return (
    <svg
      width="840"
      height="1077"
      viewBox="0 0 840 1077"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g opacity="0.05">
        <g filter="url(#filter0_b_343_598)">
          <rect
            x="-39.661"
            y="591.046"
            width="132.76"
            height="151.095"
            rx="12.8989"
            transform="rotate(-90 -39.661 591.046)"
            stroke="url(#paint0_linear_343_598)"
            stroke-width="1.83965"
          />
        </g>
        <g filter="url(#filter1_b_343_598)">
          <rect
            x="110.215"
            y="333.108"
            width="151.095"
            height="132.76"
            rx="12.8989"
            stroke="url(#paint1_linear_343_598)"
            stroke-width="1.83965"
          />
        </g>
        <g filter="url(#filter2_b_343_598)">
          <rect
            x="261.309"
            y="718.917"
            width="151.095"
            height="132.76"
            rx="12.8989"
            transform="rotate(-180 261.309 718.917)"
            stroke="url(#paint2_linear_343_598)"
            stroke-width="1.83965"
          />
        </g>
        <g filter="url(#filter3_b_343_598)">
          <rect
            x="260.092"
            y="458.286"
            width="151.095"
            height="132.76"
            rx="12.8989"
            stroke="url(#paint3_linear_343_598)"
            stroke-width="1.83965"
          />
        </g>
        <g filter="url(#filter4_b_343_598)">
          <rect
            x="566.686"
            y="84.4015"
            width="132.76"
            height="151.095"
            rx="12.8989"
            transform="rotate(-90 566.686 84.4015)"
            stroke="url(#paint4_linear_343_598)"
            stroke-width="1.83965"
          />
        </g>
        <g filter="url(#filter5_b_343_598)">
          <rect
            x="867.656"
            y="212.272"
            width="151.095"
            height="132.76"
            rx="12.8989"
            transform="rotate(-180 867.656 212.272)"
            stroke="url(#paint5_linear_343_598)"
            stroke-width="1.83965"
          />
        </g>
        <g filter="url(#filter6_b_343_598)">
          <rect
            x="263.017"
            y="844.3"
            width="132.76"
            height="151.095"
            rx="12.8989"
            transform="rotate(-90 263.017 844.3)"
            stroke="url(#paint6_linear_343_598)"
            stroke-width="1.83965"
          />
        </g>
        <g filter="url(#filter7_b_343_598)">
          <rect
            x="412.896"
            y="586.363"
            width="151.095"
            height="132.76"
            rx="12.8989"
            stroke="url(#paint7_linear_343_598)"
            stroke-width="1.83965"
          />
        </g>
        <g filter="url(#filter8_b_343_598)">
          <rect
            x="563.992"
            y="972.171"
            width="151.095"
            height="132.76"
            rx="12.8989"
            transform="rotate(-180 563.992 972.171)"
            stroke="url(#paint8_linear_343_598)"
            stroke-width="1.83965"
          />
        </g>
        <g filter="url(#filter9_b_343_598)">
          <rect
            x="562.773"
            y="711.541"
            width="151.095"
            height="132.76"
            rx="12.8989"
            stroke="url(#paint9_linear_343_598)"
            stroke-width="1.83965"
          />
        </g>
        <g filter="url(#filter10_b_343_598)">
          <rect
            x="-42.5902"
            y="205.047"
            width="151.095"
            height="132.76"
            rx="12.8989"
            stroke="white"
            stroke-width="1.83965"
          />
        </g>
        <g filter="url(#filter11_b_343_598)">
          <rect
            x="565.703"
            y="591.046"
            width="132.76"
            height="151.095"
            rx="12.8989"
            transform="rotate(-90 565.703 591.046)"
            stroke="url(#paint10_linear_343_598)"
            stroke-width="1.83965"
          />
        </g>
        <g filter="url(#filter12_b_343_598)">
          <rect
            x="715.577"
            y="333.108"
            width="151.095"
            height="132.76"
            rx="12.8989"
            stroke="url(#paint11_linear_343_598)"
            stroke-width="1.83965"
          />
        </g>
        <g filter="url(#filter13_b_343_598)">
          <rect
            x="866.672"
            y="718.916"
            width="151.095"
            height="132.76"
            rx="12.8989"
            transform="rotate(-180 866.672 718.916)"
            stroke="url(#paint12_linear_343_598)"
            stroke-width="1.83965"
          />
        </g>
        <g filter="url(#filter14_b_343_598)">
          <rect
            x="0.919823"
            y="-0.919823"
            width="151.095"
            height="132.76"
            rx="12.8989"
            transform="matrix(1 0 0 -1 -46.1064 852.859)"
            stroke="white"
            stroke-width="1.83965"
          />
        </g>
        <g filter="url(#filter15_b_343_598)">
          <rect
            x="0.919823"
            y="0.919823"
            width="132.76"
            height="151.095"
            rx="12.8989"
            transform="matrix(-4.96656e-08 1 1 3.8471e-08 561.198 965.33)"
            stroke="url(#paint13_linear_343_598)"
            stroke-width="1.83965"
          />
        </g>
        <g filter="url(#filter16_b_343_598)">
          <rect
            x="-0.919823"
            y="0.919823"
            width="151.095"
            height="132.76"
            rx="12.8989"
            transform="matrix(-1 0 0 1 862.17 837.46)"
            stroke="url(#paint14_linear_343_598)"
            stroke-width="1.83965"
          />
        </g>
        <g filter="url(#filter17_b_343_598)">
          <rect
            x="0.919823"
            y="0.919823"
            width="132.76"
            height="151.095"
            rx="12.8989"
            transform="matrix(-4.96656e-08 1 1 3.8471e-08 262.093 204.208)"
            stroke="url(#paint15_linear_343_598)"
            stroke-width="1.83965"
          />
        </g>
        <g filter="url(#filter18_b_343_598)">
          <rect
            x="0.919823"
            y="-0.919823"
            width="151.095"
            height="132.76"
            rx="12.8989"
            transform="matrix(1 0 0 -1 411.97 462.145)"
            stroke="url(#paint16_linear_343_598)"
            stroke-width="1.83965"
          />
        </g>
        <g filter="url(#filter19_b_343_598)">
          <rect
            x="-0.919823"
            y="0.919823"
            width="151.095"
            height="132.76"
            rx="12.8989"
            transform="matrix(-1 0 0 1 563.065 76.3369)"
            stroke="url(#paint17_linear_343_598)"
            stroke-width="1.83965"
          />
        </g>
        <g filter="url(#filter20_b_343_598)">
          <rect
            x="0.919823"
            y="-0.919823"
            width="151.095"
            height="132.76"
            rx="12.8989"
            transform="matrix(1 0 0 -1 561.845 336.967)"
            stroke="url(#paint18_linear_343_598)"
            stroke-width="1.83965"
          />
        </g>
      </g>
      <defs>
        <filter
          id="filter0_b_343_598"
          x="-109.674"
          y="388.273"
          width="291.122"
          height="272.787"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="34.5468" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_343_598"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_343_598"
            result="shape"
          />
        </filter>
        <filter
          id="filter1_b_343_598"
          x="40.2013"
          y="263.095"
          width="291.122"
          height="272.787"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="34.5468" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_343_598"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_343_598"
            result="shape"
          />
        </filter>
        <filter
          id="filter2_b_343_598"
          x="40.2008"
          y="516.143"
          width="291.122"
          height="272.787"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="34.5468" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_343_598"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_343_598"
            result="shape"
          />
        </filter>
        <filter
          id="filter3_b_343_598"
          x="190.079"
          y="388.273"
          width="291.122"
          height="272.787"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="34.5468" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_343_598"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_343_598"
            result="shape"
          />
        </filter>
        <filter
          id="filter4_b_343_598"
          x="496.673"
          y="-118.372"
          width="291.122"
          height="272.787"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="34.5468" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_343_598"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_343_598"
            result="shape"
          />
        </filter>
        <filter
          id="filter5_b_343_598"
          x="646.548"
          y="9.49844"
          width="291.122"
          height="272.787"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="34.5468" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_343_598"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_343_598"
            result="shape"
          />
        </filter>
        <filter
          id="filter6_b_343_598"
          x="193.003"
          y="641.527"
          width="291.122"
          height="272.787"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="34.5468" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_343_598"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_343_598"
            result="shape"
          />
        </filter>
        <filter
          id="filter7_b_343_598"
          x="342.882"
          y="516.349"
          width="291.122"
          height="272.787"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="34.5468" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_343_598"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_343_598"
            result="shape"
          />
        </filter>
        <filter
          id="filter8_b_343_598"
          x="342.884"
          y="769.398"
          width="291.122"
          height="272.787"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="34.5468" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_343_598"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_343_598"
            result="shape"
          />
        </filter>
        <filter
          id="filter9_b_343_598"
          x="492.759"
          y="641.527"
          width="291.122"
          height="272.787"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="34.5468" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_343_598"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_343_598"
            result="shape"
          />
        </filter>
        <filter
          id="filter10_b_343_598"
          x="-112.604"
          y="135.033"
          width="291.122"
          height="272.787"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="34.5468" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_343_598"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_343_598"
            result="shape"
          />
        </filter>
        <filter
          id="filter11_b_343_598"
          x="495.69"
          y="388.273"
          width="291.122"
          height="272.787"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="34.5468" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_343_598"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_343_598"
            result="shape"
          />
        </filter>
        <filter
          id="filter12_b_343_598"
          x="645.564"
          y="263.095"
          width="291.122"
          height="272.787"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="34.5468" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_343_598"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_343_598"
            result="shape"
          />
        </filter>
        <filter
          id="filter13_b_343_598"
          x="645.564"
          y="516.142"
          width="291.122"
          height="272.787"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="34.5468" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_343_598"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_343_598"
            result="shape"
          />
        </filter>
        <filter
          id="filter14_b_343_598"
          x="-115.2"
          y="651.005"
          width="291.122"
          height="272.787"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="34.5468" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_343_598"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_343_598"
            result="shape"
          />
        </filter>
        <filter
          id="filter15_b_343_598"
          x="492.104"
          y="896.236"
          width="291.122"
          height="272.787"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="34.5468" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_343_598"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_343_598"
            result="shape"
          />
        </filter>
        <filter
          id="filter16_b_343_598"
          x="641.981"
          y="768.366"
          width="291.122"
          height="272.787"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="34.5468" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_343_598"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_343_598"
            result="shape"
          />
        </filter>
        <filter
          id="filter17_b_343_598"
          x="192.999"
          y="135.114"
          width="291.122"
          height="272.787"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="34.5468" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_343_598"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_343_598"
            result="shape"
          />
        </filter>
        <filter
          id="filter18_b_343_598"
          x="342.876"
          y="260.292"
          width="291.122"
          height="272.787"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="34.5468" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_343_598"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_343_598"
            result="shape"
          />
        </filter>
        <filter
          id="filter19_b_343_598"
          x="342.877"
          y="7.24331"
          width="291.122"
          height="272.787"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="34.5468" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_343_598"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_343_598"
            result="shape"
          />
        </filter>
        <filter
          id="filter20_b_343_598"
          x="492.751"
          y="135.113"
          width="291.122"
          height="272.787"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="34.5468" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_343_598"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_343_598"
            result="shape"
          />
        </filter>
        <linearGradient
          id="paint0_linear_343_598"
          x1="26.7191"
          y1="591.966"
          x2="26.7191"
          y2="744.9"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="white" />
          <stop offset="1" stop-color="white" stop-opacity="0" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_343_598"
          x1="185.762"
          y1="332.188"
          x2="185.762"
          y2="466.788"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="white" />
          <stop offset="1" stop-color="white" stop-opacity="0" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_343_598"
          x1="338.696"
          y1="719.836"
          x2="338.696"
          y2="854.436"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="white" />
          <stop offset="1" stop-color="white" stop-opacity="0" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_343_598"
          x1="335.64"
          y1="457.366"
          x2="335.64"
          y2="591.966"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="white" />
          <stop offset="1" stop-color="white" stop-opacity="0" />
        </linearGradient>
        <linearGradient
          id="paint4_linear_343_598"
          x1="633.066"
          y1="85.3213"
          x2="633.066"
          y2="238.256"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="white" />
          <stop offset="1" stop-color="white" stop-opacity="0" />
        </linearGradient>
        <linearGradient
          id="paint5_linear_343_598"
          x1="945.043"
          y1="213.192"
          x2="945.043"
          y2="347.792"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="white" />
          <stop offset="1" stop-color="white" stop-opacity="0" />
        </linearGradient>
        <linearGradient
          id="paint6_linear_343_598"
          x1="329.397"
          y1="845.22"
          x2="329.397"
          y2="998.154"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="white" />
          <stop offset="1" stop-color="white" stop-opacity="0" />
        </linearGradient>
        <linearGradient
          id="paint7_linear_343_598"
          x1="488.443"
          y1="585.443"
          x2="488.443"
          y2="720.043"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="white" />
          <stop offset="1" stop-color="white" stop-opacity="0" />
        </linearGradient>
        <linearGradient
          id="paint8_linear_343_598"
          x1="641.379"
          y1="973.091"
          x2="641.379"
          y2="1107.69"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="white" />
          <stop offset="1" stop-color="white" stop-opacity="0" />
        </linearGradient>
        <linearGradient
          id="paint9_linear_343_598"
          x1="638.32"
          y1="710.621"
          x2="638.32"
          y2="845.221"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="white" />
          <stop offset="1" stop-color="white" stop-opacity="0" />
        </linearGradient>
        <linearGradient
          id="paint10_linear_343_598"
          x1="632.083"
          y1="591.966"
          x2="632.083"
          y2="744.9"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="white" />
          <stop offset="1" stop-color="white" stop-opacity="0" />
        </linearGradient>
        <linearGradient
          id="paint11_linear_343_598"
          x1="791.124"
          y1="332.188"
          x2="791.124"
          y2="466.788"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="white" />
          <stop offset="1" stop-color="white" stop-opacity="0" />
        </linearGradient>
        <linearGradient
          id="paint12_linear_343_598"
          x1="944.059"
          y1="719.835"
          x2="944.059"
          y2="854.435"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="white" />
          <stop offset="1" stop-color="white" stop-opacity="0" />
        </linearGradient>
        <linearGradient
          id="paint13_linear_343_598"
          x1="67.2999"
          y1="0"
          x2="67.2999"
          y2="152.934"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="white" />
          <stop offset="1" stop-color="white" stop-opacity="0" />
        </linearGradient>
        <linearGradient
          id="paint14_linear_343_598"
          x1="76.4672"
          y1="0"
          x2="76.4672"
          y2="134.6"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="white" />
          <stop offset="1" stop-color="white" stop-opacity="0" />
        </linearGradient>
        <linearGradient
          id="paint15_linear_343_598"
          x1="67.2999"
          y1="0"
          x2="67.2999"
          y2="152.934"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="white" />
          <stop offset="1" stop-color="white" stop-opacity="0" />
        </linearGradient>
        <linearGradient
          id="paint16_linear_343_598"
          x1="76.4672"
          y1="0"
          x2="76.4672"
          y2="134.6"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="white" />
          <stop offset="1" stop-color="white" stop-opacity="0" />
        </linearGradient>
        <linearGradient
          id="paint17_linear_343_598"
          x1="76.4672"
          y1="0"
          x2="76.4672"
          y2="134.6"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="white" />
          <stop offset="1" stop-color="white" stop-opacity="0" />
        </linearGradient>
        <linearGradient
          id="paint18_linear_343_598"
          x1="76.4672"
          y1="0"
          x2="76.4672"
          y2="134.6"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="white" />
          <stop offset="1" stop-color="white" stop-opacity="0" />
        </linearGradient>
      </defs>
    </svg>
  );
}
