import { useNavigate, useSearchParams } from "react-router-dom";
import styled from "styled-components";

export default function MiniNavBar2({ tab }) {
  const [query] = useSearchParams();
  const navigate = useNavigate();
  const addQueryParams = (query) => {
    const params = new URLSearchParams(location.search);

    if (params.has("filterss") || params.has("filter") || params.has("tab")) {
      params.set("tab2", query); // Update if exists
    } else {
      params.set("tab2", query); // Add if not exists
    }

    navigate({
      search: params.toString(),
    });
  };
  const active = query.get("tab2");
  return (
    <MiniNavStyle>
      <div
        style={{
          borderBottom: "1px solid #EAECF0",
          display: "flex",
          marginBottom: "20px",
        }}
        className="container"
      >
        {tab.map((item) => {
          return (
            <Tab
              $active={active === item?.tab}
              onClick={() => {
                addQueryParams(item?.tab);
              }}
              style={{
                paddingBottom: "10px",
                paddingLeft: "4px",
                paddingRight: "4px",
                borderBottom:
                  active !== item?.tab
                    ? "1px solid transparent"
                    : "1px solid #FF6500",
                width: "fit-content",
                fontSize: "14px",
                cursor: "pointer",
                marginRight: "10px",
              }}
              className="tab"
            >
              <span
                style={{
                  width: "100%",
                  color: active !== item?.tab ? "#667085" : "#FF6500",
                }}
              >
                {item?.name}
              </span>
            </Tab>
          );
        })}
      </div>
    </MiniNavStyle>
  );
}

const MiniNavStyle = styled.div`
  @media screen and (max-width: 1250px) {
    .tab {
      font-size: 14px !important;
    }
  }
  @media screen and (max-width: 650px) {
    .container {
      width: 100% !important;
      display: grid !important;
      grid-template-columns: 1fr 1fr !important;
      grid-gap: 10px;
      padding-bottom: 20px;
    }
    .tab {
      font-size: 11px !important;
      padding: 6px 2px !important;
      width: 100% !important;
      text-align: center;
      background: 1px solid #eaecf0;
    }
  }
`;

const Tab = styled.div`
  border-bottom: ${(props) =>
    props.$active
      ? "1px solid #FF6500 !important"
      : "1px solid #eaecf0 !important"};
`;
