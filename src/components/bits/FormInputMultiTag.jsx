import { getIn } from "formik";
import styled from "styled-components";
import { useState, useEffect } from "react";

export default function FormInputMultiTag({
  name,
  placeholder,
  value = [],
  label,
  disabled,
  padding = "12px",
  width = "100%",
  bottom = "0px",
  labelColor = "#000",
  background = "#F6F8FA",
  formik,
  IconRight,
  hint,
  onChange,
}) {
  const [tags, setTags] = useState([]);
  const [inputValue, setInputValue] = useState("");

  useEffect(() => {
    // Initialize tags from formik or value prop
    const initialTags = value.length
      ? value
      : getIn(formik?.values, name) || [];
    setTags(Array.isArray(initialTags) ? initialTags : []);
  }, [formik?.values, name, value]);

  const handleKeyDown = (e) => {
    if (e.key === "Enter" || e.key === ",") {
      e.preventDefault();
      addTag();
    } else if (e.key === "Backspace" && inputValue === "" && tags.length > 0) {
      removeTag(tags.length - 1);
    }
  };

  const addTag = () => {
    const trimmedInput = inputValue.trim();
    if (trimmedInput && !tags.includes(trimmedInput)) {
      const newTags = [...tags, trimmedInput];
      setTags(newTags);
      setInputValue("");

      // Update formik or call onChange
      if (formik) {
        formik.setFieldValue(name, newTags);
      }
      if (onChange) {
        onChange(newTags);
      }

      // Log for debugging
      console.log("Added tag:", trimmedInput);
      console.log("Current tags:", newTags);
    }
  };

  const removeTag = (index) => {
    const newTags = tags.filter((_, i) => i !== index);
    setTags(newTags);

    // Update formik or call onChange
    if (formik) {
      formik.setFieldValue(name, newTags);
    }
    if (onChange) {
      onChange(newTags);
    }
  };

  return (
    <div
      style={{
        width: "100%",
        marginBottom: "12px",
      }}
    >
      {label && (
        <Label
          style={{
            color: labelColor,
          }}
          htmlFor={name}
        >
          {label}
        </Label>
      )}
      <AppInputStyle
        style={{
          border: getIn(formik?.errors, name)
            ? "2px solid #E10000"
            : "1px solid #ECEFF3",
          width: width,
          marginBottom: bottom,
          background: background,
        }}
      >
        <TagsContainer>
          {tags.map((tag, index) => (
            <Tag key={index}>
              <span>{tag}</span>
              <TagRemoveButton onClick={() => removeTag(index)}>
                ×
              </TagRemoveButton>
            </Tag>
          ))}
          <TagInput
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={handleKeyDown}
            onBlur={addTag}
            placeholder={tags.length === 0 ? placeholder : ""}
            disabled={disabled}
            style={{
              padding: padding,
            }}
          />
          {IconRight && (
            <div
              style={{
                paddingRight: padding,
                transform: "translateY(10%)",
              }}
            >
              <IconRight width="20px" height="20px" />
            </div>
          )}
        </TagsContainer>
      </AppInputStyle>
      {getIn(formik?.errors, name) ? (
        <div
          style={{
            color: "#E10000",
            fontSize: "12px",
            marginTop: "4px",
            display: "flex",
            alignItems: "center",
          }}
        >
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            style={{
              width: "14px",
            }}
          >
            <path
              d="M1.83337 8C1.83337 4.59425 4.59429 1.83334 8.00004 1.83334C11.4058 1.83334 14.1667 4.59425 14.1667 8C14.1667 11.4058 11.4058 14.1667 8.00004 14.1667C4.59428 14.1667 1.83337 11.4058 1.83337 8Z"
              fill="white"
            />
            <path
              d="M8.00004 8.66667L8.00004 5.16667M1.83337 8C1.83337 4.59425 4.59429 1.83334 8.00004 1.83334C11.4058 1.83334 14.1667 4.59425 14.1667 8C14.1667 11.4058 11.4058 14.1667 8.00004 14.1667C4.59428 14.1667 1.83337 11.4058 1.83337 8Z"
              stroke="#E10000"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M7.99996 11.25C8.32213 11.25 8.58329 10.9888 8.58329 10.6667C8.58329 10.3445 8.32213 10.0833 7.99996 10.0833C7.67779 10.0833 7.41663 10.3445 7.41663 10.6667C7.41663 10.9888 7.67779 11.25 7.99996 11.25Z"
              fill="#E10000"
              stroke="#E10000"
              strokeWidth="0.5"
            />
          </svg>
          &nbsp;
          {getIn(formik?.errors, name)}
        </div>
      ) : hint ? (
        <div
          style={{
            color: "#818898",
            fontSize: "12px",
            marginTop: "4px",
          }}
        >
          {hint}
        </div>
      ) : (
        ""
      )}
    </div>
  );
}

const Label = styled.label`
  font-size: 14px;
  font-weight: 300;
  color: #36394a;
  font-weight: 400;
  display: block;
  width: fit-content;
`;

const AppInputStyle = styled.div`
  outline: none;
  position: relative;
  border-radius: 8px;
  background-color: white;
  padding: 0;
  margin-top: 10px;
`;

const TagsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding: 4px;
  min-height: 42px;
`;

const Tag = styled.div`
  display: flex;
  align-items: center;
  margin: 2px;
  padding: 4px 8px;
  background-color: #f0f2f5;
  border-radius: 4px;
  font-size: 14px;
`;

const TagRemoveButton = styled.button`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  margin-left: 4px;
  background: none;
  color: #666;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 14px;
  line-height: 1;

  &:hover {
    background-color: rgba(0, 0, 0, 0.1);
  }
`;

const TagInput = styled.input`
  flex: 1;
  min-width: 60px;
  border: none;
  outline: none;
  background: none;
  font-size: 16px;

  &::placeholder {
    color: #a4acb9;
    font-size: 16px;
  }
`;
