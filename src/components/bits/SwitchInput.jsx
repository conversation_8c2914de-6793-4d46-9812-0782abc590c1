import styled from "styled-components";

const SwitchInput = ({
  formik,
  name,
  label,
  disabled = false,
  onChange,
  checked,
  ...props
}) => {
  const handleChange = (e) => {
    if (formik) {
      formik.setFieldValue(name, e.target.checked);
    } else if (onChange) {
      onChange(e.target.checked);
    }
    console.log(e.target.checked, "cehcc");
  };

  const isChecked = formik ? formik.values[name] : checked;

  return (
    <SwitchContainer>
      {label && <Label>{label}</Label>}
      <div
        style={{
          transform: "translateY(16%)",
        }}
      >
        <SwitchWrapper>
          <SwitchCheckbox
            type="checkbox"
            checked={isChecked}
            onChange={handleChange}
            disabled={disabled}
            {...props}
          />
          <SwitchSlider />
        </SwitchWrapper>
      </div>
    </SwitchContainer>
  );
};

const SwitchContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const Label = styled.label`
  font-size: 14px;
  color: #333;
  cursor: pointer;
`;

const SwitchWrapper = styled.label`
  position: relative;
  display: inline-block;
  width: 44px;
  height: 16px;
  cursor: pointer;
`;

const SwitchCheckbox = styled.input`
  opacity: 0;
  width: 0;
  height: 0;

  &:checked + span {
    background-color: #169d0784;
  }

  &:checked + span:before {
    transform: translateX(24px);
  }

  &:disabled + span {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const SwitchSlider = styled.span`
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 24px;

  &:before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: 0px;
    top: -3px;
    background-color: #169d07;
    transition: 0.4s;
    border-radius: 50%;
  }
`;

export default SwitchInput;
