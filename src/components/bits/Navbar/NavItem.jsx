import { useNavigate } from "react-router-dom";
import DropDownIcon from "@/assets/icons/bit-icons/DropDownIcon";
import { useState } from "react";
import styled from "styled-components";

export default function NavItem({
  route,
  name = "Overview",
  Icon,
  menu,
  onClick,
}) {
  const navigate = useNavigate();

  const [dropMenu, openDropMenu] = useState(false);
  const currentRoute = "/" + window.location.pathname.split("/")[1];
  const navRoute = "/" + route?.split("?")[0].split("#")[0].split("/")[1];
  return (
    <>
      {/*  <div style={{ color: "#fff" }}>
        {currentRoute} ===
        {navRoute}
      </div> */}
      <NavItemContainer
        $active={menu ? false : currentRoute === navRoute}
        onClick={() => {
          if (menu) {
            openDropMenu(!dropMenu);
          } else {
            navigate(route);
            onClick && onClick();
          }
        }}
      >
        <NavItemSept>
          {Icon && (
            <>
              <Icon
                width="20px"
                height="20px"
                style={{
                  color: currentRoute === navRoute ? "#FF7434" : "#DEDFDF",
                }}
              />{" "}
              &nbsp;&nbsp;&nbsp;
            </>
          )}
          <NavItemText>{name}</NavItemText>
        </NavItemSept>
        {menu && (
          <DropDownIcon
            style={{
              transform: dropMenu && "rotate(90deg)",
            }}
          />
        )}
      </NavItemContainer>
      {dropMenu && <MenuSept>{menu}</MenuSept>}
    </>
  );
}

const NavItemContainer = styled.div`
  background-color: ${(props) =>
    props?.$active ? "#ffffff26" : "transparent"};
  padding: 16px 16px;
  border-radius: 999px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
`;
const NavItemSept = styled.div`
  display: flex;
  align-items: center;
`;
const NavItemText = styled.div`
  color: #fff;
  font-weight: 300;
  font-size: 14px;
`;

const MenuSept = styled.div`
  margin-left: 24px;
  padding: 0 16px;
  border-left: 1px solid #ffffff26;
`;
