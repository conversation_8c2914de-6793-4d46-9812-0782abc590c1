import { useNavigate } from "react-router-dom";
import styled, { keyframes } from "styled-components";

function AppButton({
  onClick,
  placeholder,
  loading,
  disabled,
  outline,
  secondary,
  showBorder = true,
  color = "#FF6500",
  borderColor,
  style,
  style2,
  IconLeft,
  IconRight,
  to,
  width = "100%",
  roundedFull,
  radius,
  textColor = "#000",
  borderWidth = 1,
}) {
  const navigate = useNavigate();
  return (
    <BtnBody $width={width} style={style2}>
      <ButtonStyle
        disabled={disabled}
        style={{
          cursor: (disabled && "not-allowed") || "pointer",
          background:
            (outline && "none") || loading
              ? "none"
              : disabled
              ? "#D5DBE5"
              : secondary
              ? "#E1E1E2"
              : color,
          border:
            outline &&
            showBorder &&
            `${borderWidth}px solid ${borderColor || "#D0D5DD"}`,
          borderRadius: roundedFull ? "1000px" : radius,
          ...style,
        }}
        onClick={() => {
          if (to) {
            navigate(to);
          }
          if (onClick) {
            onClick();
          }
        }}
      >
        {loading ? (
          <Spinner />
        ) : (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: "6px",

              color: disabled
                ? "#fff"
                : outline || secondary
                ? textColor
                : color?.slice(1, 10000)?.toString() === "FF6500"
                ? "#fff"
                : textColor,
            }}
          >
            {IconLeft ? (
              <IconLeft
                color={
                  outline || secondary
                    ? textColor
                    : color?.slice(1, 10000)?.toString() === "FF6500"
                    ? "#fff"
                    : textColor
                }
                width="15px"
                height="15px"
              />
            ) : (
              ""
            )}
            {placeholder}
            {IconRight ? (
              <IconRight
                color={
                  outline || secondary
                    ? textColor
                    : color?.slice(1, 10000)?.toString() === "FF6500"
                    ? "#fff"
                    : textColor
                }
                width="15px"
                height="15px"
              />
            ) : (
              ""
            )}
          </div>
        )}
      </ButtonStyle>
    </BtnBody>
  );
}

export default AppButton;

const BtnBody = styled.div`
  width: ${(props) => props?.$width};
  transition: all 0.3s;
`;

const ButtonStyle = styled.button`
  width: 100%;
  border: none;
  color: white;
  border-radius: 10px;
  padding: 14px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  display: grid;
  place-items: center;
  transition: all 0.3s;
`;

const rotate360 = keyframes`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`;

const Spinner = styled.div`
  animation: ${rotate360} 1s linear infinite;
  transform: translateZ(0);

  border-top: 2px solid #ff6500;
  border-right: 2px solid #ff6500;
  border-bottom: 2px solid #ff6500;
  border-left: 2px solid #999eaf;
  background: transparent;
  width: 16px;
  height: 16px;
  border-radius: 50%;
`;
