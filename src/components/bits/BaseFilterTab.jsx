import { useNavigate, useSearchParams } from "react-router-dom";
import styled from "styled-components";

export default function BaseFilterTab({ tab }) {
  const [query] = useSearchParams();
  const navigate = useNavigate();
  const addQueryParams = (query) => {
    const params = new URLSearchParams(location.search);

    if (params.has("filter")) {
      params.set("filterss", query); // Update if exists
    } else {
      params.set("filterss", query); // Add if not exists
    }

    navigate({
      search: params.toString(),
    });
  };
  const active = query.get("filterss");
  return (
    <MiniNavStyle>
      <div
        style={{
          border: "1px solid #D0D5DD",
          background: "#F2F4F7",
          borderRadius: "10px",
          overflow: "hidden",
          width: "fit-content",
          display: "flex",
          padding: "2px",
        }}
        className="cont"
      >
        {tab?.map((item) => {
          return (
            <div
              onClick={() => {
                addQueryParams(item?.tab);
              }}
              style={{
                padding: "8px 0",
                width: "100px",
                textAlign: "center",
                borderRadius: "8px",
                background: active === item?.tab && "#ffffff",
                border:
                  active !== item?.tab ? "1px solid #fff" : "1px solid #FF6500",
                fontSize: "15px",
                cursor: "pointer",
              }}
              className="tab"
            >
              <span
                style={{
                  width: "100%",
                  color: active === item?.tab ? "#FF6500" : "#344054",
                }}
              >
                {item?.name}
              </span>
            </div>
          );
        })}
      </div>
    </MiniNavStyle>
  );
}

const MiniNavStyle = styled.div`
  @media screen and (max-width: 1250px) {
    .tab {
      font-size: 14px !important;
    }
  }
  @media screen and (max-width: 650px) {
    .cont {
      width: 100% !important;
      display: grid !important;
      grid-template-columns: 1fr 1fr !important;
      grid-gap: 10px;
      border: none !important;
      border-radius: 0px;
      background: none !important;
    }
    .tab {
      font-size: 11px !important;
      padding: 6px 2px !important;
      width: 100% !important;
      text-align: center;
      border: 1px solid #eaecf0;
      background: 1px solid #eaecf0;
      border-radius: 10px;
    }
  }
`;
