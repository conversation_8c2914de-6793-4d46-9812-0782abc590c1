import WalletGreenIcon from "@/assets/icons/WalletGreenIcon";
import { CFormatter, FormatCurrency } from "@/lib/utils";
import { Card, Flex } from "@radix-ui/themes";
import styled from "styled-components";

export default function BalanceCard({
  amount,
  value,
  title = "Payouts",
  Icon = WalletGreenIcon,
}) {
  return (
    <Card
      style={{
        width: "100%",
        padding: "30px 24px",
      }}
    >
      <Icon
        style={{
          transform: "translateX(-15%)",
        }}
      />
      <Style>
        <Flex justify="between">
          <Flex>
            <div>
              <div
                style={{
                  color: "#818181",
                }}
                className="dText"
              >
                {title}
              </div>
              <h2
                style={{
                  color: "#333B4A",
                  fontWeight: "600",
                  fontSize: "20px",
                }}
              >
                {amount ? FormatCurrency(amount, "ngn") : CFormatter(value)}
              </h2>
            </div>
          </Flex>
        </Flex>
      </Style>
    </Card>
  );
}

const Style = styled.div`
  .dText {
    font-size: 15px;
    margin-bottom: 16px;
  }
`;
