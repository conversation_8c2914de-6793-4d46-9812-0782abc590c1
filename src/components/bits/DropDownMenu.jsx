/* import { DotsVerticalIcon } from "@radix-ui/react-icons";
import { Dropdown, Menu, Space } from "@arco-design/web-react";
import { useNavigate } from "react-router-dom";

export const MenuList = ({
  children,
  onClick,
  ActionIcon,
  ActionElement,
  iconWidth,
}) => {
  return (
    <Space
      className="dropdown-demo"
    >
      <Dropdown
        droplist={<Menu>{children}</Menu>}
        trigger="click"
        position="bl"
      >
        {ActionIcon ? (
          <ActionIcon
            onClick={onClick}
            width={iconWidth}
            style={{
              cursor: "pointer",
            }}
          />
        ) : ActionElement ? (
          ActionElement
        ) : (
          <DotsVerticalIcon
            onClick={onClick}
            width={iconWidth}
            style={{
              cursor: "pointer",
            }}
          />
        )}
      </Dropdown>
    </Space>
  );
};

export const MenuItem = ({
  name,
  Icon,
  action,
  index,
  to,
  width,
  padding = 0,
  color = "",
}) => {
  const navigate = useNavigate();
  return (
    <Menu.Item
      key={index}
      style={{
        fontWeight: "300",
        fontSize: "12px",
        display: "flex",
        width: width,
        alignItems: "center",
        padding: padding ? `${padding + 10}px` : "18px",
        paddingTop: padding ? `${padding + 20}px` : "28px",
      }}
      onClick={() => {
        if (to) {
          navigate(to);
        } else {
          action();
        }
      }}
    >
      {Icon && (
        <div>
          <Icon width="20px" height="20px" style={{ opacity: 0.6 }} /> &nbsp;
          &nbsp;
        </div>
      )}
      <div
        style={{
          marginTop: "-10px",
          color,
          fontWeight: 400,
        }}
      >
        {name}
      </div>
    </Menu.Item>
  );
};
 */

import { DotsVerticalIcon } from "@radix-ui/react-icons";
import { Dropdown, Menu, Space } from "@arco-design/web-react";
import { useNavigate } from "react-router-dom";

export const MenuList = ({
  children,
  onClick,
  ActionIcon,
  ActionElement,
  iconWidth,
}) => {
  return (
    <Space
      className="dropdown-demo"
      style={{
        borderRadius: "40px",
        position: "relative",
        zIndex: 1,
      }}
    >
      <Dropdown
        droplist={
          <div>
            <Menu>{children}</Menu>
          </div>
        }
        trigger="focus"
        position="bl"
      >
        {ActionIcon ? (
          <ActionIcon
            onClick={onClick}
            width={iconWidth}
            style={{
              cursor: "pointer",
            }}
          />
        ) : ActionElement ? (
          ActionElement
        ) : (
          <DotsVerticalIcon
            onClick={onClick}
            width={iconWidth}
            style={{
              cursor: "pointer",
            }}
          />
        )}
      </Dropdown>
    </Space>
  );
};

export const MenuItem = ({
  name,
  Icon,
  action,
  index,
  to,
  width,
  padding = 2,
  color = "",
}) => {
  const navigate = useNavigate();
  return (
    <Menu.Item
      key={index}
      style={{
        fontWeight: "300",
        fontSize: "12px",
        display: "flex",
        width: width,
        alignItems: "center",
        padding: padding && `${padding}px`,
        paddingTop: padding && `${padding + 10}px`,
        paddingInline: padding && `${padding + 10}px`,
      }}
      onClick={() => {
        if (to) {
          navigate(to);
        } else {
          action();
        }
      }}
    >
      {Icon && (
        <div>
          <Icon width="20px" height="20px" style={{ opacity: 0.6 }} /> &nbsp;
          &nbsp;
        </div>
      )}
      <div
        style={{
          marginTop: "-10px",
          color,
          fontWeight: 400,
        }}
      >
        {name}
      </div>
    </Menu.Item>
  );
};
