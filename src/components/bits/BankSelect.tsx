import React, { FC } from "react";
import { Flex } from "@radix-ui/themes";
import CurrencyFlagImage from "react-currency-flags";
import MainSelect from "@/components/bits/MainSelect";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useQuery } from "@tanstack/react-query";

interface Props {
  setCurrency?: any;
  currency?: any;
  options?: any[];
  label?: string;
  width?: string;
  hint?: string;
  defaultValue?: any;
  disabled?: boolean;
  name?: string;
  formik?: any;
  cutBorder?: boolean;
  isSearchable?: boolean;
  showError?: boolean;
  optionValue?: string;
}

const BankSelect: FC<Props> = ({
  setCurrency,
  currency,
  options,
  label,
  width = "127px",
  hint,
  defaultValue,
  disabled,
  name,
  formik,
  cutBorder,
  isSearchable,
  showError,
  optionValue,
}) => {
  const { data } = useQuery({
    queryKey: ["GetBanksQueryLissssststs"],
    queryFn: () => ApiServiceAdmin.GetBanksQuery(),
  });

  const mainOptions = data?.data?.map((item) => {
    return {
      label: item.bankName,
      value: item.bankName,
      ...item,
    };
  });
  return (
    <div>
      <MainSelect
        label={label}
        options={options ? options : mainOptions}
        onChange={setCurrency}
        optionValue={optionValue}
        value={formik ? null : currency}
        width={width}
        placeholder=""
        elementValue={undefined}
        defaultValue={defaultValue}
        disabled={disabled}
        name={name}
        formik={formik}
        hint={hint}
        cutBorder={cutBorder}
        isSearchable={isSearchable}
        showError={showError}
      />
    </div>
  );
};

export default BankSelect;
