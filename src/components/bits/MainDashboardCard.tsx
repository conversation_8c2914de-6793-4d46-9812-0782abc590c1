import { FormatCurrency } from "@/lib/utils";
import React, { FC } from "react";
import Divider from "../Divider";

interface Props {
  amount?: number;
  title?: string;
  count?: string;
  divider?: boolean;
  currency?: string;
  difference?: number;
  timeType?: string;
}

const MainDashboardCard: FC<Props> = ({
  amount,
  title,
  count,
  divider,
  currency = "ngn",
  difference,
  timeType,
}) => {
  const data = difference && difference?.toString();
  const ifUp = data && !data?.includes("-");
  return (
    <div
      style={{
        display: "flex",
        width: "100%",
        justifyContent: "space-between",
        gap: "1rem",
      }}
    >
      <div>
        <div
          style={{
            color: "#818181",
            fontSize: "13px",
          }}
          className="dText"
        >
          {title}
        </div>
        <h2
          style={{
            color: "#333B4A",
            fontWeight: "600",
            fontSize: "30px",
          }}
        >
          {FormatCurrency(amount?.toFixed(2) || 0, currency)}
        </h2>
        <div
          style={{
            color: "#333B4A",
            fontSize: "14px",
            marginBottom: "3px",
          }}
          className="dText"
        >
          Count: {count}
        </div>
        {difference ? (
          <div
            style={{
              color: ifUp ? "#12B76A" : "#F04438",
              display: "flex",
              alignItems: "center",
              gap: 4,
              fontSize: "13px",
            }}
          >
            {" "}
            {ifUp ? <UpArrow /> : <DownArrow />}{" "}
            {difference?.toFixed(2)?.toString()?.replace(/-/g, "")}%{" "}
            <span
              style={{
                color: "#667085",
              }}
            >
              vs last {timeType}
            </span>
          </div>
        ) : title === "All Time" ? (
          ""
        ) : (
          <div
            style={{
              color: "#667085",
              display: "flex",
              alignItems: "center",
              gap: 4,
              fontSize: "13px",
            }}
          >
            {" "}
            <NeutralArrow /> {difference}%{" "}
            <span
              style={{
                color: "#667085",
              }}
            >
              vs last {timeType}
            </span>
          </div>
        )}
      </div>
      {divider && <Divider />}
    </div>
  );
};

const UpArrow = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.0001 15.8334V4.16675M10.0001 4.16675L4.16675 10.0001M10.0001 4.16675L15.8334 10.0001"
        stroke="#12B76A"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

const DownArrow = () => {
  return (
    <svg
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.00008 1.16675V12.8334M7.00008 12.8334L12.8334 7.00008M7.00008 12.8334L1.16675 7.00008"
        stroke="#F04438"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

const NeutralArrow = () => {
  return (
    <svg
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.00008 1.16675V12.8334M7.00008 12.8334L12.8334 7.00008M7.00008 12.8334L1.16675 7.00008"
        stroke="#667085"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export default MainDashboardCard;
