import React, { FC } from "react";
import { Flex } from "@radix-ui/themes";
import CurrencyFlagImage from "react-currency-flags";
import MainSelect from "@/components/bits/MainSelect";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useQuery } from "@tanstack/react-query";

interface Props {
  onChange?: any;
  value?: any;
  options?: any[];
  label?: string;
  width?: string;
  hint?: string;
  defaultValue?: any;
  disabled?: boolean;
  name?: string;
  formik?: any;
  cutBorder?: boolean;
  isSearchable?: boolean;
  showError?: boolean;
  optionValue;
}

const PaymentChannelSelect: FC<Props> = ({
  onChange,
  value,
  options,
  label,
  width = "100%",
  hint,
  defaultValue,
  disabled,
  name,
  formik,
  cutBorder,
  isSearchable,
  showError,
  optionValue,
}) => {
  const { data: list } = useQuery({
    queryKey: ["GetPaymentChannelSelectQuery"],
    queryFn: () => ApiServiceAdmin.GetPaymentChannelQuery(),
  });

  const channels =
    list &&
    list.data.map((itm) => {
      return {
        label: itm.name,
        value: itm.id,
      };
    });
  return (
    <div>
      <MainSelect
        label={label}
        options={options ? options : channels}
        onChange={onChange}
        optionValue={optionValue}
        value={value}
        width={width}
        placeholder=""
        defaultValue={defaultValue}
        disabled={disabled}
        name={name}
        formik={formik}
        hint={hint}
        cutBorder={cutBorder}
        isSearchable={isSearchable}
        showError={showError}
        elementValue={undefined}
      />
    </div>
  );
};

export default PaymentChannelSelect;
