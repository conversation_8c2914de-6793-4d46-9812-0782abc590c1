import { Flex, Text, Checkbox as Checkboxx } from "@radix-ui/themes";

export default function Checkbox({
  label,
  direction,
  name,
  onChange,
  value,
  key,
  isChecked,
}) {
  return (
    <Text
      as="label"
      size="2"
      style={{
        color: "#8A919F",
      }}
    >
      <Flex gap="2" direction={{ initial: "row", md: direction }}>
        <Checkboxx
          //onChange={onChange}
          onCheckedChange={onChange}
          type="checkbox"
          name={name}
          checked={isChecked}
          key={key}
          value={value}
          color="amber"
          size="2"
        />
        {label}
      </Flex>
    </Text>
  );
}
