import { Link } from "react-router-dom";

export default function AuthFooter({
  to,
  toName,
  desc,
  align = "center",
  onClick,
}) {
  return (
    <>
      {desc && <>&nbsp;</>}
      <div
        style={{
          textAlign: align,
        }}
      >
        {desc}{" "}
        {to ? (
          <Link to={to} style={{ color: "#FF6500" }}>
            {toName}
          </Link>
        ) : (
          <div
            onClick={onClick}
            style={{ color: "#FF6500", cursor: "pointer" }}
          >
            {toName}
          </div>
        )}
      </div>
    </>
  );
}
