import { Flex } from "@radix-ui/themes";
import { Edit } from "lucide-react";

export default function DetailsHeading({ title, onClick }) {
  return (
    <>
      {" "}
      <Flex justify="between">
        <h3
          style={{
            fontWeight: "500",
            fontSize: "16px",
          }}
        >
          {title}
        </h3>
        {onClick && <Edit onClick={onClick} size={18} color="#8f8f8f" />}
      </Flex>
      <hr
        style={{
          margin: "24px 0",
        }}
        color="#eaecf0"
      />
    </>
  );
}
