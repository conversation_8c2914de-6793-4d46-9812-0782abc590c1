import { Pagination } from "@arco-design/web-react";
import styled from "styled-components";

function itemRender(page, type, originElement) {
  if (type === "prev") {
    return (
      <a style={{ fontSize: 14, margin: "0 8px", cursor: "pointer" }}>Prev</a>
    );
  }

  if (type === "next") {
    return (
      <a style={{ fontSize: 14, margin: "0 8px", cursor: "pointer" }}>Next</a>
    );
  }

  return originElement;
}

const PaginationComponent = ({ total, pageSize, onChange, current = 1 }) => {
  return (
    <PaginationStyle
      style={{
        padding: "10px",
        border: "2px solid #f1f1f1",
        borderRadius: "8px",
        marginTop: "10px",
      }}
    >
      <Pagination
        itemRender={itemRender}
        total={total}
        pageSize={pageSize}
        current={current + 1}
        onChange={(data) => {
          onChange(data);
        }}
      />
    </PaginationStyle>
  );
};

export default PaginationComponent;

const PaginationStyle = styled.div`
  .arco-pagination-item-active {
    background-color: #000;
    border-radius: 8px;
    color: #fff;
  }
`;
