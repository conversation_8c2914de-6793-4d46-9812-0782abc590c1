import { AlertDialog, Flex } from "@radix-ui/themes";
import AppButton from "./AppButton";

export default function Modal({
  trigger,
  open,
  Icon,
  title = "Modal Title",
  description,
  width = "500px",
  children,
  submit,
  dialogBtns = true,
  pads = true,
}) {
  return (
    <AlertDialog.Root onOpenChange={trigger} open={open}>
      <AlertDialog.Content
        maxWidth={width}
        style={{
          borderRadius: "14px",
          padding: pads ? "" : "0",
        }}
      >
        {Icon ? (
          <Icon
            style={{
              marginBottom: "10px",
            }}
          />
        ) : (
          ""
        )}
        {title && (
          <AlertDialog.Title
            style={{
              fontSize: "20px",
              fontWeight: 500,
              fontFamily: "Fira Sans",
            }}
          >
            {title}
          </AlertDialog.Title>
        )}
        <AlertDialog.Description size="2">
          {description}
        </AlertDialog.Description>
        {pads && <br />}
        {children}
        {pads && <br />}
        {dialogBtns && (
          <Flex gap="3" mt="4" justify="end">
            <AppButton
              placeholder="Cancel"
              width="30%"
              loading={false}
              disabled={false}
              onClick={trigger}
              outline
            />
            <AppButton
              placeholder="Proceed"
              width="70%"
              loading={false}
              disabled={false}
              onClick={submit}
            />
          </Flex>
        )}
      </AlertDialog.Content>
    </AlertDialog.Root>
  );
}
