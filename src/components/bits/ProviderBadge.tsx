import { Flex } from "@radix-ui/themes";
import React, { FC } from "react";
import CurrencyFlagImage from "react-currency-flags";

type Props = {
  logo?: string;
  name?: string;
  fontSize?: string;
  color?: string;
};
export const ProviderBadge: FC<Props> = ({
  logo,
  name,
  fontSize = "16px",
  color = "#5a6376",
}) => {
  return (
    <Flex className="countryName" gap={"2"} align={"center"}>
      <div
        style={{
          height: "22px",
          width: "22px",
          display: "grid",
          placeItems: "center",
          border: "1px solid #cecece",
          borderRadius: "999px",
          background: "#fff",
          overflow: "hidden",
        }}
      >
        <img
          src={logo}
          style={{ width: "100%", height: "100%", objectFit: "contain" }}
        />
      </div>
      <span style={{ fontSize: fontSize, color: color }}>{name}</span>
    </Flex>
  );
};
