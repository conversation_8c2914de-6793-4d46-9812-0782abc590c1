import React, { FC } from "react";
import { Flex } from "@radix-ui/themes";
import CurrencyFlagImage from "react-currency-flags";
import MainSelect from "@/components/bits/MainSelect";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useQuery } from "@tanstack/react-query";

interface Props {
  setCurrency?: any;
  currency?: any;
  options?: any[];
  label?: string;
  width?: string;
  hint?: string;
  defaultValue?: any;
  disabled?: boolean;
  name?: string;
  formik?: any;
  cutBorder?: boolean;
  isSearchable?: boolean;
  showError?: boolean;
  optionValue?: string;
}

const CountrySelect: FC<Props> = ({
  setCurrency,
  currency,
  options,
  label,
  width = "127px",
  hint,
  defaultValue,
  disabled,
  name,
  formik,
  cutBorder,
  isSearchable,
  showError,
  optionValue,
}) => {
  const { data } = useQuery({
    queryKey: ["GetCountriesQueryLissssssts"],
    queryFn: () => ApiServiceAdmin.GetCountriesQuery(),
  });

  const mainOptions = data?.data?.map((item) => {
    return {
      label: item.name,
      value: item.name,
      ...item,
    };
  });
  return (
    <div>
      <MainSelect
        label={label}
        options={options ? options : mainOptions}
        onChange={setCurrency}
        optionValue={optionValue}
        value={formik ? null : currency}
        width={width}
        placeholder=""
        elementValue={(provider) => (
          <Flex className="countryName" gap={"2"} align={"center"}>
            <div
              style={{
                height: "20px",
                width: "20px",
                display: "grid",
                placeItems: "center",
                border: "1px solid #cecece",
                borderRadius: "999px",
                background: "#fff",
                overflow: "hidden",
              }}
            >
              <CurrencyFlagImage
                currency={provider?.currencyCode}
                style={{ width: "18px", height: "18px" }}
                size="sm"
              />
            </div>
            <span style={{ fontSize: "16px" }}>{provider?.label}</span>
          </Flex>
        )}
        defaultValue={defaultValue}
        disabled={disabled}
        name={name}
        formik={formik}
        hint={hint}
        cutBorder={cutBorder}
        isSearchable={isSearchable}
        showError={showError}
      />
    </div>
  );
};

export default CountrySelect;
