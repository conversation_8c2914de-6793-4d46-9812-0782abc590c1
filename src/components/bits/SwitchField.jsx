import { Flex, Switch, Text } from "@radix-ui/themes";

export default function SwitchField({ label, direction, formik, name }) {
  return (
    <Text
      as="label"
      size="2"
      style={{
        color: "#8A919F",
      }}
    >
      <Flex gap="6" direction={{ initial: "row", md: direction }}>
        {label && label}
        <Switch
          color="amber"
          size="2"
          value={formik?.values?.[name]}
          onCheckedChange={() => {
            formik?.setFieldValue(name, !formik?.values?.[name]);
          }}
        />
      </Flex>
    </Text>
  );
}
