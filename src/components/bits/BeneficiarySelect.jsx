import ConfirmModal from "@/Modals/ConfirmModal";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useMutation } from "@tanstack/react-query";
import { Plus, Trash2 } from "lucide-react";
import { useState } from "react";

export default function BeneficiarySelect({
  formik,
  options,
  name,
  getBeneficiaries,
}) {
  const [open, setOpen] = useState();
  const [id, setId] = useState();

  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.DeleteBeneficiaryMutation,
    onSuccess: () => {
      setOpen(false);
      getBeneficiaries();
    },
    onError: () => {
      return;
    },
  });

  return (
    <>
      <ConfirmModal
        open={open}
        cancel={() => setOpen(false)}
        description={"Are you sure you want to delete this beneficiary"}
        loading={isPending}
        confirm={() => {
          mutate(id);
        }}
      />

      {options?.map((itm) => {
        return (
          <div
            style={{
              border:
                formik?.values?.[name] === itm?.id
                  ? "1px solid #FF7434"
                  : "1px solid #E9EDF5",
              padding: "20px",
              position: "relative",
              borderRadius: "8px",
              textAlign: "center",
              cursor: "pointer",
            }}
            onClick={() => {
              formik?.setFieldValue(name, itm?.id);
            }}
          >
            <div
              style={{
                position: "absolute",
                top: "20px",
                left: "20px",
                zIndex: 100,
              }}
            >
              {formik?.values?.[name] === itm?.id ? <Checked /> : <UnChecked />}
            </div>
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "space-between",
                alignItems: "center",
                height: "100%",
              }}
            >
              <div>
                <svg
                  width="71"
                  height="71"
                  viewBox="0 0 71 71"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M35.5002 0.0839844C15.9395 0.0839844 0.0834961 15.94 0.0834961 35.5006C0.0834961 55.0613 15.9395 70.9173 35.5002 70.9173C55.0608 70.9173 70.9168 55.0613 70.9168 35.5006C70.9168 15.94 55.0608 0.0839844 35.5002 0.0839844ZM23.1043 26.6465C23.1043 25.0186 23.425 23.4067 24.0479 21.9028C24.6709 20.3989 25.5839 19.0324 26.735 17.8813C27.886 16.7302 29.2526 15.8172 30.7565 15.1942C32.2604 14.5713 33.8723 14.2507 35.5002 14.2507C37.128 14.2507 38.7399 14.5713 40.2438 15.1942C41.7478 15.8172 43.1143 16.7302 44.2653 17.8813C45.4164 19.0324 46.3295 20.3989 46.9524 21.9028C47.5754 23.4067 47.896 25.0186 47.896 26.6465C47.896 29.9341 46.59 33.087 44.2653 35.4117C41.9407 37.7363 38.7877 39.0423 35.5002 39.0423C32.2126 39.0423 29.0597 37.7363 26.735 35.4117C24.4103 33.087 23.1043 29.9341 23.1043 26.6465ZM57.6639 53.1523C55.0127 56.4872 51.6424 59.1801 47.8045 61.0297C43.9666 62.8793 39.7605 63.838 35.5002 63.834C31.2398 63.838 27.0337 62.8793 23.1958 61.0297C19.3579 59.1801 15.9876 56.4872 13.3364 53.1523C19.0775 49.0334 26.9116 46.1256 35.5002 46.1256C44.0887 46.1256 51.9229 49.0334 57.6639 53.1523Z"
                    fill="#D3D0D0"
                  />
                </svg>
                <h3
                  style={{
                    color: "#5A6376",
                    fontSize: "22px",
                    textTransform: "capitalize",
                    fontWeight: "500",
                    marginBlock: "10px",
                  }}
                >
                  {itm?.beneficiaryName}
                </h3>
                <p
                  style={{
                    color: "#667085",
                    fontSize: "16px",
                    marginBottom: "20px",
                  }}
                >
                  {itm?.beneficiaryBank?.bankName}
                </p>
                <p
                  style={{
                    color: "#667085",
                    fontSize: "16px",
                    marginBottom: "40px",
                  }}
                >
                  Account ending with ...
                  {itm?.beneficiaryBank?.accountNumber?.slice(
                    itm?.beneficiaryBank?.accountNumber?.length - 4,
                    itm?.beneficiaryBank?.accountNumber?.length
                  )}
                </p>
              </div>

              <Trash2
                onClick={(e) => {
                  e.stopPropagation();
                  setId(itm?.id);
                  setOpen(true);
                }}
                color="#667085"
                size={18}
              />
            </div>
          </div>
        );
      })}
    </>
  );
}

export const AddBeneficiaryButtton = ({ onClick }) => {
  return (
    <div
      style={{
        border: "1px solid #E9EDF5",
        padding: "20px",
        position: "relative",
        borderRadius: "8px",
        textAlign: "center",
        cursor: "pointer",
      }}
      onClick={onClick}
    >
      <div
        style={{
          position: "absolute",
          top: "20px",
          left: "20px",
          zIndex: 100,
        }}
      ></div>
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "center",
          height: "100%",
        }}
      >
        {" "}
        <div>
          <h3
            style={{
              color: "#5A6376",
              fontSize: "22px",
              textTransform: "capitalize",
              fontWeight: "500",
              marginBlock: "10px",
            }}
          >
            Add Beneficiary
          </h3>
          <Plus color="#667085" size={18} />
        </div>
      </div>
    </div>
  );
};

const Checked = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="0.466535"
      y="0.466535"
      width="15.0669"
      height="15.0669"
      rx="6.99803"
      fill="#FF7434"
    />
    <rect
      x="0.466535"
      y="0.466535"
      width="15.0669"
      height="15.0669"
      rx="6.99803"
      stroke="#FF7434"
      stroke-width="0.933071"
    />
    <path
      d="M11.1101 5.66797L6.83355 9.94454L4.88965 8.00065"
      stroke="white"
      stroke-width="1.55512"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

const UnChecked = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="0.466535"
      y="0.466535"
      width="15.0669"
      height="15.0669"
      rx="6.99803"
      fill="white"
    />
    <rect
      x="0.466535"
      y="0.466535"
      width="15.0669"
      height="15.0669"
      rx="6.99803"
      stroke="#D0D5DD"
      stroke-width="0.933071"
    />
  </svg>
);
