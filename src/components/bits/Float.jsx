import styled from "styled-components";

export default function Float({
  floatTop,
  floatBottom,
  floatLeft,
  floatRight,
  children,
  ...props
}) {
  return (
    <FloatStyle
      {...props}
      $floatTop={floatTop}
      $floatBottom={floatBottom}
      $floatLeft={floatLeft}
      $floatRight={floatRight}
    >
      <br />
      {children}
      <br />
    </FloatStyle>
  );
}

const FloatStyle = styled.div`
  position: fixed;
  top: ${(props) => (props?.$floatTop ? "0px" : "")};
  bottom: ${(props) => (props?.$floatBottom ? "0px" : "")};
  left: ${(props) => (props?.$floatLeft ? "0px" : "")};
  right: ${(props) => (props?.$floatRight ? "0px" : "")};
  background-color: black;
  width: 100%;
  /* box-shadow: 0px 1px 29px -17px rgba(255, 255, 255, 0.75);
  -webkit-box-shadow: 0px 1px 29px -17px rgba(255, 255, 255, 0.75);
  -moz-box-shadow: 0px 1px 29px -17px rgba(255, 255, 255, 0.75); */
`;
