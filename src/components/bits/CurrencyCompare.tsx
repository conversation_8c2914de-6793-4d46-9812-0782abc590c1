import { FormatCurrency } from "@/lib/utils";
import { Flex } from "@radix-ui/themes";
import React, { FC } from "react";
import CurrencyFlagImage from "react-currency-flags";

type Props = {
  fromCurrency?: string;
  fromRate?: string;
  toCurrency?: string;
  toRate?: string;
};
export const CurrencyCompare: FC<Props> = (item) => {
  return (
    <Flex gap={"9"} align={"center"} justify={"center"} mt={"6"}>
      <Flex
        className="countryName"
        gap={"4"}
        align={"center"}
        justify={"center"}
        direction={"column"}
      >
        <div
          style={{
            height: "40px",
            width: "40px",
            display: "grid",
            placeItems: "center",
            border: "1px solid #cecece",
            borderRadius: "999px",
            background: "#fff",
            overflow: "hidden",
          }}
        >
          <CurrencyFlagImage
            currency={item?.fromCurrency && item?.fromCurrency}
            style={{ width: "38px", height: "38px" }}
            size="lg"
          />
        </div>
        <span style={{ fontSize: "20px" }}>
          {item?.fromRate && FormatCurrency(item?.fromRate, item?.fromCurrency)}
        </span>
      </Flex>
      <h2>=</h2>
      <Flex
        className="countryName"
        gap={"4"}
        align={"center"}
        justify={"center"}
        direction={"column"}
      >
        <div
          style={{
            height: "40px",
            width: "40px",
            display: "grid",
            placeItems: "center",
            border: "1px solid #cecece",
            borderRadius: "999px",
            background: "#fff",
            overflow: "hidden",
          }}
        >
          <CurrencyFlagImage
            currency={item?.toCurrency && item?.toCurrency}
            style={{ width: "38px", height: "38px" }}
            size="lg"
          />
        </div>
        <span style={{ fontSize: "20px" }}>
          {item?.toRate && FormatCurrency(item?.toRate, item?.toCurrency)}
        </span>
      </Flex>
    </Flex>
  );
};
