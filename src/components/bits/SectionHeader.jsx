import { useLocation, useNavigate } from "react-router-dom";
import styled from "styled-components";

export default function SectionHeader({
  title,
  description = "Section Description",
  fontSize = "32px",
  showBackBtn,
  align,
}) {
  const { pathname } = useLocation();

  const navigate = useNavigate();

  return (
    <SectionStyle
      style={{
        textAlign: align,
      }}
    >
      <div>
        {showBackBtn && (
          <svg
            width="92"
            height="36"
            viewBox="0 0 92 36"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            style={{
              transform: "translateX(-10%)",
            }}
            onClick={() => {
              navigate(-1);
            }}
          >
            <rect width="92" height="36" rx="8" fill="white" />
            <path
              d="M29.8337 17.9993H18.167M18.167 17.9993L24.0003 23.8327M18.167 17.9993L24.0003 12.166"
              stroke="#344054"
              stroke-width="1.67"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M43.064 23V12.64H47.32C47.9827 12.64 48.5473 12.7613 49.014 13.004C49.49 13.2373 49.8493 13.55 50.092 13.942C50.344 14.334 50.47 14.7633 50.47 15.23C50.47 15.7247 50.3767 16.1307 50.19 16.448C50.0127 16.756 49.8073 16.994 49.574 17.162C49.3407 17.33 49.14 17.4467 48.972 17.512V17.596C49.2053 17.6613 49.4713 17.7873 49.77 17.974C50.0687 18.1513 50.3253 18.4127 50.54 18.758C50.764 19.094 50.876 19.5327 50.876 20.074C50.876 20.5593 50.7593 21.026 50.526 21.474C50.2927 21.922 49.9193 22.2907 49.406 22.58C48.902 22.86 48.2393 23 47.418 23H43.064ZM47.166 14.04H44.702V17.036H47.166C47.7633 17.036 48.1973 16.882 48.468 16.574C48.7387 16.266 48.874 15.9207 48.874 15.538C48.874 15.1553 48.7387 14.81 48.468 14.502C48.1973 14.194 47.7633 14.04 47.166 14.04ZM47.292 18.324H44.702V21.6H47.292C47.7493 21.6 48.1227 21.5253 48.412 21.376C48.7013 21.2173 48.9113 21.012 49.042 20.76C49.1727 20.508 49.238 20.242 49.238 19.962C49.238 19.682 49.1727 19.4207 49.042 19.178C48.9113 18.926 48.7013 18.7207 48.412 18.562C48.1227 18.4033 47.7493 18.324 47.292 18.324ZM55.7277 15.44C56.1943 15.44 56.5817 15.5147 56.8897 15.664C57.207 15.8133 57.459 15.986 57.6457 16.182C57.8323 16.378 57.9677 16.546 58.0517 16.686H58.1357V15.58H59.7317V23H58.1357V21.894H58.0517C57.9677 22.034 57.8323 22.202 57.6457 22.398C57.459 22.594 57.207 22.7667 56.8897 22.916C56.5817 23.0653 56.1943 23.14 55.7277 23.14C55.1677 23.14 54.6777 23.0327 54.2577 22.818C53.847 22.594 53.5017 22.3 53.2217 21.936C52.9417 21.5627 52.7317 21.1473 52.5917 20.69C52.461 20.2327 52.3957 19.766 52.3957 19.29C52.3957 18.814 52.461 18.3473 52.5917 17.89C52.7317 17.4327 52.9417 17.022 53.2217 16.658C53.5017 16.2847 53.847 15.9907 54.2577 15.776C54.6777 15.552 55.1677 15.44 55.7277 15.44ZM56.0777 16.77C55.6203 16.77 55.2377 16.896 54.9297 17.148C54.6217 17.3907 54.3883 17.7033 54.2297 18.086C54.0803 18.4687 54.0057 18.87 54.0057 19.29C54.0057 19.71 54.0803 20.1113 54.2297 20.494C54.3883 20.8767 54.6217 21.194 54.9297 21.446C55.2377 21.6887 55.6203 21.81 56.0777 21.81C56.535 21.81 56.913 21.6887 57.2117 21.446C57.5197 21.194 57.7483 20.8767 57.8977 20.494C58.0563 20.1113 58.1357 19.71 58.1357 19.29C58.1357 18.87 58.0563 18.4687 57.8977 18.086C57.7483 17.7033 57.5197 17.3907 57.2117 17.148C56.913 16.896 56.535 16.77 56.0777 16.77ZM65.2115 23.14C64.5861 23.14 64.0401 23.0327 63.5735 22.818C63.1068 22.594 62.7195 22.3 62.4115 21.936C62.1035 21.5627 61.8701 21.1473 61.7115 20.69C61.5621 20.2327 61.4875 19.766 61.4875 19.29C61.4875 18.814 61.5621 18.3473 61.7115 17.89C61.8701 17.4327 62.1035 17.022 62.4115 16.658C62.7195 16.2847 63.1068 15.9907 63.5735 15.776C64.0401 15.552 64.5861 15.44 65.2115 15.44C65.8275 15.44 66.3361 15.5333 66.7375 15.72C67.1481 15.8973 67.4748 16.1213 67.7175 16.392C67.9601 16.6627 68.1421 16.9333 68.2635 17.204C68.3848 17.4747 68.4595 17.7033 68.4875 17.89C68.5248 18.0767 68.5435 18.17 68.5435 18.17H67.0315C67.0315 18.17 67.0081 18.1 66.9615 17.96C66.9241 17.82 66.8448 17.6567 66.7235 17.47C66.6021 17.2833 66.4201 17.12 66.1775 16.98C65.9441 16.84 65.6268 16.77 65.2255 16.77C64.7495 16.77 64.3528 16.8913 64.0355 17.134C63.7181 17.3673 63.4755 17.6753 63.3075 18.058C63.1488 18.4407 63.0695 18.8513 63.0695 19.29C63.0695 19.7287 63.1488 20.1393 63.3075 20.522C63.4755 20.9047 63.7181 21.2173 64.0355 21.46C64.3528 21.6933 64.7495 21.81 65.2255 21.81C65.6268 21.81 65.9441 21.74 66.1775 21.6C66.4201 21.46 66.6021 21.2967 66.7235 21.11C66.8448 20.9233 66.9241 20.76 66.9615 20.62C67.0081 20.48 67.0315 20.41 67.0315 20.41H68.5435C68.5435 20.41 68.5248 20.5033 68.4875 20.69C68.4595 20.8767 68.3848 21.1053 68.2635 21.376C68.1421 21.6467 67.9601 21.9173 67.7175 22.188C67.4748 22.4587 67.1481 22.6873 66.7375 22.874C66.3361 23.0513 65.8275 23.14 65.2115 23.14ZM70.4078 23V12.36H72.0038V18.534L74.7898 15.58H76.7498L73.5158 18.968L76.8478 23H74.8738L72.0038 19.5V23H70.4078Z"
              fill="#344054"
            />
          </svg>
        )}
        <div
          className="title_heading"
          style={{
            fontSize: fontSize,
          }}
        >
          {title
            ? title
            : pathname === "/"
            ? "Dashboard"
            : pathname?.replace("/", "")?.replace("-", " ")?.replace("-", " ")}
          {/* {title} */}
        </div>
        {description && <div className="description">{description}</div>}
      </div>
    </SectionStyle>
  );
}

const SectionStyle = styled.div`
  width: 100%;
  margin-bottom: 10px;

  .title_heading {
    font-weight: 500;
    color: #1d2939;
    text-transform: capitalize;
  }
  .description {
    font-size: 16px;
    color: #848d87;
    font-weight: 400;
  }

  @media screen and (max-width: 650px) {
    align-items: flex-start;
    flex-direction: column !important;
  }
`;
