import styled from "styled-components";
import AppButton from "./AppButton";
import { useState } from "react";
import axios from "axios";
import { getIn } from "formik";
import PDFImageRenderer from "../viewPdf";

const Attachment = () => (
  <svg
    width="17"
    height="19"
    viewBox="0 0 17 19"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M16.1271 8.58247L8.6141 16.0955C6.90556 17.804 4.13546 17.804 2.42692 16.0955C0.718372 14.3869 0.718372 11.6168 2.42692 9.9083L9.93992 2.39529C11.079 1.25626 12.9257 1.25626 14.0647 2.39529C15.2037 3.53432 15.2037 5.38105 14.0647 6.52008L6.84633 13.7385C6.27682 14.308 5.35345 14.308 4.78394 13.7385C4.21442 13.1689 4.21442 12.2456 4.78394 11.6761L11.1184 5.34157"
      stroke="#666D80"
      stroke-width="1.66667"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

export default function FormInputFile({
  name,
  type,
  placeholder,
  defaultValue,
  label,
  disabled,
  padding = "12px",
  width = "100%",
  bottom = "0px",
  labelColor = "#000",
  background = "#fff",
  required = false,
  formik,
  max,
  IconRight,
  IconLeft,
  hint,
  id,
  userId,
}) {
  const [isPending, setIsPending] = useState(false);
  const [inputKey, setInputKey] = useState(0); // Add this state

  const [fileName, setFileName] = useState("");

  const handleFileUpload = async (file) => {
    if (!file || !userId) return;

    setIsPending(true);
    const formData = new FormData();
    formData.append("file", file);

    try {
      const token = localStorage.getItem("token");
      const response = await axios({
        method: "post",
        url: `${import.meta.env.VITE_BASE_URL}/FileUploadAPI/${userId}`,
        data: formData,
        headers: {
          "Content-Type": "multipart/form-data",
          ...(token && { Authorization: `Bearer ${token}` }),
        },
      });

      if (response.data && response.data.secure_url) {
        formik.setFieldValue(name, response.data.secure_url);
      }
    } catch (error) {
      console.error("File upload failed:", error);
    } finally {
      setIsPending(false);
    }
  };

  const isPdf = getIn(formik?.values, name)?.includes(".pdf") ? true : false;

  return (
    <div
      style={{
        width: "100%",
        marginBottom: "12px",
      }}
    >
      {label && (
        <Label
          style={{
            color: labelColor,
          }}
        >
          {label}
        </Label>
      )}
      <AppInputStyle
        style={{
          border: getIn(formik?.errors, name)
            ? "2px solid #E10000"
            : "2px solid #ECEFF3",
          width: width,
          marginBottom: bottom,
          background: background,
          paddingTop: padding,
          paddingBottom: padding,
          paddingRight: padding,
          paddingLeft: padding,
        }}
      >
        <div
          style={{
            display: "flex",
            alignItems: "center",
          }}
        >
          {IconLeft && (
            <div
              style={{
                paddingLeft: padding,
                transform: "translateY(10%)",
              }}
            >
              <IconLeft width="20px" height="20px" />
            </div>
          )}

          <input
            key={inputKey} // Add the key prop
            type="file"
            id={id}
            name={name}
            required={required}
            defaultValue={defaultValue}
            onChange={(e) => {
              if (e?.target?.files?.[0]) {
                handleFileUpload(e.target.files[0]);
                setFileName(e.target.files[0].name);
              }
            }}
            placeholder={placeholder}
            disabled={disabled}
            hidden
            style={{
              paddingTop: padding,
              paddingBottom: padding,
              paddingRight: padding,
              paddingLeft: padding,
            }}
            maxLength={max}
            onKeyDown={(evt) => {
              if (type === "number") {
                ["e", "E", "+", "-", "=", "(", ")", "*", "&"].includes(
                  evt.key
                ) && evt.preventDefault();
              }
            }}
          />

          {IconRight && (
            <div
              style={{
                paddingRight: padding,
                transform: "translateY(10%)",
              }}
            >
              <IconRight width="20px" height="20px" />
            </div>
          )}
        </div>
        {getIn(formik?.values, name) ? (
          <>
            {isPdf ? (
              <PDFImageRenderer pdfName={fileName} />
            ) : (
              <img
                src={getIn(formik?.values, name)}
                style={{
                  width: "100%",
                  height: "100%",
                  objectFit: "contain",
                }}
              />
            )}
          </>
        ) : (
          <label
            style={{
              position: "absolute",
              left: "50%",
              top: "50%",
              transform: "translate(-50%, -50%)",
            }}
            htmlFor={id}
          >
            <AppButton
              disabled={isPending}
              outline
              loading={isPending}
              IconLeft={Attachment}
              borderColor={"#fff"}
              textColor="#666D80"
              placeholder="Choose File"
              style2={{
                pointerEvents: "none",
              }}
            />
          </label>
        )}
      </AppInputStyle>

      {getIn(formik?.values, name) && (
        <AppButton
          placeholder={"Delete"}
          outline
          borderColor={"#fff"}
          style2={{
            marginLeft: "auto",
          }}
          width="fit-content"
          textColor="red"
          onClick={() => {
            formik?.setFieldValue(name, "");
            setInputKey(inputKey + 1); // Add this line
          }}
        />
      )}

      {getIn(formik?.errors, name) ? (
        <div
          style={{
            color: "#E10000",
            fontSize: "12px",
            marginTop: "4px",
            display: "flex",
            alignItems: "center",
          }}
        >
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            style={{
              width: "14px",
            }}
          >
            <path
              d="M1.83337 8C1.83337 4.59425 4.59429 1.83334 8.00004 1.83334C11.4058 1.83334 14.1667 4.59425 14.1667 8C14.1667 11.4058 11.4058 14.1667 8.00004 14.1667C4.59428 14.1667 1.83337 11.4058 1.83337 8Z"
              fill="white"
            />
            <path
              d="M8.00004 8.66667L8.00004 5.16667M1.83337 8C1.83337 4.59425 4.59429 1.83334 8.00004 1.83334C11.4058 1.83334 14.1667 4.59425 14.1667 8C14.1667 11.4058 11.4058 14.1667 8.00004 14.1667C4.59428 14.1667 1.83337 11.4058 1.83337 8Z"
              stroke="#E10000"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M7.99996 11.25C8.32213 11.25 8.58329 10.9888 8.58329 10.6667C8.58329 10.3445 8.32213 10.0833 7.99996 10.0833C7.67779 10.0833 7.41663 10.3445 7.41663 10.6667C7.41663 10.9888 7.67779 11.25 7.99996 11.25Z"
              fill="#E10000"
              stroke="#E10000"
              stroke-width="0.5"
            />
          </svg>
          &nbsp;
          {getIn(formik?.errors, name)}
        </div>
      ) : hint ? (
        <div
          style={{
            color: "#818898",
            fontSize: "12px",
            marginTop: "4px",
          }}
        >
          {hint}
        </div>
      ) : (
        ""
      )}
    </div>
  );
}

const Label = styled.label`
  font-size: 14px;
  font-weight: 300;
  color: #36394a;
  font-weight: 400;
  display: block;
  width: fit-content;
`;
const AppInputStyle = styled.div`
  outline: none;
  position: relative;
  border-radius: 8px;
  background-color: white;
  padding: 0;
  margin-top: 10px;
  position: relative;
  width: 100%;
  appearance: none;
  border-radius: 10px;
  outline: none;
  border: none;
  background: none;
  font-size: 16px;
  height: 140px;
  input::placeholder {
    color: #a4acb9;
    font-size: 16px;
  }
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    display: none;
  }
  @media screen and (max-width: 750px) {
    input {
      width: 100% !important;
    }
  }
`;
