import WalletGreenIcon from "@/assets/icons/WalletGreenIcon";
import { FormatCurrency } from "@/lib/utils";
import { Card, Flex } from "@radix-ui/themes";
import styled from "styled-components";

export default function BalanceCard({
  amount = 106000000.21,
  formattedAmount,
  title = "Total Revenue",
  Icon = WalletGreenIcon,
}) {
  return (
    <Card
      style={{
        width: "100%",
        padding: "20px ",
      }}
    >
      <Style>
        <Flex justify="between">
          <Flex>
            <div>
              <div
                style={{
                  color: "#131313",
                }}
                className="dText"
              >
                {title}
              </div>

              <h2
                style={{
                  color: "#333B4A",
                  fontWeight: "600",
                  fontSize: "20px",
                  marginTop: "10px",
                }}
              >
                {formattedAmount
                  ? formattedAmount
                  : FormatCurrency(amount, "ngn")}
              </h2>
            </div>
          </Flex>

          <Icon />
        </Flex>
      </Style>
    </Card>
  );
}

const Style = styled.div`
  .dText {
    font-size: 15px;
  }
`;
