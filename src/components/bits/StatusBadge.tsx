import { Badge } from "@radix-ui/themes";
import React, { FC } from "react";
import styled from "styled-components";

type Props = {
  variant?: "default" | "other";
  status?:
    | "completed"
    | "received"
    | "success"
    | "pending"
    | "verified"
    | "deposited"
    | "accepted"
    | "active"
    | "cancelled"
    | "approved"
    | "inactive"
    | "declined"
    | "not verified"
    | "sent"
    | "suspended"
    | "on-hold"
    | "false"
    | "true"
    | "debit"
    | "credit";
};

const StyledBadge = styled(Badge)<{ $color: string; $colorBorder: string }>`
  background-color: ${(props) => props.$color}13;
  color: ${(props) => props.$color};
  border: 1px solid ${(props) => props.$colorBorder};
  padding: 10px;
  font-size: 14px;
  font-weight: 500;
  text-transform: capitalize;
  border-radius: 6px;
`;

const StyledBadgeOther = styled(Badge)<{ $color: string }>`
  background: none !important;
  color: ${(props) => props.$color};
  padding: 0px;
  font-size: 14px;
  font-weight: 500;
  text-transform: capitalize;
  border-radius: 6px;
`;

export const StatusBadge: FC<Props> = ({ status, variant = "default" }) => {
  const statusColors = {
    completed: "#027A48",
    credit: "#027A48",
    low: "#027A48",
    received: "#027A48",
    success: "#027A48",
    successful: "#027A48",
    approved: "#027A48",
    verified: "#027A48",
    deposited: "#027A48",
    active: "#027A48",
    accepted: "#027A48",
    pending: "#DC6803",
    medium: "#DC6803",
    cancelled: "#B42318",
    failed: "#B42318",
    inactive: "#B42318",
    declined: "#B42318",
    suspended: "#B42318",
    false: "#B42318",
    debit: "#B42318",
    high: "#B42318",
    true: "#027A48",
    "not verified": "#B42318",
    sent: "#101828",
  };

  const statusColorsBorder = {
    completed: "#32D583",
    credit: "#32D583",
    low: "#32D583",
    received: "#32D583",
    success: "#32D583",
    successful: "#32D583",
    approved: "#32D583",
    verified: "#32D583",
    deposited: "#32D583",
    active: "#32D583",
    accepted: "#32D583",
    pending: "#FDB022",
    medium: "#FDB022",
    cancelled: "#F04438",
    failed: "#F04438",
    inactive: "#F04438",
    declined: "#F04438",
    suspended: "#F04438",
    false: "#F04438",
    high: "#F04438",
    debit: "#F04438",
    true: "#32D583",
    "not verified": "#F04438",
    sent: "#D0D5DD",
  };
  return variant === "default" ? (
    <StyledBadge
      $color={statusColors[status?.toLocaleLowerCase()] || "#757575"}
      $colorBorder={
        statusColorsBorder[status?.toLocaleLowerCase()] || "#757575"
      }
    >
      {status?.charAt(0).toLocaleUpperCase()}
      {status?.slice(1, status.length).toLocaleLowerCase()}
    </StyledBadge>
  ) : (
    <StyledBadgeOther
      $color={statusColors[status.toLocaleLowerCase()] || "#757575"}
    >
      <div
        style={{
          borderRadius: "50%",
          width: "6px",
          height: "6px",
          backgroundColor:
            statusColors[status?.toLocaleLowerCase()] || "#757575",
        }}
      ></div>
      {status?.charAt(0).toLocaleUpperCase()}
      {status?.slice(1, status.length).toLocaleLowerCase()}
    </StyledBadgeOther>
  );
};
