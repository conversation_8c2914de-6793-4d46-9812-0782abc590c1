import { EyeIcon, EyeOffIcon } from "lucide-react";
import { useState } from "react";
import styled from "styled-components";

export default function FormInputPassword({
  name,
  placeholder,
  defaultValue,
  label,
  disabled,
  padding = "12px",
  width = "100%",
  bottom = "0px",
  labelColor = "#000",
  background = "#F6F8FA",
  required = false,
  formik,
  max,
  IconLeft,
  hint,
}) {
  const [type, setType] = useState("password");

  return (
    <div
      style={{
        width: "100%",

        marginBottom: "12px",
      }}
    >
      <Label
        style={{
          color: labelColor,
        }}
        htmlFor={name}
      >
        {label}
      </Label>
      <AppInputStyle
        style={{
          border: formik?.errors?.[name]
            ? "2px solid #E10000"
            : "2px solid #ECEFF3",
          width: width,
          marginBottom: bottom,
          background: background,
        }}
      >
        <div
          style={{
            display: "flex",
            alignItems: "center",
          }}
        >
          {IconLeft && (
            <div
              style={{
                paddingLeft: padding,
                transform: "translateY(10%)",
              }}
            >
              <IconLeft width="20px" height="20px" />
            </div>
          )}

          <input
            type={type}
            name={name}
            required={required}
            defaultValue={defaultValue}
            value={formik?.values?.[name]}
            onChange={formik?.handleChange}
            placeholder={placeholder}
            disabled={disabled}
            style={{
              paddingTop: padding,
              paddingBottom: padding,
              paddingRight: padding,
              paddingLeft: padding,
            }}
            maxLength={max}
          />

          <div
            style={{
              paddingRight: padding,
              transform: "translateY(10%)",
            }}
          >
            {type === "text" ? (
              <EyeOffIcon
                onClick={() => {
                  setType(type === "password" ? "text" : "password");
                }}
                width="20px"
                height="20px"
              />
            ) : (
              <EyeIcon
                onClick={() => {
                  setType(type === "password" ? "text" : "password");
                }}
                width="20px"
                height="20px"
              />
            )}
          </div>
        </div>
      </AppInputStyle>
      {formik?.errors?.[name] ? (
        <div
          style={{
            color: "#E10000",
            fontSize: "12px",
            marginTop: "4px",
            display: "flex",
            alignItems: "center",
          }}
        >
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            style={{
              width: "14px",
            }}
          >
            <path
              d="M1.83337 8C1.83337 4.59425 4.59429 1.83334 8.00004 1.83334C11.4058 1.83334 14.1667 4.59425 14.1667 8C14.1667 11.4058 11.4058 14.1667 8.00004 14.1667C4.59428 14.1667 1.83337 11.4058 1.83337 8Z"
              fill="white"
            />
            <path
              d="M8.00004 8.66667L8.00004 5.16667M1.83337 8C1.83337 4.59425 4.59429 1.83334 8.00004 1.83334C11.4058 1.83334 14.1667 4.59425 14.1667 8C14.1667 11.4058 11.4058 14.1667 8.00004 14.1667C4.59428 14.1667 1.83337 11.4058 1.83337 8Z"
              stroke="#E10000"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M7.99996 11.25C8.32213 11.25 8.58329 10.9888 8.58329 10.6667C8.58329 10.3445 8.32213 10.0833 7.99996 10.0833C7.67779 10.0833 7.41663 10.3445 7.41663 10.6667C7.41663 10.9888 7.67779 11.25 7.99996 11.25Z"
              fill="#E10000"
              stroke="#E10000"
              stroke-width="0.5"
            />
          </svg>
          &nbsp;
          {formik?.errors?.[name]}
        </div>
      ) : hint ? (
        <div
          style={{
            color: "#818898",
            fontSize: "12px",
            marginTop: "4px",
          }}
        >
          {hint}
        </div>
      ) : (
        ""
      )}
    </div>
  );
}

const Label = styled.label`
  font-size: 14px;
  font-weight: 300;
  color: #36394a;
  font-weight: 400;
  display: block;
  width: fit-content;
`;
const AppInputStyle = styled.div`
  outline: none;
  position: relative;
  border-radius: 8px;
  background-color: white;
  padding: 0;
  margin-top: 10px;
  input {
    width: 100%;
    height: 100%;
    appearance: none;
    border-radius: 6px;
    outline: none;
    border: none;
    background: none;
    font-size: 16px;
  }
  input::placeholder {
    color: #a4acb9;
    font-size: 16px;
  }
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    display: none;
  }

  @media screen and (max-width: 750px) {
    input {
      width: 100% !important;
    }
  }
`;
