import { Card, Flex } from "@radix-ui/themes";

export default function RadioCard({ name, subName, active }) {
  return (
    <Flex align={"center"} gap={"4"}>
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        style={{
          opacity: active ? 1 : 0,
        }}
      >
        <rect x="0.5" y="0.5" width="15" height="15" rx="3.5" fill="white" />
        <rect
          x="0.5"
          y="0.5"
          width="15"
          height="15"
          rx="3.5"
          stroke="#FF6500"
        />
        <path
          d="M12.5 5.49992L6.5 11.4999L3.75 8.74992L4.455 8.04492L6.5 10.0849L11.795 4.79492L12.5 5.49992Z"
          fill="#FF6500"
        />
      </svg>

      <Card
        style={{
          padding: "20px 24px",
          borderRadius: "12px",
          border: active ? "1px solid #FF6500 " : "1px solid #EBEBEB",
          width: "100%",
          overflow: "hidden",
        }}
      >
        <Flex justify={"between"} align={"center"} width="100%">
          <Flex direction="column" width="100%">
            <div
              style={{
                color: "#333333",
                fontSize: "14px",
              }}
            >
              {name}
            </div>
            <div
              style={{
                color: "#71717A",
                fontSize: "12px",
              }}
            >
              {subName}
            </div>
          </Flex>

          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10.7219 8.91317C11.2051 8.39977 11.2051 7.59893 10.7219 7.08553L5.81864 1.87577C5.56629 1.60766 5.14438 1.59487 4.87626 1.84722C4.60815 2.09956 4.59536 2.52148 4.84771 2.78959L9.75101 7.99935L4.84771 13.2091C4.59536 13.4772 4.60815 13.8991 4.87626 14.1515C5.14438 14.4038 5.5663 14.391 5.81864 14.1229L10.7219 8.91317Z"
              fill="#71717A"
            />
          </svg>
        </Flex>
      </Card>
    </Flex>
  );
}
