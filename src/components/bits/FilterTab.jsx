import { useNavigate, useSearchParams } from "react-router-dom";
import styled from "styled-components";

export default function FilterTab({ tab }) {
  const [query] = useSearchParams();
  const navigate = useNavigate();

  const addQueryParams = (query) => {
    const params = new URLSearchParams(location.search);

    if (params.has("tab2")) {
      params.set("tab", query); // Update if exists
    } else {
      params.set("tab", query); // Add if not exists
    }

    navigate({
      search: params.toString(),
    });
  };

  const active = query.get("tab");
  return (
    <MiniNavStyle>
      <div
        style={{
          background: "1px solid #fff",
          overflow: "hidden",
          display: "flex",
          borderBottom: "1px solid #ededed",
        }}
        className="cont"
      >
        {tab?.map((item) => {
          return (
            <div
              onClick={() => {
                addQueryParams(item?.tab);
              }}
              style={{
                padding: "10px 16px",
                borderBottom:
                  active !== item?.tab
                    ? "1px solid #ededed"
                    : "1px solid #FF6500",
                fontSize: "15px",
                cursor: "pointer",
              }}
              className="tab"
            >
              <span
                style={{
                  width: "100%",
                  color: active !== item?.tab ? "#667085" : "#FF6500",
                }}
              >
                {item?.name}
              </span>
            </div>
          );
        })}
      </div>
    </MiniNavStyle>
  );
}

const MiniNavStyle = styled.div`
  @media screen and (max-width: 1250px) {
    .tab {
      font-size: 14px !important;
    }
  }
  @media screen and (max-width: 650px) {
    .cont {
      width: 100% !important;
      display: grid !important;
      grid-template-columns: 1fr 1fr !important;
      grid-gap: 10px;
      border: none !important;
      border-radius: 0px;
      background: none !important;
    }
    .tab {
      font-size: 11px !important;
      padding: 6px 2px !important;
      width: 100% !important;
      text-align: center;
      border: 1px solid #eaecf0;
      background: 1px solid #eaecf0;
      border-radius: 10px;
    }
  }
`;
