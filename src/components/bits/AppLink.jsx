import { Link } from "react-router-dom";

export default function AppLink({
  to,
  children,
  type = "link",
  color = "#417CD4",
  onClick = () => null,
}) {
  return type === "link" ? (
    <Link to={to}>
      <div style={{ color: "#417CD4" }}>{children}</div>
    </Link>
  ) : (
    <div
      style={{
        color: color,
        cursor: "pointer",
        width: "fit-content",
        display: "flex",
        alignItems: "center",
        gap: 4,
      }}
      onClick={onClick}
    >
      {children}
    </div>
  );
}
