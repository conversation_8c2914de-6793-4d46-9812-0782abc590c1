import styled from "styled-components";

export default function Box({
  children,
  padding = "24px",
  background = "#fff",
  style,
  onClick = () => {},
}) {
  return (
    <StyleBox onClick={onClick}>
      <div
        style={{
          width: "100%",
          padding: padding,
          background: background,
          overflow: "auto",
          borderRadius: "12px",
          boxShadow: "0px 1px 3px rgba(0, 0, 0, 0.08)",
          display: "flex",
          flexDirection: "column",
          ...style,
        }}
        className="box"
      >
        {children}
      </div>
    </StyleBox>
  );
}

const StyleBox = styled.div`
  @media screen and (max-width: 750px) {
    .box {
      padding: 10px !important;
    }
  }
`;
