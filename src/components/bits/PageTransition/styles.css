/* 
.fade-enter {
  opacity: 0;
  transform: scale(1.1);
}

.fade-enter-active {
  opacity: 1;
  transform: scale(1);
  transition: opacity 300ms, transform 300ms;
}

.fade-exit {
  opacity: 1;
  transform: scale(1);
}

.fade-exit-active {
  opacity: 0;
  transform: scale(0.9);
  transition: opacity 300ms, transform 300ms;
} */

/* .fade-enter {
  opacity: 0;
  transform: scale(1.1);
}
.fade-enter-active {
  opacity: 1;
  transform: scale(1);
}
.fade-exit {
  opacity: 0;
  transform: scale(1);
}
.fade-exit-active {
  opacity: 0;
  transform: scale(1.1);
}
.fade-enter-active,
.fade-exit-active {
  transition:all 600ms;
} */



/* .fade-enter {
  opacity: 0;
  transform: translateY(10%);
}
.fade-enter-active {
  opacity: 1;
  transform: translateY(0%);
}
.fade-exit {
  opacity: 1;
  transform: translateY(0%);
}
.fade-exit-active {
  opacity: 0;
  transform: translateY(10%);
}
.fade-enter-active,
.fade-exit-active {
  transition: all 360ms ease-out;
} */


/* .fade {
  position: absolute;
  left: 15px;
  right: 15px;
}

.fade-enter {
  opacity: 0;
  transform: scale(1.1);
}

.fade-enter-active {
  opacity: 1;
  transform: scale(1);
  transition: opacity 300ms, transform 300ms;
}

.fade-exit {
  opacity: 1;
  transform: scale(1);
}

.fade-exit-active {
  opacity: 0;
  transform: scale(0.9);
  transition: opacity 300ms, transform 300ms;
} */


.fade-enter {
  opacity: 0;
  transform: translateX(-100%);
}
.fade-enter-active {
  opacity: 1;
  transform: translateX(0%);
}
.fade-exit {
  opacity: 1;
  transform: translateX(0%);
}
.fade-exit-active {
  opacity: 0;
  transform: translateX(100%);
}
.fade-enter-active,
.fade-exit-active {
  transition: opacity 300ms, transform 300ms;
}