import { Flex } from "@radix-ui/themes";
import React, { FC } from "react";
import CurrencyFlagImage from "react-currency-flags";

type Props = {
  currency?: string;
  name?: string;
  fontSize?: string;
  color?: string;
};
export const CurrencyBadge: FC<Props> = ({
  currency,
  name,
  fontSize = "12px",
  color = "#5a6376",
}) => {
  return (
    <Flex className="countryName" gap={"2"} align={"center"}>
      <div
        style={{
          height: "20px",
          width: "20px",
          display: "grid",
          placeItems: "center",
          border: "1px solid #cecece",
          borderRadius: "999px",
          background: "#fff",
          overflow: "hidden",
        }}
      >
        <CurrencyFlagImage
          currency={currency ? currency : ""}
          style={{ width: "18px", height: "18px" }}
          size="sm"
        />
      </div>
      <span style={{ fontSize: fontSize, color: color }}>
        {name ? name : currency}
      </span>
    </Flex>
  );
};
