import styled from "styled-components";
import AppButton from "./AppButton";
import { CalendarIcon } from "lucide-react";
import { useRef, useState } from "react";
import { Calendar } from "@arco-design/web-react";
import moment from "moment";
import { getIn } from "formik";
//import { checkedMatureDate } from "@/lib/utils";

export default function FormInputDate({
  name,
  placeholder,
  label,
  disabled,
  padding = "13.958px",
  width = "100%",
  labelColor = "#000",
  formik,
  onChange,
  disabledDate,
  position = "right",
}) {
  const [showCalendar, setShowCalendar] = useState(false);
  const [date, setDate] = useState("");

  const catMenu = useRef(null);

  const closeOpenMenus = (e) => {
    if (showCalendar && !catMenu.current?.contains(e.target)) {
      setShowCalendar(false);
    }
  };

  document.addEventListener("mousedown", closeOpenMenus);

  return (
    <div>
      {label && (
        <Label
          style={{
            color: labelColor,
          }}
          htmlFor={name}
        >
          {label}
        </Label>
      )}

      <div
        style={{
          position: "relative",
        }}
      >
        <AppButton
          placeholder={getIn(formik?.values, name) || date || placeholder}
          width={width}
          borderColor={getIn(formik?.errors, name) ? " #E10000" : " #d5d8dd"}
          outline
          radius={"8px"}
          style={{
            padding: padding,
          }}
          disabled={disabled}
          IconLeft={CalendarIcon}
          onClick={() => {
            setShowCalendar(!showCalendar);
          }}
        />

        {showCalendar && (
          <div ref={catMenu}>
            <Calendar
              style={{
                top: "130%",
                right: position === "right" && 0,
                left: position === "left" && 0,
              }}
              panel
              panelTodayBtn
              allowSelect={true}
              headerType="select"
              className={"calendar"}
              disabledDate={disabledDate}
              value={moment(
                getIn(formik?.values, name) || date || new Date()
              )?.format("YYYY-MM-DD")}
              onChange={(date) => {
                onChange && onChange(date);
                const newDate = moment(date?.toString())?.format("DD MMM YYYY");
                console.log(moment(newDate)?.format("DD-MM-YYYY"), "ddd");
                setDate(newDate);
                formik?.setFieldValue(
                  name,
                  moment(newDate).format("YYYY-MM-DD")
                );
                setShowCalendar(false);
              }}
            />
          </div>
        )}
      </div>
    </div>
  );
}

const Label = styled.label`
  font-size: 14px;
  font-weight: 300;
  color: #36394a;
  font-weight: 400;
  display: block;
  width: fit-content;
  margin-bottom: 10px;
`;
