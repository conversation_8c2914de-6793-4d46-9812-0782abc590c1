import ArrowDownIcon2 from "@/assets/icons/bit-icons/ArrowDownIcon2";
//import MainContext from "@/context/global.context";
import { Avatar, Flex } from "@radix-ui/themes";
import { ArrowRightCircle, LogOutIcon } from "lucide-react";
import { /* useContext, */ useState } from "react";
import { Link, useLocation } from "react-router-dom";
import styled from "styled-components";
import { MenuItem, MenuList } from "./DropDownMenu";
import ConfirmModal from "@/Modals/ConfirmModal";
import { useAuth } from "@/context/global.context";

export default function TopBar({ onClick, width }) {
  const { pathname } = useLocation();
  const { user_data } = useAuth();

  const [open, setOpen] = useState(false);

  return (
    <>
      {width <= 1280 && (
        <ArrowRightCircle
          onClick={onClick}
          width="24px"
          height="24px"
          style={{
            marginLeft: "18px",
            marginTop: "20px",
            position: "relative",
            zIndex: 1,
            cursor: "pointer",
          }}
        />
      )}
      <ConfirmModal
        title="Logout"
        description="Are you sure you want to logout?"
        open={open}
        confirm={() => {
          localStorage.clear();
          window.location.replace("/");
        }}
        cancel={() => {
          setOpen(false);
        }}
      />
      <Style>
        <Flex gap={{ initial: "2", md: "5" }} justify="center" align="center">
          <Link to="/dashboard">
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              style={{
                transform: "translateY(10%)",
              }}
            >
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M9.52975 1.58996C8.61157 0.947228 7.38947 0.947227 6.47129 1.58996L1.88624 4.79949C1.11382 5.34018 0.684342 6.24856 0.756656 7.18864L1.22667 13.2988C1.31531 14.4512 2.35945 15.2888 3.50362 15.1253L5.6167 14.8234C6.60199 14.6827 7.33385 13.8388 7.33385 12.8435V11.9999C7.33385 11.6317 7.63233 11.3333 8.00052 11.3333C8.36871 11.3333 8.66719 11.6317 8.66719 11.9999V12.8435C8.66719 13.8388 9.39904 14.6827 10.3843 14.8234L12.4974 15.1253C13.6416 15.2888 14.6857 14.4512 14.7744 13.2988L15.2444 7.18864C15.3167 6.24857 14.8872 5.34018 14.1148 4.79949L9.52975 1.58996ZM7.2359 2.68227C7.695 2.3609 8.30604 2.3609 8.76514 2.68227L13.3502 5.8918C13.7364 6.16215 13.9511 6.61634 13.915 7.08638L13.445 13.1965C13.4154 13.5807 13.0674 13.8599 12.686 13.8054L10.5729 13.5035C10.2445 13.4566 10.0005 13.1753 10.0005 12.8435V11.9999C10.0005 10.8954 9.10509 9.99993 8.00052 9.99993C6.89595 9.99993 6.00052 10.8954 6.00052 11.9999V12.8435C6.00052 13.1753 5.75657 13.4566 5.42813 13.5035L3.31506 13.8054C2.93367 13.8599 2.58562 13.5807 2.55607 13.1965L2.08606 7.08638C2.0499 6.61634 2.26465 6.16215 2.65085 5.8918L7.2359 2.68227Z"
                fill="#667185"
              />
            </svg>
          </Link>
          <svg
            width="8"
            height="14"
            viewBox="0 0 8 14"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M6.72218 7.9139C7.20538 7.4005 7.20538 6.59966 6.72218 6.08626L1.81888 0.876507C1.56654 0.608391 1.14462 0.595606 0.876507 0.84795C0.608391 1.10029 0.595606 1.52221 0.84795 1.79033L5.75125 7.00008L0.84795 12.2098C0.595606 12.478 0.608392 12.8999 0.876507 13.1522C1.14462 13.4046 1.56654 13.3918 1.81888 13.1237L6.72218 7.9139Z"
              fill="#D0D5DD"
            />
          </svg>
          <div className="route">
            {pathname === "/"
              ? "Dashboard"
              : pathname
                  ?.replace("/", "")
                  ?.replace("-", " ")
                  ?.replace("-", " ")
                  ?.replace("%20", " ")
                  ?.replace("/", " - ")}
          </div>
        </Flex>
        <Flex align={"center"} gap={"3"}>
          <Avatar
            size={"3"}
            fallback={`${user_data?.firstName?.slice(0, 1)}${
              user_data?.surName?.slice(0, 1) ||
              user_data?.username?.slice(0, 1)
            }`}
            radius="full"
          />
          <div>
            <div className="hard">{user_data?.firstName}</div>
            <div className="light">
              {user_data?.surName || user_data?.username}
            </div>
          </div>
          <MenuList iconWidth="13px" ActionIcon={ArrowDownIcon2}>
            <MenuItem
              name={"Logout"}
              Icon={LogOutIcon}
              action={() => {
                setOpen(true);
              }}
              width={"150px"}
            />
            {/* <MenuItem
              name={"Settings"}
              Icon={Settings2}
              to={"/system-settings?tab=profile"}
              width={"150px"}
            /> */}
          </MenuList>
        </Flex>
      </Style>
    </>
  );
}

const Style = styled.div`
  padding: 0 20px;
  height: 8vh;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .verify-box {
    background-color: #f044380d;
    padding: 8px;
    font-size: 13px;
  }
  .live_mode {
    color: #f04438;
  }
  .hard {
    color: #121212;
    font-size: 14px;
  }
  .light {
    color: #12121299;
    font-size: 14px;
  }
  .route {
    text-transform: capitalize;
  }
  @media screen and (max-width: 1280px) {
    height: fit-content;
  }
  @media screen and (max-width: 1028px) {
    flex-direction: column-reverse;
    align-items: flex-start;
    margin-top: 20px;
    gap: 20px;
    .verify-box {
    }
    .live_mode {
    }
  }
`;
