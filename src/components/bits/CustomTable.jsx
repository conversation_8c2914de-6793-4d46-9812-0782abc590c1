import { Table } from "@arco-design/web-react";
import "@arco-design/web-react/dist/css/arco.css";

import styled from "styled-components";
//import { Flex, Skeleton } from "@radix-ui/themes";
import useScreenSize from "@/lib/useScreenSize";
import PaginationComponent from "./Pagination";

const CustomTable = ({
  Apidata,
  tableColumns,
  loading,
  noData,
  topContent,
  pagination = false,
  scroll,
  arrayData,
  setPage,
  total,
  currentPage,
  tableWidth,
  directPagination = true,
  rowClassName, // <-- add this prop
}) => {
  const columns = [
    {
      title: "",
      dataIndex: "",
    },
  ];
  const data = [];
  const { width } = useScreenSize();
  return (
    <>
      <Content>
        {topContent && <div className="top">{topContent} </div>}

        <div
          style={{
            overflow: "hidden",
            width: tableWidth
              ? tableWidth - 372
              : width <= 1280
              ? width - 31
              : width - 372,
          }}
        >
          {/*  {loading ? (
            <Flex direction={"column"} gap={"3"} style={{ padding: "30px" }}>
              <Skeleton height={"3vh"} loading />
              <Skeleton height={"3vh"} loading />
              <Skeleton height={"3vh"} loading />
              <Skeleton height={"3vh"} loading />
              <Skeleton height={"3vh"} loading />
              <Skeleton height={"3vh"} loading />
              <Skeleton height={"3vh"} loading />
              <Skeleton height={"3vh"} loading />
              <Skeleton height={"3vh"} loading />
              <Skeleton height={"3vh"} loading />
              <Skeleton height={"3vh"} loading />
              <Skeleton height={"3vh"} loading />
              <Skeleton height={"3vh"} loading />
              <Skeleton height={"3vh"} loading />
              <Skeleton height={"3vh"} loading />
              <Skeleton height={"3vh"} loading />
            </Flex>
          ) : (
            <Table
              loading={loading}
              scroll={
                scroll
                  ? scroll
                  : {
                      x: "100%",
                    }
              }
              noDataElement={noData}
              columns={tableColumns || columns}
              data={arrayData || Apidata?.data?.data || data}
              className="table3"
              pagination={directPagination}
              stripe={true}
           
              style={{
                padding: "12px 0",
              }}
            />
          )} */}
          <Table
            loading={loading}
            scroll={
              scroll
                ? scroll
                : {
                    x: "100%",
                  }
            }
            noDataElement={noData}
            columns={tableColumns || columns}
            data={arrayData || Apidata?.data?.data || data}
            className="table3"
            pagination={directPagination}
            stripe={true}
            rowClassName={rowClassName} // <-- pass to Table
            /*   pagination={
                pagination && {
                  showTotal: true,
                  total: Apidata?.length || data?.length,
                  pageSize: 50,
                  pageSizeChangeResetCurrent: true,
                }
              } */
            style={{
              padding: "12px 0",
            }}
          />
        </div>
      </Content>
      {pagination && (
        <PaginationComponent
          total={total || Apidata?.data?.meta?.totalCount}
          current={currentPage || Apidata?.data?.meta?.currentPage}
          pageSize={10}
          onChange={(data) => {
            setPage(data);
          }}
        />
      )}
    </>
  );
};

export default CustomTable;
const Content = styled.div`
  border-radius: 10px;
  border: 1px solid #eaecf0;
  //border: 1px solid #000000;
  background-color: #ffffff;
  overflow: hidden;
  width: 100%;
  min-height: 420px;

  .arco-table-tr:nth-child(odd) {
    background-color: #f9fafb;
  }
  .top {
    padding: 16px;
    position: relative;
    z-index: 100;
  }

  .table3 {
    overflow: hidden;
    //border: 1px solid #eaecf0;

    th {
      padding: 4px 0;
      font-size: 12px;
      text-transform: uppercase;
      background-color: #f9fafb;
      border-radius: 0px !important;
      font-family: "Fira Sans" !important;
      border: 1px solid #f9fafb !important;

      .arco-table-th-item-title {
        color: #667085 !important;
      }
    }
    td {
      padding-top: 20px;
      padding-bottom: 20px;
      font-family: "Fira Sans" !important;
      font-size: 13px;

      .arco-table-cell-wrap-value {
        color: #5a6376 !important;
      }
    }
  }
`;
