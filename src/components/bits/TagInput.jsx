import { InputTag } from "@arco-design/web-react";
import styled from "styled-components";

export default function TagInput({
  name,
  onChange,
  placeholder,
  value,
  label,
  padding = "6px",
  width = "100%",
  bottom = "20px",
  labelColor = "#000",
  background,
}) {
  return (
    <div style={{ width: "100%" }}>
      <Label
        style={{
          color: labelColor,
        }}
        htmlFor={name}
      >
        {label}
      </Label>
      <AppInputStyle
        style={{
          border: "1px solid #D0D5DD",
          width: width,
          marginBottom: bottom,
          background: background,
        }}
      >
        <div>
          <InputTag
            value={value}
            onChange={onChange}
            className="input"
            placeholder={placeholder}
            style={{ padding: padding }}
          />
        </div>
      </AppInputStyle>
    </div>
  );
}

const Label = styled.label`
  font-size: 16px;
  color: #344054;
  font-weight: 400;
`;
const AppInputStyle = styled.div`
  outline: none;
  border-radius: 6px;
  background-color: white;
  padding: 0;
  margin-top: 10px;

  .input {
    width: 100%;
    height: 100%;
    appearance: none;
    border-radius: 6px;
    outline: none;
    border: none;
    background: none;
    font-size: 16px;
  }
  input::placeholder {
    color: #ababab;
  }
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    display: none;
  }
`;
