import { Flex } from "@radix-ui/themes";
import { useNavigate, useSearchParams } from "react-router-dom";
import styled from "styled-components";

export default function MiniNavBar({ tab }) {
  const [query] = useSearchParams();
  const navigate = useNavigate();

  const addQueryParams = (query) => {
    const params = new URLSearchParams(location.search);

    if (params.has("filterss") || params.has("filter") || params.has("tab2")) {
      params.set("tab", query); // Update if exists
    } else {
      params.set("tab", query); // Add if not exists
    }

    navigate({
      search: params.toString(),
    });
  };

  const active = query.get("tab");
  return (
    <MiniNavStyle>
      <div
        style={{
          borderBottom: "1px solid #EAECF0",
          background: "1px solid #EAECF0",
          overflow: "hidden",
          display: "flex",
        }}
        className="cont"
      >
        {tab?.map((item) => {
          return (
            <div
              onClick={() => {
                addQueryParams(item?.tab);
              }}
              style={{
                background: "#fff",
                fontSize: "14px",
                cursor: "pointer",
                borderBottom:
                  active === item?.tab
                    ? "2px solid #FF6500"
                    : "2px solid transparent",
              }}
              className="tab"
            >
              <Flex
                gap={"2"}
                align={"center"}
                style={{
                  width: "100%",
                  color: active === item?.tab ? "#FF6500" : "#667085",
                  fontWeight: "600",
                }}
              >
                {item?.name}
                {item?.count && (
                  <span
                    style={{
                      color: active === item?.tab ? "#FF6500" : "#667085",
                      background: active === item?.tab ? "#FCE9EC" : "#F2F4F7",
                      fontWeight: "600",
                      fontSize: "12px",
                      padding: "5px 6px",
                      borderRadius: "999px",
                    }}
                  >
                    {item?.count || 100}
                  </span>
                )}
              </Flex>
            </div>
          );
        })}
      </div>
    </MiniNavStyle>
  );
}

const MiniNavStyle = styled.div`
  .tab {
    padding: 13px 8px;
  }
  @media screen and (max-width: 1250px) {
    .tab {
      font-size: 14px !important;
      padding: 15px 10px;
    }
  }
  @media screen and (max-width: 750px) {
    .cont {
      width: 100% !important;
      display: grid !important;
      grid-template-columns: 1fr 1fr !important;
      border: none !important;
      border-radius: 0px;
      background: none !important;
    }
    .tab {
      font-size: 11px !important;
      padding: 6px 2px !important;
      width: 100% !important;
      text-align: center;
      border: 1px solid #eaecf0;
      background: 1px solid #eaecf0;
    }
  }
`;
