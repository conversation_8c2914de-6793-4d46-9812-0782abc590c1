import styled from "styled-components";
import Select from "react-select";
import { getIn } from "formik";

export default function MainSelect({
  label,
  options,
  defaultValue,
  disabled,
  name,
  padding = "4px",
  formik,
  width = "100%",
  optionValue = "value",
  hint,
  placeholder,
  elementValue,
  cutBorder,
  background = "#F6F8FA",
  isSearchable,
  onChange,
  value,
  marginBottom = "12px",
  showError = true,
  mainValue,
}) {
  const customStyles = {
    control: (base) => ({
      ...base,
      background: background,

      borderRight:
        cutBorder && getIn(formik?.errors, name)
          ? "2px solid transparent"
          : "2px solid transparent",
      borderLeft:
        cutBorder && getIn(formik?.errors, name)
          ? "2px solid #E10000"
          : "2px solid #ECEFF3",
      borderTop:
        cutBorder && getIn(formik?.errors, name)
          ? "2px solid #E10000"
          : "2px solid #ECEFF3",
      borderBottom:
        cutBorder && getIn(formik?.errors, name)
          ? "2px solid #E10000"
          : "2px solid #ECEFF3",
      border: cutBorder
        ? ""
        : getIn(formik?.errors, name)
        ? "2px solid #E10000"
        : "2px solid #ECEFF3",
      boxShadow: "none",
      borderRadius: cutBorder ? "8px 0px 0px 8px" : "8px",
      padding: padding,

      "&:hover": {
        border: "2px solid #f9a7022b",
      }, // You can also use state.isFocused to conditionally style based on the focus state
    }),
  };
  return (
    <NewSelect
      style={{
        width: width,
        marginBottom: marginBottom,
      }}
      className="name"
    >
      {label && <Label htmlFor={name}>{label}</Label>}
      <Select
        value={
          value
            ? value
            : mainValue
            ? options?.find(
                (item) =>
                  item?.[optionValue]?.toString() === mainValue?.toString()
              )
            : options?.find(
                (item) => item?.[optionValue] === getIn(formik?.values, name)
              )
        }
        isSearchable={isSearchable}
        //onChange={onChange}
        placeholder={placeholder}
        options={options}
        defaultValue={defaultValue}
        isDisabled={disabled}
        theme={(theme) => ({
          ...theme,
          borderRadius: 0,
          colors: {
            ...theme.colors,
            primary25: "#f9a7022b",
            primary: "#f9a702",
          },
        })}
        onChange={(item) => {
          formik?.setFieldValue(name, item?.[optionValue]);

          onChange && onChange(item);
        }}
        getOptionLabel={
          elementValue
            ? elementValue
            : (provider) => (
                <span className="countryName" style={{ fontSize: "16px" }}>
                  {provider.label}
                </span>
              )
        }
        styles={{
          option: (styles) => ({
            ...styles,
            display: "flex",
            alignItems: "center",
            color: "#000",
            width: "100%",
            fontSize: "30px",
            padding: padding,
            position: "relative",
            zIndex: "4",
            "&:hover": {
              background: "#f9a7022b",
            },
            //   border:"0.1px solid #d8d8d8",
            //   backgroundColor:"#e4e4e4",
            //   borerRadius:"18px"
          }),
          menuList: (styles) => ({
            ...styles,
            display: "flex",
            backgroundColor: "#F6F8FA",
            flexDirection: "column",
            // gap:"10px",
            color: "#FFF",

            borerRadius: "18px",
            width: "100%",
            alignItems: "center",
          }),

          singleValue: (styles) => ({
            ...styles,
            display: "flex",
            width: "100%",
            color: "#000",
            alignItems: "center",

            "> svg": {
              marginRight: "8px",
              borderRadius: "50%",
            },
          }),
          ...customStyles,
        }}
      />
      {showError && getIn(formik?.errors, name) ? (
        <div
          style={{
            color: "#E10000",
            fontSize: "12px",
            marginTop: "4px",
            display: "flex",
            alignItems: "center",
          }}
        >
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            style={{
              width: "14px",
            }}
          >
            <path
              d="M1.83337 8C1.83337 4.59425 4.59429 1.83334 8.00004 1.83334C11.4058 1.83334 14.1667 4.59425 14.1667 8C14.1667 11.4058 11.4058 14.1667 8.00004 14.1667C4.59428 14.1667 1.83337 11.4058 1.83337 8Z"
              fill="white"
            />
            <path
              d="M8.00004 8.66667L8.00004 5.16667M1.83337 8C1.83337 4.59425 4.59429 1.83334 8.00004 1.83334C11.4058 1.83334 14.1667 4.59425 14.1667 8C14.1667 11.4058 11.4058 14.1667 8.00004 14.1667C4.59428 14.1667 1.83337 11.4058 1.83337 8Z"
              stroke="#E10000"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M7.99996 11.25C8.32213 11.25 8.58329 10.9888 8.58329 10.6667C8.58329 10.3445 8.32213 10.0833 7.99996 10.0833C7.67779 10.0833 7.41663 10.3445 7.41663 10.6667C7.41663 10.9888 7.67779 11.25 7.99996 11.25Z"
              fill="#E10000"
              stroke="#E10000"
              stroke-width="0.5"
            />
          </svg>
          &nbsp;
          {getIn(formik?.errors, name)}
        </div>
      ) : hint ? (
        <div
          style={{
            color: "#818898",
            fontSize: "12px",
            marginTop: "4px",
          }}
        >
          {hint}
        </div>
      ) : (
        ""
      )}
    </NewSelect>
  );
}
const Label = styled.label`
  font-size: 14px;
  font-weight: 300;
  color: #36394a;
  font-weight: 400;
  display: block;
  width: fit-content;
  margin-bottom: 10px;
`;
const NewSelect = styled.div`
  margin-bottom: 0px;

  .css-kdhqlc-control {
    background-color: #fff !important;
    width: 100%;
  }

  .css-13cymwt-control {
    border-radius: 8px;
    padding: 8px;
  }
  .css-t3ipsp-control {
    border-radius: 8px;
    padding: 8px;
  }
  .css-1u9des2-indicatorSeparator {
    display: none;
  }
  .css-1xc3v61-indicatorContainer .css-1xc3v61-indicatorContainer {
    background: none;
    padding: 0 !important;
    svg path {
      color: #667085;
      stroke: #667085;
    }
  }

  @media screen and (max-width: 750px) {
    width: 100% !important;
  }
`;
