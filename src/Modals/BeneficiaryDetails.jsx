import Modal from "@/components/bits/Modal";
import DividerHorizontal from "@/components/DividerHorizontal";
import { Notification } from "@arco-design/web-react";
import { Flex } from "@radix-ui/themes";
import { Copy, X } from "lucide-react";

export default function BeneficiaryDetails({ setOpen, open, data }) {
  const details = data;
  return (
    <Modal
      dialogBtns={false}
      open={open}
      title=""
      trigger={() => {
        setOpen(!open);
      }}
    >
      <Flex
        justify={"between"}
        align={"center"}
        style={{
          position: "absolute",
          top: "0",
          right: "0",
          padding: "20px",
          width: "100%",
        }}
      >
        <h3 style={{ fontWeight: 500 }}>Beneficiary Bank Details</h3>

        <X
          style={{
            cursor: "pointer",
          }}
          onClick={() => {
            setOpen(false);
          }}
        />
      </Flex>
      <br />
      <div
        style={{
          borderRadius: "8px",
          border: "1px solid #ECEFF3",
          background: "#F8FAFB",
          paddingInline: "20px",
          paddingBlock: "8px",
          marginBottom: "-20px",
        }}
      >
        <Flex justify={"between"} py={"3"}>
          <span
            style={{
              color: "#666D80",
              fontSize: "14px",
            }}
          >
            Bank Name
          </span>
          <span
            style={{
              color: "#36394A",
              fontSize: "14px",
              display: "flex",
              gap: "5px",
              alignItems: "center",
            }}
          >
            {details?.userBeneficiary?.beneficiaryBank?.bankName}{" "}
            <Copy
              size={"12"}
              color="#000"
              onClick={() => {
                navigator.clipboard.writeText(
                  details?.userBeneficiary?.beneficiaryBank?.bankName
                );
                Notification.success({
                  content: "Copied to clipboard",
                });
              }}
            />
          </span>
        </Flex>
        <DividerHorizontal style={{ margin: 0 }} />
        <Flex justify={"between"} py={"3"}>
          <span
            style={{
              color: "#666D80",
              fontSize: "14px",
            }}
          >
            Account Number
          </span>
          <span
            style={{
              color: "#36394A",
              fontSize: "14px",
              display: "flex",
              gap: "5px",
              alignItems: "center",
            }}
          >
            {details?.userBeneficiary?.beneficiaryBank?.accountNumber}
            <Copy
              size={"12"}
              color="#000"
              onClick={() => {
                navigator.clipboard.writeText(
                  details?.userBeneficiary?.beneficiaryBank?.accountNumber
                );
                Notification.success({
                  content: "Copied to clipboard",
                });
              }}
            />
          </span>
        </Flex>
        <DividerHorizontal style={{ margin: 0 }} />
        <Flex justify={"between"} py={"3"}>
          <span
            style={{
              color: "#666D80",
              fontSize: "14px",
            }}
          >
            Account Name
          </span>
          <span
            style={{
              color: "#36394A",
              fontSize: "14px",
              display: "flex",
              gap: "5px",
              alignItems: "center",
            }}
          >
            {details?.userBeneficiary?.beneficiaryBank?.accountName}
            <Copy
              size={"12"}
              color="#000"
              onClick={() => {
                navigator.clipboard.writeText(
                  details?.userBeneficiary?.beneficiaryBank?.accountName
                );
                Notification.success({
                  content: "Copied to clipboard",
                });
              }}
            />
          </span>
        </Flex>
      </div>
    </Modal>
  );
}
