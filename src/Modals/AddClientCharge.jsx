import { useState } from "react";
import AppModal from "./AppModal";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useFormik } from "formik";
import * as Yup from "yup";
import MainSelect from "@/components/bits/MainSelect";
import FormInputNumber from "@/components/bits/FormInputNumber";
import { useAuth } from "@/context/global.context";
import CurrencySelect from "@/components/bits/CurrencySelect";

export default function AddClientCharge({ setOpen, open, finished, item }) {
  const [step, setStep] = useState(0);
  const { user_id } = useAuth;
  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.AddClientChargesMutation,
    onSuccess: () => {
      setStep((curr) => curr + 1);
    },
    onError: () => {
      return;
    },
  });
  const Schema = Yup.object().shape({
    data: Yup.object({
      currency: Yup.object({
        id: Yup.string().required("Currency is required"),
      }),
      payoutChargeType: Yup.object({
        id: Yup.string().required("Charge Type is required"),
      }),
      payOutClientTransactionType: Yup.object({
        typeId: Yup.string().required("Transaction Type is required"),
      }),
      baseValue: Yup.string().required("Base Value is required"),
      minimumFixedCapped: Yup.string().required("Minimum is required"),
      maximumFixedCapped: Yup.string().required("Maximum is required"),
    }),
  });
  const formik = useFormik({
    initialValues: {
      adminId: user_id === undefined ? 0 : user_id,
      clientId: item?.userId,
      data: {
        currency: {
          id: "",
        },
        payoutChargeType: {
          id: "",
        },
        payOutClientTransactionType: {
          typeId: "",
        },
        baseValue: "",
        minimumFixedCapped: "",
        maximumFixedCapped: "",
      },
    },
    validationSchema: Schema,
    onSubmit: () => {
      setStep((curr) => curr + 1);
    },
  });

  const { data: chargesTypes } = useQuery({
    queryKey: ["GetChargeTypesssQuery"],
    queryFn: () => ApiServiceAdmin.GetChargeTypesQuery(),
  });

  const { data: transactionTypes } = useQuery({
    queryKey: ["GetTransactionsssTypeQuery"],
    queryFn: () => ApiServiceAdmin.GetTransactionTypeQuery(),
  });

  console.log(formik.values, "vals");
  return (
    <AppModal
      setStep={setStep}
      step={step}
      setOpen={setOpen}
      open={open}
      loading={isPending}
      submit={() => {
        mutate(formik?.values);
      }}
      formik={formik}
      title="Create Charge"
      finish={() => {
        setStep(0);
        setOpen(false);
        finished();
      }}
    >
      <CurrencySelect
        label="Currency"
        name={"data[currency][id]"}
        width="100%"
        optionValue="id"
        formik={formik}
      />
      <MainSelect
        label="Charge Type"
        name={"data[payoutChargeType][id]"}
        width="100%"
        formik={formik}
        options={chargesTypes?.data?.map((itm) => {
          return {
            ...itm,
            label: itm?.typeName,
            value: itm?.id,
          };
        })}
      />

      <MainSelect
        label="Transaction Type"
        name={"data[payOutClientTransactionType][typeId]"}
        width="100%"
        formik={formik}
        options={transactionTypes?.data?.map((itm) => {
          return {
            ...itm,
            label: itm?.typeName,
            value: itm?.typeId,
          };
        })}
        optionValue="typeId"
      />
      <FormInputNumber
        label={"Base Value"}
        name={"data[baseValue]"}
        formik={formik}
        amount
      />
      <FormInputNumber
        label={"Min Fixed Capped Amt"}
        name={"data[minimumFixedCapped]"}
        formik={formik}
        amount
      />
      <FormInputNumber
        label={"Max Fixed Capped Amt"}
        name={"data[maximumFixedCapped]"}
        formik={formik}
        amount
      />
    </AppModal>
  );
}
