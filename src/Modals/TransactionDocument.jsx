import Modal from "@/components/bits/Modal";
import { X } from "lucide-react";

export default function TransactionDocument({ setOpen, open, data }) {
  return (
    <Modal
      dialogBtns={false}
      open={open}
      title="Document"
      width="700px"
      trigger={() => {
        setOpen(!open);
      }}
    >
      <X
        style={{
          position: "absolute",
          top: "30px",
          right: "30px",
          cursor: "pointer",
        }}
        onClick={() => {
          setOpen(false);
        }}
      />
      <img
        src={data}
        style={{
          height: "450px",
          width: "100%",
          margin: "0 auto",
          objectFit: "contain",
          border: "1px solid",
        }}
      />
    </Modal>
  );
}
