import { useState } from "react";
import AppModal from "./AppModal";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useFormik } from "formik";
import * as Yup from "yup";
import FormInputNumber from "@/components/bits/FormInputNumber";
import MainSelect from "@/components/bits/MainSelect";
import { CurrencyCompare } from "@/components/bits/CurrencyCompare";
import { useAuth } from "@/context/global.context";
import CurrencySelect from "@/components/bits/CurrencySelect";

export default function CreateRateModal({ setOpen, open, finished, item }) {
  const [step, setStep] = useState(0);
  const { user_id, user_data } = useAuth();
  const { data } = useQuery({
    queryKey: ["GetCurrencyRateMetadataListQuery"],
    queryFn: () =>
      ApiServiceAdmin.GetCurrencyRateMetadataQuery({
        role: user_id,
        rateMetaDataId: 0,
      }),
  });
  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.AddNewRateMutation,
    onSuccess: () => {
      setStep((curr) => curr + 1);
    },
    onError: () => {
      return;
    },
  });
  const Schema = Yup.object().shape({
    currencyRateMetaData: Yup.object({
      id: Yup.string().required("Rate Metadata is required"),
      role: Yup.object({
        id: Yup.string().required("Role is required"),
      }),
    }),
    fromCurrency: Yup.object({
      id: Yup.string().required("Sending Currency is required"),
    }),
    toCurrency: Yup.object({
      id: Yup.string().required("Receiving Currency is required"),
    }),
    conversionRate: Yup.string().required("Rate is required"),
  });
  const formik = useFormik({
    initialValues: {
      currencyRateMetaData: {
        id: "",
        role: {
          id: "",
        },
      },
      updatedBy: {
        userId: user_id,
        firstName: user_data?.firstName,
      },
      conversionRate: "",
      fromCurrency: {
        id: "",
      },
      toCurrency: {
        id: "",
      },
    },
    validationSchema: Schema,
    onSubmit: () => {
      setStep((curr) => curr + 1);
    },
  });

  console.log(formik.values, "vals");

  const [obj, setObj] = useState();

  const metadata = data?.data?.map((itm) => {
    return {
      ...itm,
      label: itm?.name,
      value: itm?.id,
    };
  });
  console.log(item, obj, formik?.values, "yeah");
  return (
    <AppModal
      setStep={setStep}
      step={step}
      setOpen={setOpen}
      open={open}
      loading={isPending}
      submit={() => {
        mutate(formik?.values);
      }}
      formik={formik}
      title="Update Rate"
      finish={() => {
        setStep(0);
        setOpen(false);
        finished();
      }}
    >
      <MainSelect
        label="Rate Metadata"
        name="currencyRateMetaData[id]"
        options={metadata}
        width="100%"
        formik={formik}
        onChange={(e) => {
          setObj({
            ...obj,
            meta: e,
          });

          formik.setFieldValue("currencyRateMetaData[role][id]", e?.role?.id);
        }}
      />
      <CurrencySelect
        label="Sending Currency"
        name="fromCurrency[id]"
        width="100%"
        optionValue="id"
        formik={formik}
        setCurrency={(e) => {
          setObj({
            ...obj,
            from: e,
          });
        }}
      />
      <CurrencySelect
        label="Receiving Currency"
        name="toCurrency[id]"
        optionValue="id"
        width="100%"
        formik={formik}
        setCurrency={(e) => {
          setObj({
            ...obj,
            to: e,
          });
        }}
      />
      <FormInputNumber
        label="Rate"
        name="conversionRate"
        width="100%"
        amount
        formik={formik}
      />

      {obj?.to && (
        <CurrencyCompare
          fromCurrency={obj?.from?.label}
          fromRate={"1"}
          toCurrency={obj?.to?.label}
          toRate={formik.values?.conversionRate}
        />
      )}
      <br />
    </AppModal>
  );
}
