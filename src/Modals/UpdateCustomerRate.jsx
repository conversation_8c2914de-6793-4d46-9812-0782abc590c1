import { useState } from "react";
import AppModal from "./AppModal";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useMutation } from "@tanstack/react-query";
import { useFormik } from "formik";
import * as Yup from "yup";
import FormInputNumber from "@/components/bits/FormInputNumber";
import MainSelect from "@/components/bits/MainSelect";
import { CurrencyBadge } from "@/components/bits/CurrencyBadge";
import { Flex } from "@radix-ui/themes";
import { CurrencyCompare } from "@/components/bits/CurrencyCompare";
import { useAuth } from "@/context/global.context";

export default function UpdateCustomerRateModal({
  setOpen,
  open,
  finished,
  item,
}) {
  const { user_id } = useAuth();
  const [step, setStep] = useState(0);

  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.UpdateSpecialRateMutation,
    onSuccess: () => {
      setStep((curr) => curr + 1);
    },
    onError: () => {
      return;
    },
  });
  const Schema = Yup.object().shape({
    currencyRateId: Yup.string().required("Currenncy Rate is required"),
    specialRate: Yup.string().required("Special Rate is required"),
    charge: Yup.string().required("Fee is required"),
  });
  const formik = useFormik({
    initialValues: {
      agentId: user_id,
      customerId: item?.userId,
      currencyRateId: "",
      specialRate: "",
      charge: "",
      whole: {},
    },
    validationSchema: Schema,
    onSubmit: () => {
      setStep((curr) => curr + 1);
    },
  });

  console.log(formik.values, "vals");

  const currencyOptions = item?.specialRates?.map((itm) => {
    return {
      ...itm,
      value: itm?.currencyRate?.id,
      fromCurrency: itm?.currencyRate?.fromCurrency?.code,
      fromRate: "1",
      toCurrency: itm?.currencyRate?.toCurrency?.code,
      toRate: itm?.currencyRate?.conversionRate,
    };
  });
  console.log(item, currencyOptions, "yeah");
  return (
    <AppModal
      setStep={setStep}
      step={step}
      setOpen={setOpen}
      open={open}
      loading={isPending}
      submit={() => {
        mutate({
          agentId: user_id,
          customerId: item?.userId,
          specialRate: {
            currencyRateId: Number(formik.values.currencyRateId),
            specialRate: Number(formik.values.specialRate),
            charge: Number(formik.values.charge),
          },
        });
      }}
      formik={formik}
      title="Update Rate"
      finish={() => {
        setStep(0);
        setOpen(false);
        finished();
      }}
    >
      <MainSelect
        label="Currency"
        name="currencyRateId"
        width="100%"
        options={currencyOptions}
        onChange={(e) => {
          if (e?.specialRate !== 0) {
            formik.setFieldValue("specialRate", e?.specialRate);
          }
          formik.setFieldValue("whole", e);
          formik.setFieldValue("charge", e?.charge);
        }}
        elementValue={(e) => {
          return (
            <Flex gap={"3"} align={"center"}>
              <CurrencyBadge currency={e?.fromCurrency} fontSize="14px" />
              <span
                style={{
                  fontSize: "13px",
                  color: "#5a6376",
                }}
              >
                to
              </span>
              <CurrencyBadge currency={e?.toCurrency} fontSize="14px" />
            </Flex>
          );
        }}
        formik={formik}
      />
      <FormInputNumber
        label="Special Rate"
        name="specialRate"
        width="100%"
        amount
        formik={formik}
      />
      <FormInputNumber
        label="Transfer Fee"
        name="charge"
        width="100%"
        amount
        formik={formik}
      />
      {formik.values?.currencyRateId && (
        <CurrencyCompare
          fromCurrency={formik.values.whole?.fromCurrency}
          fromRate={formik.values.whole?.fromRate}
          toCurrency={formik.values.whole?.toCurrency}
          toRate={formik.values.whole?.toRate}
        />
      )}
      <br />
    </AppModal>
  );
}
