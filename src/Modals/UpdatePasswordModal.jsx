import { useState } from "react";
import AppModal from "./AppModal";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useMutation } from "@tanstack/react-query";
import { useFormik } from "formik";
import { useAuth } from "@/context/global.context";
import * as Yup from "yup";
import FormInputPassword from "@/components/bits/FormInputPassword";

export default function UpdatePasswordModal({ setOpen, open, finished, item }) {
  const { user_id } = useAuth();
  const [step, setStep] = useState(0);

  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.UpdateUserPasswordMutation,
    onSuccess: () => {
      setStep((curr) => curr + 1);
      formik.resetForm();
    },
    onError: () => {
      return;
    },
  });

  const Schema = Yup.object().shape({
    oldPassword: Yup.string().required("Old Password is required"),
    password: Yup.string().required("Password is required"),
    password_confirmation: Yup.string()
      .required("Confirm Password")
      .oneOf([Yup.ref("password"), null], "Passwords must match"),
  });
  const formik = useFormik({
    initialValues: {
      oldPassword: "",
      password: "",
      password_confirmation: "",
    },
    validationSchema: Schema,
    onSubmit: () => {
      setStep((curr) => curr + 1);
    },
  });

  console.log(formik.values, "vals");
  return (
    <AppModal
      setStep={setStep}
      step={step}
      setOpen={setOpen}
      open={open}
      loading={isPending}
      submit={() => {
        mutate({
          updatedBy: user_id,
          userId: item?.userId,
          oldPassword: formik.values.oldPassword,
          password: formik.values.password_confirmation,
        });
      }}
      formik={formik}
      title="Update Password"
      finish={() => {
        setStep(0);
        setOpen(false);
        finished();
      }}
    >
      <FormInputPassword
        label="Old Password"
        name="oldPassword"
        width="100%"
        formik={formik}
      />
      <FormInputPassword
        label="New Password"
        name="password"
        width="100%"
        formik={formik}
      />
      <FormInputPassword
        label="Confirm Password"
        name="password_confirmation"
        width="100%"
        formik={formik}
      />
    </AppModal>
  );
}
