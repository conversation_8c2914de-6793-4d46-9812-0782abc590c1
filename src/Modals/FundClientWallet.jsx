import { useState } from "react";
import AppModal from "./AppModal";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useMutation } from "@tanstack/react-query";
import { useFormik } from "formik";
import * as Yup from "yup";
import MainSelect from "@/components/bits/MainSelect";
import FormInputNumber from "@/components/bits/FormInputNumber";
import { useAuth } from "@/context/global.context";
import FormInput from "@/components/bits/FormInput";

export default function FundClientWalletModal({
  setOpen,
  open,
  finished,
  item,
}) {
  const [step, setStep] = useState(0);
  const { user_id } = useAuth;
  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.FundClientWalletMutation,
    onSuccess: () => {
      setStep((curr) => curr + 1);
    },
    onError: () => {
      return;
    },
  });

  const Schema = Yup.object().shape({
    walletId: Yup.string().required("Wallet is required"),

    comment: Yup.string().required("Comment is required"),
    amountRequested: Yup.string().required("Ammount is required"),
  });
  const formik = useFormik({
    initialValues: {
      astUpdatedBy: user_id === undefined ? 0 : user_id,
      userId: item?.userId,
      walletId: "",

      amountRequested: "",
      comment: "",
    },
    validationSchema: Schema,
    onSubmit: () => {
      setStep((curr) => curr + 1);
    },
  });

  const options = item?.payOutClientWalletPayOutProviders?.map((itm) => {
    return {
      ...itm,
      label: itm?.providerName,
      value: itm?.wallet?.walletId,
    };
  });

  console.log(formik.values, formik.errors, "vals");
  return (
    <AppModal
      setStep={setStep}
      step={step}
      setOpen={setOpen}
      open={open}
      loading={isPending}
      submit={() => {
        mutate({
          userWallet: {
            walletId: formik?.values?.walletId,
          },
          userId: item?.userId,
          amountRequested: formik?.values?.amountRequested,
          comment: formik?.values?.comment,
          astUpdatedBy: user_id === undefined ? 0 : user_id,
        });
      }}
      formik={formik}
      title="Fund Client"
      finish={() => {
        setStep(0);
        setOpen(false);
        finished();
      }}
    >
      <MainSelect
        label="Gateway"
        name="walletId"
        width="100%"
        formik={formik}
        options={options}
      />
      <FormInputNumber
        label={"Amount"}
        name={"amountRequested"}
        formik={formik}
        amount
      />
      <FormInput label={"Comment"} name={"comment"} formik={formik} amount />
    </AppModal>
  );
}
