import { useState } from "react";
import AppModal from "./AppModal";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useMutation } from "@tanstack/react-query";
import { useFormik } from "formik";
import * as Yup from "yup";
import FormInputFile from "@/components/bits/FormInputFile";

export default function EditClientID({ setOpen, open, finished, item }) {
  const [step, setStep] = useState(0);

  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.UpdateClientFileMutation,
    onSuccess: () => {
      finished();
    },
    onError: () => {
      return;
    },
  });
  const Schema = Yup.object().shape({
    file: Yup.string().required("File is required"),
  });
  const formik = useFormik({
    initialValues: {
      file: item?.file,
    },
    validationSchema: Schema,
    onSubmit: () => {
      setStep((curr) => curr + 1);
    },
  });

  console.log(formik.values, "vals");
  return (
    <AppModal
      setStep={setStep}
      step={step}
      setOpen={setOpen}
      open={open}
      loading={isPending}
      submit={() => {
        mutate({
          objectId: item?.userId,
          action: 1,
          fileName: item?.name,
          fileURL: formik.values.file,
        });
      }}
      formik={formik}
      title="Edit File"
      finish={() => {
        setStep(0);
        setOpen(false);
        finished();
      }}
    >
      <FormInputFile
        label="File"
        name="file"
        id={"filee"}
        width="100%"
        userId={item?.userId}
        formik={formik}
      />
    </AppModal>
  );
}
