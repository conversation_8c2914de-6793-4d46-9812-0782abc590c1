import { useState } from "react";
import AppModal from "./AppModal";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useMutation } from "@tanstack/react-query";
import { useFormik } from "formik";
import * as Yup from "yup";
import CurrencySelect from "@/components/bits/CurrencySelect";
import { Grid } from "@radix-ui/themes";
import { CurrencyBadge } from "@/components/bits/CurrencyBadge";
import { X } from "lucide-react";

export default function UpdatePayoutProviderModal({
  setOpen,
  open,
  finished,
  data,
}) {
  const [step, setStep] = useState(0);
  const details = data;
  console.log(details, "details");

  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.UpdatePayoutProviderMutation,
    onSuccess: () => {
      setStep((curr) => curr + 1);
    },
    onError: () => {
      return;
    },
  });
  const Schema = Yup.object().shape({
    payOutProviderSupportedCurrency: Yup.array()
      .min(1)
      .required("at least one item needs to be here"),
  });
  const formik = useFormik({
    initialValues: {
      id: details?.id,
      payOutProviderSupportedCurrency:
        details?.payOutProviderSupportedCurrency?.map((itm) => {
          return {
            id: itm?.id,
            code: itm?.code,
          };
        }),
    },
    validationSchema: Schema,
    onSubmit: () => {
      setStep((curr) => curr + 1);
    },
    validateOnChange: true, // Ensure this is true
  });

  console.log(formik.values, formik.errors, "vals");
  return (
    <AppModal
      setStep={setStep}
      step={step}
      setOpen={setOpen}
      open={open}
      loading={isPending}
      submit={() => {
        mutate({
          ...formik.values,
          payOutProviderSupportedCurrency:
            formik.values.payOutProviderSupportedCurrency?.map((itm) => {
              return {
                id: itm?.id,
              };
            }),
        });
      }}
      formik={formik}
      title="Update Payout Provider"
      finish={() => {
        setStep(0);
        setOpen(false);
        finished();
      }}
    >
      <CurrencySelect
        label="Currency"
        placeholder="Select..."
        setCurrency={(e) => {
          if (
            formik.values.payOutProviderSupportedCurrency
              ?.map((itm) => itm?.id)
              ?.includes(e?.id)
          ) {
            return;
          } else {
            formik.setFieldValue("payOutProviderSupportedCurrency", [
              ...formik.values.payOutProviderSupportedCurrency,
              { id: e?.id, code: e?.code },
            ]);
          }
        }}
        formik={formik}
        name="payOutProviderSupportedCurrency"
        optionValue={"id"}
        width="100%"
      />

      <Grid gap={"5"} columns={"4"}>
        {formik.values.payOutProviderSupportedCurrency?.map((itm) => {
          return (
            <div
              style={{
                background: "#F4F7F7",
                padding: "10px",
                borderRadius: "8px",
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <CurrencyBadge currency={itm?.code} />
              <X
                onClick={() => {
                  formik.setFieldValue(
                    "payOutProviderSupportedCurrency",
                    formik.values.payOutProviderSupportedCurrency?.filter(
                      (curr) => curr?.id !== itm?.id
                    )
                  );
                }}
                size={16}
              />
            </div>
          );
        })}
      </Grid>
    </AppModal>
  );
}
