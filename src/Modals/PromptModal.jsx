import { useState } from "react";
import AppModal from "./AppModal";

export default function PromptModal({ setOpen, open, finish }) {
  const [step, setStep] = useState(1);

  return (
    <AppModal
      setStep={setStep}
      step={step}
      setOpen={setOpen}
      open={open}
      submit={() => {
        setStep((curr) => curr + 1);
      }}
      title=""
      finish={() => {
        setStep(1);
        setOpen(false);
        finish();
      }}
    ></AppModal>
  );
}
