import { useEffect, useState } from "react";
import AppModal from "./AppModal";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useFormik } from "formik";
import MainSelect from "@/components/bits/MainSelect";
import * as Yup from "yup";
import FormInput from "@/components/bits/FormInput";
import CurrencySelect from "@/components/bits/CurrencySelect";
import SwitchInput from "@/components/bits/SwitchInput";

export default function UpdatePaymentProcessorModal({
  setOpen,
  open,
  finished,
  data,
}) {
  const [step, setStep] = useState(0);

  const details = data;

  console.log(details, "details");

  useEffect(() => {}, []);

  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.UpdatePaymentProcessorMutation,
    onSuccess: () => {
      setStep((curr) => curr + 1);
    },
    onError: () => {
      return;
    },
  });
  const Schema = Yup.object().shape({
    name: Yup.string().required("Name is required"),
    description: Yup.string().required("Description is required"),
    currency: Yup.object({
      id: Yup.string().required("Currency is required"),
    }),
    paymentChannel: Yup.object({
      id: Yup.string().required("Channel is required"),
    }),
    paymentProvider: Yup.object({
      id: Yup.string().required("Provider is required"),
    }),
  });
  const formik = useFormik({
    initialValues: {
      name: details?.name,
      description: details?.description,
      status: details?.status,
      id: details?.id,
      currency: {
        id: details?.currency?.id,
      },
      paymentChannel: {
        id: details?.paymentChannel?.id,
      },
      paymentProvider: {
        id: details?.paymentProvider?.id,
      },
    },
    validationSchema: Schema,
    onSubmit: () => {
      setStep((curr) => curr + 1);
    },
    validateOnChange: true, // Ensure this is true
  });

  const { data: list1 } = useQuery({
    queryKey: ["GetPaymentProviderQuery"],
    queryFn: () => ApiServiceAdmin.GetPaymentProviderQuery(),
  });

  const { data: list } = useQuery({
    queryKey: ["GetPaymentChannelQuery"],
    queryFn: () => ApiServiceAdmin.GetPaymentChannelQuery(),
  });

  const providers =
    list1 &&
    list1.data.map((itm) => {
      return {
        label: itm.name,
        value: itm.id,
      };
    });

  const channels =
    list &&
    list.data.map((itm) => {
      return {
        label: itm.name,
        value: itm.id,
      };
    });

  console.log(formik.values, formik.errors, "vals");
  return (
    <AppModal
      setStep={setStep}
      step={step}
      setOpen={setOpen}
      open={open}
      loading={isPending}
      submit={() => {
        mutate(formik.values);
      }}
      formik={formik}
      title="Update Payment Processor"
      finish={() => {
        setStep(0);
        setOpen(false);
        finished();
      }}
    >
      <SwitchInput label={"Status"} name={"status"} formik={formik} />
      <br />
      <FormInput label="Name" name="name" width="100%" amount formik={formik} />
      <FormInput
        label="Description"
        name="description"
        width="100%"
        amount
        formik={formik}
      />
      <CurrencySelect
        label="Currency"
        placeholder="Select..."
        formik={formik}
        name="currency[id]"
        optionValue={"id"}
        width="100%"
      />
      <MainSelect
        label="Payment Channel"
        placeholder="Select..."
        options={channels}
        formik={formik}
        name="paymentChannel[id]"
      />
      <MainSelect
        label="Payment Provider"
        placeholder="Select..."
        options={providers}
        formik={formik}
        name="paymentProvider[id]"
      />
    </AppModal>
  );
}
