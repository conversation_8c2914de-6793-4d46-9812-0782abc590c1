import { CurrencyBadge } from "@/components/bits/CurrencyBadge";
import Modal from "@/components/bits/Modal";
import DividerHorizontal from "@/components/DividerHorizontal";
import { Flex, Grid } from "@radix-ui/themes";
import { <PERSON><PERSON><PERSON>, Mark<PERSON>, useJsApiLoader } from "@react-google-maps/api";
import { X } from "lucide-react";
import { useCallback } from "react";
import styled from "styled-components";

export default function TransactionLocation({ setOpen, open, data }) {
  const details = data;

  console.log(details, "details");

  const { isLoaded } = useJsApiLoader({
    id: "google-map-script",
    googleMapsApiKey: "AIzaSyB0EeusnqsQYjy7FsoSivl3dYwVxyKhvZs",
  });

  const onLoad = (map) => {
    const bounds = new window.google.maps.LatLngBounds();
    bounds.extend({
      lat: Number(details?.latitude),
      lng: Number(details?.longitude),
    });
    map.fitBounds(bounds);
  };

  const onUnmount = useCallback(function callback(map) {
    console.log(map);
  }, []);

  const containerStyle = {
    width: "100%",
    height: "100%",
    transform: "translateY(10%)",
  };
  return (
    <Modal
      dialogBtns={false}
      open={open}
      title=""
      width="800px"
      pads={false}
      trigger={() => {
        setOpen(!open);
      }}
    >
      <Grid columns={"2"}>
        <div
          style={{
            position: "relative",
          }}
        >
          <Map>
            {isLoaded && (
              <GoogleMap
                mapContainerStyle={containerStyle}
                onLoad={onLoad}
                onUnmount={onUnmount}
              >
                <Marker
                  position={{
                    lat: Number(details?.latitude),
                    lng: Number(details?.longitude),
                  }}
                  icon={"https://img.icons8.com/fluency/48/null/maps.png"}
                />
              </GoogleMap>
            )}
          </Map>
        </div>

        <div
          style={{
            padding: "30px",
            width: "100%",
          }}
        >
          <Flex justify={"between"} align={"center"}>
            <div></div>
            <h3 style={{ fontWeight: 500 }}>Location Details</h3>
            <X
              style={{
                cursor: "pointer",
              }}
              onClick={() => {
                setOpen(false);
              }}
            />
          </Flex>
          <br />
          <div
            style={{
              borderRadius: "8px",
              border: "1px solid #ECEFF3",
              background: "#F8FAFB",
              paddingInline: "20px",
              paddingBlock: "8px",
              width: "100%",
            }}
          >
            <Flex justify={"between"} py={"3"}>
              <span
                style={{
                  color: "#666D80",
                  fontSize: "14px",
                }}
              >
                Country
              </span>
              <span
                style={{
                  color: "#36394A",
                  fontSize: "14px",
                }}
              >
                <CurrencyBadge
                  currency={details?.currency?.code}
                  name={details?.country_name}
                />
              </span>
            </Flex>
            <DividerHorizontal style={{ margin: 0 }} />
            <Flex justify={"between"} py={"3"}>
              <span
                style={{
                  color: "#666D80",
                  fontSize: "14px",
                }}
              >
                City
              </span>
              <span
                style={{
                  color: "#36394A",
                  fontSize: "14px",
                }}
              >
                {details?.city}
              </span>
            </Flex>
            <DividerHorizontal style={{ margin: 0 }} />
            <Flex justify={"between"} py={"3"}>
              <span
                style={{
                  color: "#666D80",
                  fontSize: "14px",
                }}
              >
                District
              </span>
              <span
                style={{
                  color: "#36394A",
                  fontSize: "14px",
                }}
              >
                {details?.district}
              </span>
            </Flex>
            <DividerHorizontal style={{ margin: 0 }} />
            <Flex justify={"between"} py={"3"}>
              <span
                style={{
                  color: "#666D80",
                  fontSize: "14px",
                }}
              >
                State
              </span>
              <span
                style={{
                  color: "#36394A",
                  fontSize: "14px",
                }}
              >
                {details?.state_prov}
              </span>
            </Flex>
            <DividerHorizontal style={{ margin: 0 }} />
            <Flex justify={"between"} py={"3"}>
              <span
                style={{
                  color: "#666D80",
                  fontSize: "14px",
                }}
              >
                Zip Code
              </span>
              <span
                style={{
                  color: "#36394A",
                  fontSize: "14px",
                }}
              >
                {details?.zipcode}
              </span>
            </Flex>
            <DividerHorizontal style={{ margin: 0 }} />
            <Flex justify={"between"} py={"3"}>
              <span
                style={{
                  color: "#666D80",
                  fontSize: "14px",
                }}
              >
                IP
              </span>
              <span
                style={{
                  color: "#36394A",
                  fontSize: "14px",
                }}
              >
                {details?.ip}
              </span>
            </Flex>
            <DividerHorizontal style={{ margin: 0 }} />
            <Flex justify={"between"} py={"3"}>
              <span
                style={{
                  color: "#666D80",
                  fontSize: "14px",
                }}
              >
                Currency
              </span>
              <span
                style={{
                  color: "#36394A",
                  fontSize: "14px",
                }}
              >
                {details?.currency?.code}
              </span>
            </Flex>
            <DividerHorizontal style={{ margin: 0 }} />
            <Flex justify={"between"} py={"3"}>
              <span
                style={{
                  color: "#666D80",
                  fontSize: "14px",
                }}
              >
                Timezone
              </span>
              <span
                style={{
                  color: "#36394A",
                  fontSize: "14px",
                }}
              >
                {details?.time_zone?.name}
              </span>
            </Flex>
          </div>
        </div>
      </Grid>
    </Modal>
  );
}

const Map = styled.div`
  height: 100%;
  width: 100%;
  transform: translate(-3.4%, -10.23%);
  border-radius: 14px 0 0 14px;
  position: absolute;
  left: 0;
  top: 0;
`;
