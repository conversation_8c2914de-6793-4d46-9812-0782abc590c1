/* eslint-disable react-hooks/exhaustive-deps */
import Modal from "@/components/bits/Modal";
import DividerHorizontal from "@/components/DividerHorizontal";
import { Flex } from "@radix-ui/themes";
import { X } from "lucide-react";

export default function IDComments({ setOpen, open, data }) {
  return (
    <Modal
      dialogBtns={false}
      open={open}
      title=""
      trigger={() => {
        setOpen(!open);
      }}
    >
      <Flex
        justify={"between"}
        align={"center"}
        style={{
          position: "absolute",
          top: "0",
          right: "0",
          padding: "20px",
          width: "100%",
        }}
      >
        <h3 style={{ fontWeight: 500 }}>View Comments</h3>

        <X
          style={{
            cursor: "pointer",
          }}
          onClick={() => {
            setOpen(false);
          }}
        />
      </Flex>
      <br />
      <div
        style={{
          marginBottom: "-20px",
        }}
      >
        {data?.map((itm, idx) => {
          return (
            <>
              <Flex justify={"between"} py={"3"}>
                <div>
                  <div
                    style={{
                      color: "#000000",
                      fontSize: "14px",
                    }}
                  >
                    {itm?.comment}
                  </div>
                  <div
                    style={{
                      color: "#36394A",
                      fontSize: "14px",
                    }}
                  >
                    Submitted by {itm?.commentByName}
                  </div>
                </div>
              </Flex>
              {idx === data?.length - 1 ? (
                ""
              ) : (
                <DividerHorizontal style={{ margin: 0 }} />
              )}
            </>
          );
        })}
      </div>
    </Modal>
  );
}
