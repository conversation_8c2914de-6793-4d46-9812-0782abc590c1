/* eslint-disable react-hooks/exhaustive-deps */
import * as Yu<PERSON> from "yup";
import { useEffect, useState } from "react";
import AppModal from "./AppModal";
import FormInput from "@/components/bits/FormInput";
import AddUserIcon from "@/assets/icons/modal-icons/AddUserIcon";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useFormik } from "formik";
import CurrencySelect from "@/components/bits/CurrencySelect";
import CountrySelect from "@/components/bits/CountrySelect";
import BankSelect from "@/components/bits/BankSelect";
import FormInputNumber from "@/components/bits/FormInputNumber";

export default function AddBeneficiaryModal({
  setOpen,
  open,
  finished,
  customerId,
}) {
  const [step, setStep] = useState(0);

  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.AddBeneficiaryMutation,
    onSuccess: () => {
      setStep((curr) => curr + 1);
    },
    onError: () => {
      return;
    },
  });

  const { data: name, refetch } = useQuery({
    queryKey: ["NameEnquiry"],
    queryFn: () =>
      ApiServiceAdmin.NameEnquiry({
        bankCode: formik?.values?.userBeneficiary?.beneficiaryBank?.bankId,
        accountNumber:
          formik?.values?.userBeneficiary?.beneficiaryBank?.accountNumber,
      }),
    enabled: false,
  });

  console.log(name?.data?.account_name);

  const Schema = Yup.object().shape({
    userBeneficiary: Yup.object({
      beneficiaryCountry: Yup.object({
        id: Yup.string().required("Country is required"),
      }),
      currency: Yup.object({
        code: Yup.string().required("Currency is required"),
      }),

      beneficiaryBank: Yup.object({
        accountNumber: Yup.string().required("Account Number is required"),
        bankId: Yup.string().required("Bank is required"),
      }),
    }),
  });

  const Schema2 = Yup.object().shape({
    userBeneficiary: Yup.object({
      beneficiaryCountry: Yup.object({
        id: Yup.string().required("Country is required"),
      }),
      currency: Yup.object({
        code: Yup.string().required("Currency is required"),
      }),

      beneficiaryBank: Yup.object({
        bankName: Yup.string().required("Required"),
        bankAddress: Yup.string().required("Required"),
        postalCode: Yup.string().required("Required"),
        accountNumber: Yup.string().required("Required"),
        accountName: Yup.string().required("Required"),
        reference: Yup.string().required("Required"),
        bic: Yup.string().required("Required"),
      }),
      correspondenceBank: Yup.object({
        bankName: Yup.string().required("Required"),
        bankAddress: Yup.string().required("Required"),
        accountNumber: Yup.string().required("Required"),
        accountName: Yup.string().required("Required"),
        bic: Yup.string().required("Required"),
      }),
    }),
  });
  const [cID, setCID] = useState();
  const [bank, setBank] = useState();
  const formik = useFormik({
    initialValues: {
      userId: customerId,
      userBeneficiary: {
        beneficiaryCountry: {
          id: "",
        },
        currency: {
          code: "",
        },
        beneficiaryName: name?.data?.account_name,
        beneficiaryPhoneNumber: "",
        beneficiaryBank: {
          accountNumber: "",
          bankId: "",
        },
      },
    },
    validationSchema: cID === 161 ? Schema : Schema2,
    onSubmit: () => {
      setStep((curr) => curr + 1);
    },
  });

  useEffect(() => {
    setCID(formik.values.userBeneficiary?.beneficiaryCountry?.id);
  }, [formik.values.userBeneficiary?.beneficiaryCountry?.id]);

  useEffect(() => {
    refetch({
      bankCode: formik?.values?.userBeneficiary?.beneficiaryBank?.bankId,
      accountNumber:
        formik?.values?.userBeneficiary?.beneficiaryBank?.accountNumber,
    });
  }, [
    formik?.values?.userBeneficiary?.beneficiaryBank?.accountNumber?.length ===
      10 && formik?.values?.userBeneficiary?.beneficiaryBank?.bankId,
  ]);

  return (
    <AppModal
      setStep={setStep}
      step={step}
      Icon={AddUserIcon}
      setOpen={setOpen}
      open={open}
      loading={isPending}
      submit={() => {
        if (formik.values.userBeneficiary?.beneficiaryCountry?.id === 161) {
          mutate({
            userId: customerId,
            userBeneficiary: {
              beneficiaryCountry: {
                id: formik.values.userBeneficiary?.beneficiaryCountry?.id,
              },
              currency: {
                code: formik.values.userBeneficiary?.currency?.code,
              },
              beneficiaryName: name?.data?.account_name,
              beneficiaryPhoneNumber: "",
              beneficiaryBank: {
                accountNumber: name?.data?.account_number,
                bankId: bank?.bankId,
              },
            },
          });
        } else {
          mutate({
            userId: customerId,
            userBeneficiary: {
              beneficiaryCountry: {
                id: formik.values.userBeneficiary?.beneficiaryCountry?.id,
              },
              currency: {
                code: formik.values.userBeneficiary?.currency?.code,
              },

              beneficiaryName:
                formik?.values?.userBeneficiary?.beneficiaryBank?.accountName,
              beneficiaryAddress:
                formik?.values?.userBeneficiary?.beneficiaryBank?.bankAddress,
              beneficiaryBank: {
                bankName:
                  formik?.values?.userBeneficiary?.beneficiaryBank?.bankName,
                bankAddress:
                  formik?.values?.userBeneficiary?.beneficiaryBank?.bankAddress,
                postalCode:
                  formik?.values?.userBeneficiary?.beneficiaryBank?.postalCode,
                accountNumber:
                  formik?.values?.userBeneficiary?.beneficiaryBank
                    ?.accountNumber,
                accountName:
                  formik?.values?.userBeneficiary?.beneficiaryBank?.accountName,
                reference:
                  formik?.values?.userBeneficiary?.beneficiaryBank?.reference,
                iban: formik?.values?.userBeneficiary?.beneficiaryBank
                  ?.accountNumber,
                bic: formik?.values?.userBeneficiary?.beneficiaryBank?.bic,
                swiftCode:
                  formik?.values?.userBeneficiary?.beneficiaryBank?.bic,
              },
              correspondenceBank: {
                bankName:
                  formik?.values?.userBeneficiary?.correspondenceBank?.bankName,
                bankAddress:
                  formik?.values?.userBeneficiary?.correspondenceBank
                    ?.bankAddress,
                accountNumber:
                  formik?.values?.userBeneficiary?.correspondenceBank
                    ?.accountNumber,
                accountName:
                  formik?.values?.userBeneficiary?.correspondenceBank
                    ?.accountName,
                iban: formik?.values?.userBeneficiary?.correspondenceBank
                  ?.accountNumber,
                bic: formik?.values?.userBeneficiary?.correspondenceBank?.bic,
                swiftCode:
                  formik?.values?.userBeneficiary?.correspondenceBank?.bic,
              },
            },
          });
        }
      }}
      formik={formik}
      title="Add Beneficiary"
      finish={() => {
        setStep(0);
        setOpen(false);
        finished();
      }}
    >
      {formik.values.userBeneficiary?.beneficiaryCountry?.id === 161 ? (
        <div>
          <CountrySelect
            label="Select Country"
            name="userBeneficiary[beneficiaryCountry][id]"
            width="100%"
            optionValue="id"
            formik={formik}
          />
          <CurrencySelect
            label="Select Currency"
            name="userBeneficiary[currency][code]"
            optionValue="code"
            width="100%"
            formik={formik}
          />
          <BankSelect
            label="Select Bank"
            name="userBeneficiary[beneficiaryBank][bankId]"
            width="100%"
            optionValue="bankCode"
            formik={formik}
            setCurrency={(e) => {
              setBank(e);
            }}
          />
          <FormInputNumber
            label="Account Number"
            name="userBeneficiary[beneficiaryBank][accountNumber]"
            width="100%"
            max={10}
            formik={formik}
            hint={name?.data?.account_name}
          />
        </div>
      ) : (
        <div>
          <CountrySelect
            label="Select Country"
            name="userBeneficiary[beneficiaryCountry][id]"
            width="100%"
            optionValue="id"
            formik={formik}
          />
          <CurrencySelect
            label="Select Currency"
            name="userBeneficiary[currency][code]"
            optionValue="code"
            width="100%"
            formik={formik}
          />
          <br />

          <FormInput
            label="Bank Name"
            name="userBeneficiary[beneficiaryBank][bankName]"
            width="100%"
            formik={formik}
          />
          <FormInput
            label="Bank Address"
            name="userBeneficiary[beneficiaryBank][bankAddress]"
            width="100%"
            formik={formik}
          />
          <FormInputNumber
            label="Postal Code"
            name="userBeneficiary[beneficiaryBank][postalCode]"
            width="100%"
            formik={formik}
          />
          <FormInputNumber
            label="IBAN/Account Number"
            name="userBeneficiary[beneficiaryBank][accountNumber]"
            width="100%"
            formik={formik}
          />
          <FormInput
            label="Account Name"
            name="userBeneficiary[beneficiaryBank][accountName]"
            width="100%"
            formik={formik}
          />
          <FormInputNumber
            label="BIC/Swiftcode"
            name="userBeneficiary[beneficiaryBank][bic]"
            width="100%"
            formik={formik}
          />
          <FormInput
            label="Reference"
            name="userBeneficiary[beneficiaryBank][reference]"
            width="100%"
            formik={formik}
          />

          <h4>Correspondent Bank</h4>
          <FormInput
            label="Bank Name"
            name="userBeneficiary[correspondenceBank][bankName]"
            width="100%"
            formik={formik}
          />
          <FormInput
            label="Bank Address"
            name="userBeneficiary[correspondenceBank][bankAddress]"
            width="100%"
            formik={formik}
          />

          <FormInputNumber
            label="IBAN/Account Number"
            name="userBeneficiary[correspondenceBank][accountNumber]"
            width="100%"
            formik={formik}
          />
          <FormInput
            label="Account Name"
            name="userBeneficiary[correspondenceBank][accountName]"
            width="100%"
            max={10}
            formik={formik}
          />
          <FormInputNumber
            label="BIC/Swiftcode"
            name="userBeneficiary[correspondenceBank][bic]"
            width="100%"
            max={10}
            formik={formik}
          />
        </div>
      )}
    </AppModal>
  );
}
