import { useState } from "react";
import AppModal from "./AppModal";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useFormik } from "formik";
import MainSelect from "@/components/bits/MainSelect";
import FormInputNumber from "@/components/bits/FormInputNumber";
import { useAuth } from "@/context/global.context";
import * as Yup from "yup";

export default function ApproveClientFundRequestModal({
  setOpen,
  open,
  finished,
  item,
}) {
  const { user_id } = useAuth();
  const [step, setStep] = useState(0);

  const {
    mutate,
    isPending,
    data: sdata,
  } = useMutation({
    mutationFn: ApiServiceAdmin.ApproveWithdrawalRequestMutation,
    onSuccess: () => {
      setStep((curr) => curr + 1);
    },
    onError: () => {
      return;
    },
  });
  const Schema = Yup.object().shape({
    amountPaid: Yup.string().required("Amount is required"),
    pamentGatewayId: Yup.string().required("Gateway is required"),
  });
  const formik = useFormik({
    initialValues: {
      amountPaid: item?.amountRequested,
      pamentGatewayId: "",
      payoutPartner: "",
    },
    validationSchema: Schema,
    onSubmit: () => {
      setStep((curr) => curr + 1);
    },
  });

  const [partner, setPartner] = useState("");

  const { data } = useQuery({
    queryKey: ["GetPayoutPartnerQuery"],
    queryFn: () => ApiServiceAdmin.GetPayoutPartnerQuery(),
  });

  const { data: list } = useQuery({
    queryKey: ["GetPayoutPartnerGatewaysQuery"],
    queryFn: () => ApiServiceAdmin.GetPayoutPartnerGatewaysQuery(partner),
    enabled: partner ? true : false,
  });

  const payoutPartners =
    data &&
    data.data.map((itm) => {
      return {
        label: itm.name,
        value: itm.id,
        status: itm.status,
      };
    });

  const gateways =
    list &&
    list.data.map((itm) => {
      return {
        label: `${itm.providerName} - ${itm?.wallet?.currency?.code}`,
        value: itm.providerId,
      };
    });
  console.log(formik.values, item, "vals");
  return (
    <AppModal
      setStep={setStep}
      successMsg={sdata?.message}
      step={step}
      setOpen={setOpen}
      open={open}
      loading={isPending}
      submit={() => {
        mutate({
          adminId: user_id,
          updateType: 1,
          userId: item?.userId,
          pamentGatewayId: formik.values.pamentGatewayId,
          WithdrawalRequest: {
            id: item?.id,
            amountPaid: formik.values.amountPaid,
          },
        });
      }}
      formik={formik}
      title="Fund Request Approval"
      finish={() => {
        setStep(0);
        setOpen(false);
        finished();
      }}
    >
      <MainSelect
        label="Select Payout Partner"
        placeholder="Select..."
        options={payoutPartners}
        formik={formik}
        name="payoutPartner"
        onChange={(e) => {
          setPartner(e.value);
        }}
      />
      <MainSelect
        label="Gateway"
        placeholder="Select..."
        options={gateways}
        formik={formik}
        name="pamentGatewayId"
      />
      <FormInputNumber
        label="Amount Paid"
        name="amountPaid"
        width="100%"
        amount
        formik={formik}
      />
    </AppModal>
  );
}
