import { useState } from "react";
import AppModal from "./AppModal";
import FormInput from "@/components/bits/FormInput";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useMutation } from "@tanstack/react-query";
import { useFormik } from "formik";
import { InviteAgentSchema } from "@/Schema";

export default function InviteAgentModal({ setOpen, open, finished }) {
  const [step, setStep] = useState(0);

  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.SendAgentInviteMutation,
    onSuccess: () => {
      setStep((curr) => curr + 1);
    },
    onError: () => {
      return;
    },
  });

  const formik = useFormik({
    initialValues: {
      firstName: "",
      email: "",
    },
    validationSchema: InviteAgentSchema,
    onSubmit: () => {
      setStep((curr) => curr + 1);
    },
  });

  return (
    <AppModal
      setStep={setStep}
      step={step}
      setOpen={setOpen}
      open={open}
      loading={isPending}
      submit={() => {
        mutate(formik.values);
      }}
      formik={formik}
      title="Invite Agent"
      finish={() => {
        setStep(0);
        setOpen(false);
        finished();
      }}
    >
      <FormInput
        label="Email Address"
        placeholder="Enter their email address"
        name="email"
        width="100%"
        formik={formik}
      />
      <FormInput
        label="First Name"
        placeholder="Enter their first name"
        name="firstName"
        width="100%"
        formik={formik}
      />
    </AppModal>
  );
}
