import { useState } from "react";
import AppModal from "./AppModal";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useFormik } from "formik";
import * as Yup from "yup";
import FormInputNumber from "@/components/bits/FormInputNumber";
import MainSelect from "@/components/bits/MainSelect";
import { CurrencyBadge } from "@/components/bits/CurrencyBadge";
import { Flex } from "@radix-ui/themes";
import FormInput from "@/components/bits/FormInput";
import { FormatCurrency } from "@/lib/utils";
import { useAuth } from "@/context/global.context";

export default function DepleteCustomerWalletModal({
  setOpen,
  open,
  finished,
  item,
}) {
  const [step, setStep] = useState(0);
  const { user_id } = useAuth();
  const { data: customer } = useQuery({
    queryKey: ["GetCustomerFundQuery"],
    queryFn: () => ApiServiceAdmin.GetCustomerQuery(item?.userId),
  });
  const wallets = customer?.data?.wallet?.map((itm) => {
    return {
      ...itm,

      value: itm?.walletId,
    };
  });
  console.log(wallets);
  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.FundCustomerWalletMutation,
    onSuccess: () => {
      setStep((curr) => curr + 1);
    },
    onError: () => {
      return;
    },
  });
  const Schema = Yup.object().shape({
    amount: Yup.string().required("Amount is required"),
    id: Yup.string().required("Select a wallet"),
  });
  const formik = useFormik({
    initialValues: {
      id: "",
      amount: "",
    },
    validationSchema: Schema,
    onSubmit: () => {
      setStep((curr) => curr + 1);
    },
  });

  console.log(formik.values, "vals");

  return (
    <AppModal
      setStep={setStep}
      step={step}
      setOpen={setOpen}
      open={open}
      loading={isPending}
      submit={() => {
        mutate({
          adminId: user_id,
          userId: Number(item?.userId),
          userWallet: {
            walletId: Number(formik.values?.id),
            balance: -Number(formik.values.amount),
          },
        });
      }}
      formik={formik}
      title="Deplete Wallet"
      finish={() => {
        setStep(0);
        setOpen(false);
        finished();
      }}
    >
      <MainSelect
        label="Currency"
        name="id"
        width="100%"
        options={wallets}
        elementValue={(e) => {
          return (
            <Flex gap={"3"} align={"center"}>
              <CurrencyBadge
                currency={e?.currency?.code}
                name={`${e?.currency?.code} (${FormatCurrency(
                  e?.balance,
                  e?.currency?.code
                )})`}
                fontSize="14px"
              />
            </Flex>
          );
        }}
        formik={formik}
      />
      <FormInputNumber
        label="Amount"
        name="amount"
        width="100%"
        amount
        formik={formik}
      />

      <FormInput label="Note" name="note" width="100%" formik={formik} />
    </AppModal>
  );
}
