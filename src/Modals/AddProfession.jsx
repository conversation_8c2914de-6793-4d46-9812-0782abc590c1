import * as Yup from "yup";
import { useState } from "react";
import AppModal from "./AppModal";
import FormInput from "@/components/bits/FormInput";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useMutation } from "@tanstack/react-query";
import { useFormik } from "formik";
import MainSelect from "@/components/bits/MainSelect";

export default function AddProfessionModal({ setOpen, open, finished }) {
  const [step, setStep] = useState(0);

  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.AddProfessionMutation,
    onSuccess: () => {
      setStep((curr) => curr + 1);
    },
    onError: () => {
      return;
    },
  });

  const formik = useFormik({
    initialValues: {
      name: "",
      status: "",
      riskLevel: {
        id: "",
      },
    },
    validationSchema: Yup.object().shape({
      name: Yup.string().required("Name is required"),
      status: Yup.string().required("Status is required"),
      riskLevel: Yup.object({
        id: Yup.string().required("Risk Level is required"),
      }),
    }),
    onSubmit: () => {
      setStep((curr) => curr + 1);
    },
  });

  return (
    <AppModal
      setStep={setStep}
      step={step}
      setOpen={setOpen}
      open={open}
      loading={isPending}
      submit={() => {
        mutate(formik.values);
      }}
      formik={formik}
      title="Add Profession"
      finish={() => {
        setStep(0);
        setOpen(false);
        finished();
      }}
    >
      <FormInput label="Profession" name="name" width="100%" formik={formik} />
      <MainSelect
        label="Status"
        name="status"
        width="100%"
        options={[
          { label: "Active", value: "Active" },
          { label: "Inactive", value: "Inactive" },
        ]}
        formik={formik}
      />
      <MainSelect
        label="Risk Level"
        name="riskLevel[id]"
        width="100%"
        options={[
          { label: "Low", value: 1 },
          { label: "Medium", value: 2 },
          { label: "High", value: 3 },
        ]}
        formik={formik}
      />
    </AppModal>
  );
}
