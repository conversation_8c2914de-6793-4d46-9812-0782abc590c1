import AppButton from "@/components/bits/AppButton";
import { AlertDialog, Flex } from "@radix-ui/themes";

export default function ConfirmModal({
  trigger,
  open,
  title = "Confirm Changes?",
  description,
  width = "500px",
  confirm,
  cancel,
  loading,
}) {
  return (
    <AlertDialog.Root onOpenChange={trigger} open={open}>
      <AlertDialog.Content
        align="center"
        maxWidth={width}
        style={{
          padding: "60px 50px",
          borderRadius: "24px",
        }}
      >
        <center>
          <svg
            width="61"
            height="60"
            viewBox="0 0 61 60"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect
              x="0.5"
              width="60"
              height="60"
              rx="30"
              fill="#FF6500"
              fill-opacity="0.1"
            />
            <path
              d="M25.3529 25.8487C25.3504 25.9093 25.3602 25.9697 25.3819 26.0263C25.4035 26.0829 25.4365 26.1345 25.4789 26.1779C25.5212 26.2213 25.5719 26.2555 25.628 26.2786C25.684 26.3016 25.7442 26.313 25.8048 26.3119H27.3517C27.6104 26.3119 27.8167 26.1 27.8504 25.8431C28.0192 24.6131 28.8629 23.7169 30.3667 23.7169C31.6529 23.7169 32.8304 24.36 32.8304 25.9069C32.8304 27.0975 32.1292 27.645 31.0211 28.4775C29.7592 29.3944 28.7598 30.465 28.8311 32.2031L28.8367 32.61C28.8387 32.733 28.8889 32.8503 28.9766 32.9366C29.0643 33.0229 29.1824 33.0713 29.3054 33.0712H30.8261C30.9504 33.0712 31.0696 33.0219 31.1575 32.934C31.2454 32.846 31.2948 32.7268 31.2948 32.6025V32.4056C31.2948 31.0594 31.8067 30.6675 33.1886 29.6194C34.3304 28.7512 35.5211 27.7875 35.5211 25.7644C35.5211 22.9313 33.1286 21.5625 30.5092 21.5625C28.1336 21.5625 25.5311 22.6687 25.3529 25.8487ZM28.2723 36.6544C28.2723 37.6537 29.0692 38.3925 30.1661 38.3925C31.3079 38.3925 32.0936 37.6537 32.0936 36.6544C32.0936 35.6194 31.3061 34.8919 30.1642 34.8919C29.0692 34.8919 28.2723 35.6194 28.2723 36.6544Z"
              fill="#FF6500"
            />
          </svg>

          <AlertDialog.Title
            style={{
              fontFamily: "Fira Sans",
              color: "#FF6500",
            }}
          >
            {title}
          </AlertDialog.Title>
          <AlertDialog.Description
            size="3"
            style={{
              fontFamily: "Fira Sans",
              color: "#667085",
            }}
          >
            {description}
          </AlertDialog.Description>

          <br />
          <Flex gap="3" mt="4" justify="end">
            <AppButton
              placeholder="Cancel"
              loading={false}
              disabled={loading}
              onClick={cancel}
              secondary
              width="30%"
            />
            <AppButton
              width="70%"
              placeholder="Yes, Continue"
              loading={loading}
              disabled={loading}
              onClick={confirm}
            />
          </Flex>
        </center>
      </AlertDialog.Content>
    </AlertDialog.Root>
  );
}
