import { useState } from "react";
import AppModal from "./AppModal";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useMutation } from "@tanstack/react-query";
import { useFormik } from "formik";
import { useAuth } from "@/context/global.context";
import * as Yup from "yup";
import FormTextArea from "@/components/bits/FormTextArea";
import FormInput from "@/components/bits/FormInput";

export default function MessageCustomerModal({
  setOpen,
  open,
  finished,
  item,
  userRoleId,
  type,
  title = "Message Customer(s)",
}) {
  const { user_id } = useAuth();
  const [step, setStep] = useState(0);

  const { mutate, isPending } = useMutation({
    mutationFn:
      type === "all"
        ? ApiServiceAdmin.MessageCustomerByRoleMutation
        : ApiServiceAdmin.MessageCustomerMutation,
    onSuccess: () => {
      setStep((curr) => curr + 1);
      formik.setFieldValue("message", "");
    },
    onError: () => {
      return;
    },
  });
  const Schema = Yup.object().shape({
    message: Yup.string().required("Message is required"),
    title: Yup.string().required("Title is required"),
  });
  const formik = useFormik({
    initialValues: {
      message: "",
      title: "",
    },
    validationSchema: Schema,
    onSubmit: () => {
      setStep((curr) => curr + 1);
    },
  });

  console.log(formik.values, "vals");
  return (
    <AppModal
      setStep={setStep}
      step={step}
      setOpen={setOpen}
      open={open}
      loading={isPending}
      submit={() => {
        if (type === "all") {
          mutate({
            adminId: user_id,
            userRoleId: userRoleId,
            subject: formik.values.title,
            message: formik.values.message,
          });
        } else {
          mutate({
            adminId: user_id,
            userRoleId: userRoleId,
            userId: item?.userId,
            subject: formik.values.title,
            message: formik.values.message,
          });
        }
      }}
      formik={formik}
      title={title}
      finish={() => {
        setStep(0);
        setOpen(false);
        finished();
      }}
    >
      <FormInput
        label="Subject"
        placeholder={"Enter Subject"}
        name="title"
        width="100%"
        height={150}
        formik={formik}
      />
      <FormTextArea
        label="Message"
        placeholder={"Type your message"}
        name="message"
        width="100%"
        height={150}
        formik={formik}
      />
    </AppModal>
  );
}
