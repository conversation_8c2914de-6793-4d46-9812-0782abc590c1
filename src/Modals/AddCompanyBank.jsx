import { useState } from "react";
import AppModal from "./AppModal";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useMutation } from "@tanstack/react-query";
import { useFormik } from "formik";
import * as Yup from "yup";
import FormInputNumber from "@/components/bits/FormInputNumber";
import FormInput from "@/components/bits/FormInput";
import CurrencySelect from "@/components/bits/CurrencySelect";
import FormTextArea from "@/components/bits/FormTextArea";
import SwitchInput from "@/components/bits/SwitchInput";
import { Grid } from "@radix-ui/themes";

export default function AddCompanyBank({ setOpen, open, finished, item }) {
  const [step, setStep] = useState(0);
  const { mutate, isPending } = useMutation({
    mutationFn: item
      ? ApiServiceAdmin.UpdateCompanyBankMutation
      : ApiServiceAdmin.AddCompanyBankMutation,
    onSuccess: () => {
      setStep((curr) => curr + 1);
    },
    onError: () => {
      return;
    },
  });

  const Schema = Yup.object().shape({
    bankName: Yup.string().required("Bank Name is required"),
    description: Yup.string().required("Description is required"),
    accountName: Yup.string().required("Account Name is required"),
    accountNumber: Yup.string().required("Account Number is required"),
    bic: Yup.string().required("BIC is required"),
    sortCode: Yup.string().required("Sort Code is required"),
    iban: Yup.string().required("IBAN is required"),
    status: Yup.string().required("Status is required"),
    currency: Yup.object({
      id: Yup.string().required("Currency is required"),
    }),
  });

  const formik = useFormik({
    initialValues: item
      ? {
          sn: item?.sn,
          dateCreated: item?.dateCreated,
          description: item?.description,
          currency: {
            id: item?.currency?.id,
          },
          accountName: item?.accountName,
          bankName: item?.bankName,
          accountNumber: item?.accountNumber,
          sortCode: item?.sortCode,
          iban: item?.iban,
          bic: item?.bic,
          branch: item?.branch,
          lastUpdated: item?.lastUpdated,
          status: item?.status,
        }
      : {
          bankName: "",
          description: "",
          accountName: "",
          accountNumber: "",
          currency: {
            id: "",
          },
          bic: "",
          sortCode: "",
          iban: "",
          status: true,
        },
    validationSchema: Schema,
    onSubmit: () => {
      setStep((curr) => curr + 1);
    },
  });

  console.log(formik.values, formik.errors, "vals");
  return (
    <AppModal
      width="600px"
      setStep={setStep}
      step={step}
      setOpen={setOpen}
      open={open}
      loading={isPending}
      submit={() => {
        mutate(formik.values);
      }}
      formik={formik}
      title={item ? "Edit Company Bank" : "Add Company Bank"}
      finish={() => {
        setStep(0);
        setOpen(false);
        finished();
      }}
    >
      <SwitchInput formik={formik} name={"status"} label={"Status"} />
      <br />
      <FormInput
        label="Company Bank Name"
        name="bankName"
        width="100%"
        formik={formik}
      />
      <FormTextArea
        label="Company Bank Description"
        name="description"
        width="100%"
        formik={formik}
      />
      <Grid columns={"2"} gap={"4"}>
        <FormInput
          label="Account Name"
          name="accountName"
          width="100%"
          formik={formik}
        />
        <FormInputNumber
          label="Account Number"
          name="accountNumber"
          width="100%"
          formik={formik}
        />
        <CurrencySelect
          label="Currency"
          name="currency[id]"
          width="100%"
          formik={formik}
          optionValue="id"
        />
        <FormInputNumber
          label="BIC/CBN Code"
          name="bic"
          width="100%"
          formik={formik}
        />
        <FormInputNumber
          label="Sort Code"
          name="sortCode"
          width="100%"
          formik={formik}
        />
        <FormInputNumber
          label="IBAN"
          name="iban"
          width="100%"
          formik={formik}
        />
      </Grid>
    </AppModal>
  );
}
