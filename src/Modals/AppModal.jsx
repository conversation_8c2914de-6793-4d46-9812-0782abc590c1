import Modal from "@/components/bits/Modal";
import SuccessModal from "./SuccessModal";
import ConfirmModal from "./ConfirmModal";

export default function AppModal({
  step = 0,
  setStep,
  open,
  setOpen,
  Icon,
  children,
  submit,
  finish,
  title,
  formik,
  loading,
  cancel,
  override,
  width = "480px",
  successMsg = "You have successfully confirmed the request.",
}) {
  return override ? (
    <div>{children}</div>
  ) : (
    <div>
      {step === 0 && (
        <Modal
          Icon={Icon}
          trigger={() => {
            setOpen(!open);
          }}
          submit={() => {
            formik.handleSubmit();
          }}
          title={title}
          open={open}
          width={width}
        >
          {setOpen && (
            <svg
              width="44"
              height="44"
              viewBox="0 0 44 44"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              style={{
                position: "absolute",
                top: "20px",
                right: "20px",
                cursor: "pointer",
              }}
              onClick={() => {
                setOpen(false);
                setStep(0);
                formik.resetForm();
              }}
            >
              <circle
                cx="22"
                cy="22"
                r="21.45"
                fill="white"
                stroke="#F0F0F0"
                stroke-width="1.1"
              />
              <path
                d="M28.6009 15.4009L15.4009 28.6009M15.4009 15.4009L28.6009 28.6009"
                stroke="#666D80"
                stroke-width="2.2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          )}
          {children}
        </Modal>
      )}
      {step === 1 && (
        <ConfirmModal
          open={open}
          description="You are about to confirm this request. Are you sure?."
          confirm={submit}
          loading={loading}
          cancel={() => {
            setStep((curr) => curr - 1);
            cancel();
          }}
        />
      )}
      {step === 2 && (
        <SuccessModal
          open={open}
          description={successMsg}
          finish={finish}
          formik={formik}
        />
      )}
    </div>
  );
}
