/* eslint-disable react-hooks/exhaustive-deps */
import Modal from "@/components/bits/Modal";
import DividerHorizontal from "@/components/DividerHorizontal";
import { ApiServiceAdmin } from "@/service/admin-services";
import { Flex } from "@radix-ui/themes";
import { useQuery } from "@tanstack/react-query";
import { X } from "lucide-react";
import { useEffect } from "react";

export default function ViewTransactionComments({ setOpen, open, data }) {
  const { data: comments, refetch } = useQuery({
    queryKey: ["ViewTransactionCommentsQuery"],
    queryFn: () =>
      ApiServiceAdmin.ViewTransactionCommentsQuery(
        data?.paymentRef?.replace(/[A-Za-z]/g, "")
      ),
    enabled: !!data?.sn,
  });
  console.log(comments, "comments");

  useEffect(() => {
    refetch(data?.paymentRef?.replace(/[A-Za-z]/g, ""));
  }, []);
  return (
    <Modal
      dialogBtns={false}
      open={open}
      title=""
      trigger={() => {
        setOpen(!open);
      }}
    >
      <Flex
        justify={"between"}
        align={"center"}
        style={{
          position: "absolute",
          top: "0",
          right: "0",
          padding: "20px",
          width: "100%",
        }}
      >
        <h3 style={{ fontWeight: 500 }}>View Comments</h3>

        <X
          style={{
            cursor: "pointer",
          }}
          onClick={() => {
            setOpen(false);
          }}
        />
      </Flex>
      <br />
      <div
        style={{
          marginBottom: "-20px",
        }}
      >
        {comments?.data?.map((itm, idx) => {
          return (
            <>
              <Flex justify={"between"} py={"3"}>
                <div>
                  <div
                    style={{
                      color: "#000000",
                      fontSize: "14px",
                    }}
                  >
                    {itm?.comment}
                  </div>
                  <div
                    style={{
                      color: "#36394A",
                      fontSize: "14px",
                    }}
                  >
                    Submitted by {itm?.commentByName}
                  </div>
                </div>
              </Flex>
              {idx === comments?.data?.length - 1 ? (
                ""
              ) : (
                <DividerHorizontal style={{ margin: 0 }} />
              )}
            </>
          );
        })}
      </div>
    </Modal>
  );
}
