import * as Yup from "yup";
import { useState } from "react";
import AppModal from "./AppModal";
import FormInput from "@/components/bits/FormInput";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useMutation } from "@tanstack/react-query";
import { useFormik } from "formik";
import MainSelect from "@/components/bits/MainSelect";

export default function EditProfessionModal({ setOpen, open, finished, item }) {
  const [step, setStep] = useState(0);

  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.EditProfessionMutation,
    onSuccess: () => {
      setStep((curr) => curr + 1);
    },
    onError: () => {
      return;
    },
  });

  const formik = useFormik({
    initialValues: {
      id: item?.id,
      name: item?.name,
      riskLevel: {
        id: item?.riskLevel?.id,
      },
    },
    validationSchema: Yup.object().shape({
      name: Yup.string().required("Name is required"),
      riskLevel: Yup.object({
        id: Yup.string().required("Risk Level is required"),
      }),
    }),
    onSubmit: () => {
      setStep((curr) => curr + 1);
    },
  });
  console.log(item, "item");
  return (
    <AppModal
      setStep={setStep}
      step={step}
      setOpen={setOpen}
      open={open}
      loading={isPending}
      submit={() => {
        mutate(formik.values);
      }}
      formik={formik}
      title="Edit Profession"
      finish={() => {
        setStep(0);
        setOpen(false);
        finished();
      }}
    >
      <FormInput label="Profession" name="name" width="100%" formik={formik} />
      <MainSelect
        label="Status"
        name="status"
        width="100%"
        options={[
          { label: "Active", value: "Active" },
          { label: "Inactive", value: "Inactive" },
        ]}
        formik={formik}
      />
      <MainSelect
        label="Risk Level"
        name="riskLevel[id]"
        width="100%"
        options={[
          { label: "Low", value: 1 },
          { label: "Medium", value: 2 },
          { label: "High", value: 3 },
        ]}
        formik={formik}
      />
    </AppModal>
  );
}
