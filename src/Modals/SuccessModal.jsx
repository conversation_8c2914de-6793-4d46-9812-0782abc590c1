import AppButton from "@/components/bits/AppButton";
import useScreenSize from "@/lib/useScreenSize";
import { AlertDialog } from "@radix-ui/themes";

export default function SuccessModal({
  open,
  title = "Successful!",
  description,
  width = "500px",
  finish,
  padding,
}) {
  const { width: screenWidth } = useScreenSize();

  return (
    <AlertDialog.Root open={open}>
      <AlertDialog.Content
        align="center"
        maxWidth={width}
        style={{
          padding: screenWidth < 600 ? "60px 20px" : padding || "60px 20px",
          borderRadius: "24px",
        }}
      >
        <center>
          <svg
            width="61"
            height="60"
            viewBox="0 0 61 60"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect
              x="0.5"
              width="60"
              height="60"
              rx="30"
              fill="#FF6500"
              fill-opacity="0.1"
            />
            <path
              d="M41.75 23.7508L26.75 38.7508L19.875 31.8758L21.6375 30.1133L26.75 35.2133L39.9875 21.9883L41.75 23.7508Z"
              fill="#FF6500"
            />
          </svg>

          <AlertDialog.Title
            style={{
              fontFamily: "Fira Sans",
              color: "#FF6500",
            }}
          >
            {title}
          </AlertDialog.Title>
          <AlertDialog.Description
            size="3"
            style={{
              fontFamily: "Fira Sans",
              color: "#667085",
            }}
          >
            {description}
          </AlertDialog.Description>

          <br />
          <AppButton
            placeholder="Ok"
            width="180px"
            textColor="#fff"
            loading={false}
            disabled={false}
            onClick={finish}
          />
        </center>
      </AlertDialog.Content>
    </AlertDialog.Root>
  );
}
