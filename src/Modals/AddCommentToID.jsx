import { useState } from "react";
import AppModal from "./AppModal";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useMutation } from "@tanstack/react-query";
import { useFormik } from "formik";
import { useAuth } from "@/context/global.context";
import * as Yup from "yup";
import FormTextArea from "@/components/bits/FormTextArea";

export default function AddCommentToID({ setOpen, open, finished, item }) {
  const { user_id } = useAuth();
  const [step, setStep] = useState(0);

  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.AddCommentToIDMutation,
    onSuccess: () => {
      setStep((curr) => curr + 1);
      formik.setFieldValue("reason", "");
    },
    onError: () => {
      return;
    },
  });
  const Schema = Yup.object().shape({
    reason: Yup.string().required("Reason is required"),
  });
  const formik = useFormik({
    initialValues: {
      reason: "",
    },
    validationSchema: Schema,
    onSubmit: () => {
      setStep((curr) => curr + 1);
    },
  });

  console.log(formik.values, "vals");
  return (
    <AppModal
      setStep={setStep}
      step={step}
      setOpen={setOpen}
      open={open}
      loading={isPending}
      submit={() => {
        mutate({
          userId: item?.userId,
          documentId: item?.id,
          commentBy: user_id,
          comment: formik.values.reason,
        });
      }}
      formik={formik}
      title="Add Comment"
      finish={() => {
        setStep(0);
        setOpen(false);
        finished();
      }}
    >
      <FormTextArea
        label="Comment"
        name="reason"
        width="100%"
        formik={formik}
      />
    </AppModal>
  );
}
