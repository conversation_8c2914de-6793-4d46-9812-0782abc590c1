import { useState } from "react";
import AppModal from "./AppModal";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useMutation } from "@tanstack/react-query";
import { useFormik } from "formik";
import { useAuth } from "@/context/global.context";
import * as Yup from "yup";
import FormInput from "@/components/bits/FormInput";

export default function DeclineClientFundRequestModal({
  setOpen,
  open,
  finished,
  item,
}) {
  const { user_id } = useAuth();
  const [step, setStep] = useState(0);

  const { mutate, isPending, data } = useMutation({
    mutationFn: ApiServiceAdmin.DeclineWithdrawalRequestMutation,
    onSuccess: () => {
      setStep((curr) => curr + 1);
    },
    onError: () => {
      return;
    },
  });
  const Schema = Yup.object().shape({
    reason: Yup.string().required("Reason is required"),
  });
  const formik = useFormik({
    initialValues: {
      reason: "",
    },
    validationSchema: Schema,
    onSubmit: () => {
      setStep((curr) => curr + 1);
    },
  });

  console.log(formik.values, "vals");
  return (
    <AppModal
      successMsg={data?.message}
      setStep={setStep}
      step={step}
      setOpen={setOpen}
      open={open}
      loading={isPending}
      submit={() => {
        mutate({
          adminId: user_id,
          updateType: 2,
          userId: item?.userId,
          pamentGatewayId: formik.values.pamentGatewayId,
          WithdrawalRequest: {
            id: item?.id,
            amountPaid: formik.values.amountPaid,
          },
        });
      }}
      formik={formik}
      title="Confirm Action"
      finish={() => {
        setStep(0);
        setOpen(false);
        finished();
      }}
    >
      <p
        style={{
          fontSize: "13px",
          marginBottom: "10px",
        }}
      >
        Are you sure you want to decline this request? This action cannot be
        undone.
      </p>
      <FormInput
        label="Comment a reason"
        name="reason"
        width="100%"
        formik={formik}
      />
    </AppModal>
  );
}
