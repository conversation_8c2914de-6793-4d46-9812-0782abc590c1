import { useState } from "react";
import AppModal from "./AppModal";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useFormik } from "formik";
import MainSelect from "@/components/bits/MainSelect";
import * as Yup from "yup";
import FormInput from "@/components/bits/FormInput";
import CurrencySelect from "@/components/bits/CurrencySelect";

export default function AddPayoutProcessorModal({ setOpen, open, finished }) {
  const [step, setStep] = useState(0);

  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.AddPayoutProcessorMutation,
    onSuccess: () => {
      setStep((curr) => curr + 1);
    },
    onError: () => {
      return;
    },
  });
  const Schema = Yup.object().shape({
    name: Yup.string().required("Name is required"),
    description: Yup.string().required("Description is required"),
    currency: Yup.object({
      id: Yup.string().required("Currency is required"),
    }),
    payoutChannel: Yup.object({
      id: Yup.string().required("Channel is required"),
    }),
    payoutProvider: Yup.object({
      id: Yup.string().required("Provider is required"),
    }),
  });
  const formik = useFormik({
    initialValues: {
      name: "",
      description: "",
      currency: {
        id: "",
      },
      payoutChannel: {
        id: "",
      },
      payoutProvider: {
        id: "",
      },
    },
    validationSchema: Schema,
    onSubmit: () => {
      setStep((curr) => curr + 1);
    },
    validateOnChange: true, // Ensure this is true
  });

  const { data } = useQuery({
    queryKey: ["GetPayoutProviderQuery"],
    queryFn: () => ApiServiceAdmin.GetPayoutProviderQuery(),
  });

  const { data: list } = useQuery({
    queryKey: ["GetPayoutChannelQuery"],
    queryFn: () => ApiServiceAdmin.GetPayoutChannelQuery(),
  });

  const providers =
    data &&
    data.data.map((itm) => {
      return {
        label: itm.name,
        value: itm.id,
      };
    });

  const channels =
    list &&
    list.data.map((itm) => {
      return {
        label: itm.name,
        value: itm.id,
      };
    });

  console.log(formik.values, formik.errors, "vals");
  return (
    <AppModal
      setStep={setStep}
      step={step}
      setOpen={setOpen}
      open={open}
      loading={isPending}
      submit={() => {
        mutate(formik.values);
      }}
      formik={formik}
      title="Add Payment Processor"
      finish={() => {
        setStep(0);
        setOpen(false);
        finished();
      }}
    >
      <FormInput label="Name" name="name" width="100%" amount formik={formik} />
      <FormInput
        label="Description"
        name="description"
        width="100%"
        amount
        formik={formik}
      />
      <CurrencySelect
        label="Currency"
        placeholder="Select..."
        formik={formik}
        name="currency[id]"
        optionValue={"id"}
        width="100%"
      />
      <MainSelect
        label="Payout Channel"
        placeholder="Select..."
        options={channels}
        formik={formik}
        name="payoutChannel[id]"
      />
      <MainSelect
        label="Payout Provider"
        placeholder="Select..."
        options={providers}
        formik={formik}
        name="payoutProvider[id]"
      />
    </AppModal>
  );
}
