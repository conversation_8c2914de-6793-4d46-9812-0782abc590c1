import AppButton from "@/components/bits/AppButton";
import CustomTable from "@/components/bits/CustomTable";
import Modal from "@/components/bits/Modal";
import { StatusBadge } from "@/components/bits/StatusBadge";
import { Flex } from "@radix-ui/themes";
import { Download, X } from "lucide-react";

export default function TransactionReceipt({ setOpen, open, data }) {
  const details = data;
  return (
    <Modal
      dialogBtns={false}
      open={open}
      title=""
      width="700px"
      trigger={() => {
        setOpen(!open);
      }}
    >
      <Flex
        justify={"between"}
        align={"center"}
        style={{
          position: "absolute",
          top: "0",
          right: "0",
          padding: "20px",
          width: "100%",
        }}
      >
        <AppButton
          IconLeft={Download}
          placeholder={"Download Receipt"}
          outline
          width="fit-content"
        />

        <X
          style={{
            cursor: "pointer",
          }}
          onClick={() => {
            setOpen(false);
          }}
        />
      </Flex>
      <div
        style={{
          textAlign: "center",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          gap: "20px",
        }}
      >
        <img src="/logo.svg" width={100} />
        <div>
          <h2 style={{ fontWeight: 500 }}>BCON Solutions Ltd</h2>
          <p
            style={{
              color: "#A5A6A7",
            }}
          >
            {details?.senderAddress}
          </p>
        </div>
        <div>
          <h2 style={{ fontWeight: 500 }}>Payment Advice</h2>
          <p
            style={{
              color: "#A5A6A7",
            }}
          >
            {details?.paymentDate}
          </p>
        </div>

        <StatusBadge status={details?.paymentStatus || ""} />

        <div>
          <h2 style={{ fontWeight: 500 }}>From</h2>
          <p
            style={{
              color: "#A5A6A7",
            }}
          >
            {details?.senderName}
          </p>
        </div>

        <CustomTable
          tableColumns={[
            { title: "Date Created", dataIndex: "paymentDate", width: 100 },
            { title: "Reference", dataIndex: "paymentRef", width: 100 },
            { title: "Beneficiary Name", dataIndex: "name", width: 100 },
            { title: "Amount", dataIndex: "paymentAmount", width: 100 },
          ]}
          tableWidth={"100%"}
          arrayData={[
            {
              name: details?.userBeneficiary?.beneficiaryName,
              paymentDate: details?.paymentDate,
              paymentRef: details?.paymentRef,
              paymentAmount: details?.paymentAmount,
            },
          ]}
          directPagination={false}
        />
      </div>
    </Modal>
  );
}
