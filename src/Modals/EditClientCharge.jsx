import { useState } from "react";
import AppModal from "./AppModal";
import { ApiServiceAdmin } from "@/service/admin-services";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useFormik } from "formik";
import * as Yup from "yup";
import MainSelect from "@/components/bits/MainSelect";
import FormInputNumber from "@/components/bits/FormInputNumber";
import { useAuth } from "@/context/global.context";
import CurrencySelect from "@/components/bits/CurrencySelect";

export default function EditClientCharge({ setOpen, open, finished, item }) {
  const [step, setStep] = useState(0);
  const { user_id } = useAuth;
  const { mutate, isPending } = useMutation({
    mutationFn: ApiServiceAdmin.UpdateClientChargesMutation,
    onSuccess: () => {
      setStep((curr) => curr + 1);
    },
    onError: () => {
      return;
    },
  });

  const Schema = Yup.object().shape({
    data: Yup.object({
      baseValue: Yup.string().required("Base Value is required"),
      minimumFixedCapped: Yup.string().required("Minimum is required"),
      maximumFixedCapped: Yup.string().required("Maximum is required"),
    }),
  });
  const formik = useFormik({
    initialValues: {
      adminId: user_id === undefined ? 0 : user_id,
      clientId: item?.userId,
      data: {
        id: item?.id,
        currency: {
          id: item?.currency?.id,
        },
        payoutChargeType: {
          id: item?.payoutChargeType?.id,
        },
        payOutClientTransactionType: {
          typeId: item?.payOutClientTransactionType?.typeId,
        },
        baseValue: item?.baseValue,
        minimumFixedCapped: item?.minimumFixedCapped,
        maximumFixedCapped: item?.maximumFixedCapped,
      },
    },
    validationSchema: Schema,
    onSubmit: () => {
      setStep((curr) => curr + 1);
    },
  });

  const { data: chargesTypes } = useQuery({
    queryKey: ["GetChargeTypessQuery"],
    queryFn: () => ApiServiceAdmin.GetChargeTypesQuery(),
  });

  const { data: transactionTypes } = useQuery({
    queryKey: ["GetTransactionssTypeQuery"],
    queryFn: () => ApiServiceAdmin.GetTransactionTypeQuery(),
  });

  console.log(formik.values, item, "vals");
  return (
    <AppModal
      setStep={setStep}
      step={step}
      setOpen={setOpen}
      open={open}
      loading={isPending}
      submit={() => {
        mutate({
          id: item?.id,
          baseValue: formik?.values?.baseValue,
          minimumFixedCapped: formik?.values?.minimumFixedCapped,
          maximumFixedCapped: formik?.values?.maximumFixedCapped,
        });
      }}
      formik={formik}
      title="Edit Charge"
      finish={() => {
        setStep(0);
        setOpen(false);
        finished();
      }}
    >
      <CurrencySelect
        label="Currency"
        name={"data[currency][id]"}
        width="100%"
        disabled
        itemValue={item?.currency?.id}
        optionValue="id"
        formik={formik}
      />
      <MainSelect
        label="Charge Type"
        name={"data[payoutChargeType][id]"}
        width="100%"
        formik={formik}
        itemValue={item?.payoutChargeType?.id}
        disabled
        options={chargesTypes?.data?.map((itm) => {
          return {
            ...itm,
            label: itm?.typeName,
            value: itm?.id,
          };
        })}
      />

      <MainSelect
        label="Transaction Type"
        name={"data[payOutClientTransactionType][typeId]"}
        width="100%"
        itemValue={item?.payOutClientTransactionType?.typeId}
        disabled
        formik={formik}
        options={transactionTypes?.data?.map((itm) => {
          return {
            ...itm,
            label: itm?.typeName,
            value: itm?.typeId,
          };
        })}
        optionValue="typeId"
      />
      <FormInputNumber
        label={"Base Value"}
        name={"data[baseValue]"}
        formik={formik}
        amount
      />
      <FormInputNumber
        label={"Min Fixed Capped Amt"}
        name={"data[minimumFixedCapped]"}
        formik={formik}
        amount
      />
      <FormInputNumber
        label={"Max Fixed Capped Amt"}
        name={"data[maximumFixedCapped]"}
        formik={formik}
        amount
      />
    </AppModal>
  );
}
