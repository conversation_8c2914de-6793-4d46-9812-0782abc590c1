*,
html {
  font-family: "Fira Sans", sans-serif;
  scroll-behavior: smooth;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  transition: all 0.3s;
  color: #000;
}
a {
  text-decoration: none;
  color: #000;
}
body {
  background-color: #f8f8f8;
}
img {
  pointer-events: none;
}

::-webkit-scrollbar {
  width: 0;
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}

.swiper-pagination-bullets {
  display: none !important;
}
.css-1u9des2-indicatorSeparator {
  display: none;
}
.range {
  width: 300px;
  appearance: none;
  outline: none;
  padding: 20px 15px;
  font-size: 16px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  background-color: #f6f6f7;
  position: relative;
}

.dropdown-demo > .arco-btn {
  padding: 0 8px;
  font-weight: normal;
}

.dropdown-demo .arco-dropdown-popup-visible .arco-icon-down {
  transform: rotate(180deg);
}

.arco-drawer-content {
  overflow: hidden !important;
  padding: 0 !important;
  position: relative;
}
.calendar {
  position: absolute !important;
  z-index: 1 !important;
  background-color: #ffffff;
  border: 1px solid #d5d8dd;
  border-radius: 12px;
  max-width: 300px !important;
}
tr.highlight-duplicate-row,
tr.highlight-duplicate-row > td {
  background-color: #ff4f4f41 !important;
}
