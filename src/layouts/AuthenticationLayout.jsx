import { Outlet, useLocation, useNavigate } from "react-router-dom";
import styled from "styled-components";
import Noise from "@/assets/noise2.png";
import { useEffect } from "react";
import { useAuth } from "@/context/global.context";

export default function AuthenticationLayout({
  title = "Welcome",
  desc = "Please select account type to continue.",
}) {
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const { pathname } = useLocation();
  useEffect(() => {
    window.scrollTo(0, 0);
    if (isAuthenticated) {
      navigate("/dashboard");
    }
  }, [pathname, isAuthenticated, navigate]);
  return (
    <AuthStyle>
      <img
        src={Noise}
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          width: "100vw",
          height: "100vh",
          objectFit: "cover",
        }}
      />
      <div className="child">
        <div className="child_outlet">
          <div className="top">
            <img src="/logo.svg" />
            <h2>{title}</h2>
            <p>{desc}</p>
          </div>
          <Outlet />
        </div>
      </div>
    </AuthStyle>
  );
}

const AuthStyle = styled.div`
  position: relative;
  height: 100vh;
  padding: 20px;
  display: grid;
  place-items: center;
  .child {
    display: grid;
    place-items: center;
    padding: 100px 50px;
    border-radius: 24px;
    border: 1px solid #a2a0a040;
    position: relative;
    z-index: 1;
    background-color: #ffffff;
    .child_outlet {
      width: 500px;
    }
    .top {
      text-align: center;
    }
    h2 {
      color: #101828;
      font-size: 1.6vw;
      margin: 10px 0;
      font-weight: 500;
    }
    p {
      color: #667085;
      font-size: 1vw;
      margin-bottom: 20px;
    }
  }

  @media screen and (max-width: 1023px) {
    padding: 0px;

    .child {
      width: 100%;
      height: 100vh;

      h2 {
        color: #101828;
        font-size: 22px;
        margin: 10px 0;
        font-weight: 500;
      }
      p {
        color: #667085;
        font-size: 14px;
        margin-bottom: 20px;
      }
      .child_outlet {
        width: 90%;
      }
    }
  }
  @media screen and (max-width: 600px) {
    .child {
      padding: 0px;
      border: 1px solid #ffffff;
    }
  }
`;
