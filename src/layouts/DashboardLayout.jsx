//import LogoIcon from "@/assets/icons/LogoIcon";
//import PageTransition from "@/components/bits/PageTransition/PageTransition";
import TopBar from "@/components/bits/TopBar";
import Navbar from "@/components/Navbar";
import { useEffect /* useRef */ } from "react";
import { Outlet, useLocation, useNavigate } from "react-router-dom";
import styled from "styled-components";
import { Drawer } from "@arco-design/web-react";
import { useState } from "react";
import useScreenSize from "@/lib/useScreenSize";
import { useAuth } from "@/context/global.context";
export default function DashboardLayout() {
  //const nodeRef = useRef();
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const [visible, setVisible] = useState(false);
  const { width } = useScreenSize();

  useEffect(() => {
    window.scrollTo(0, 0);
    if (!isAuthenticated) {
      navigate("/");
    }
  }, [pathname, isAuthenticated, navigate]);
  return (
    <>
      <Drawer
        title={null}
        footer={null}
        closable={false}
        visible={visible}
        placement="left"
        width={width <= 650 ? "100%" : 280}
        onOk={() => {
          setVisible(false);
        }}
        onCancel={() => {
          setVisible(false);
        }}
        style={{
          overflow: "hidden",
        }}
      >
        <Navbar
          onClick={() => {
            setVisible(false);
          }}
          logo={
            <div
              style={{
                display: "grid",
                placeItems: "center",
              }}
            >
              <img src="/logo.svg" />
            </div>
          }
        />
      </Drawer>
      <DashboardLayoutStyle>
        <Navbar
          className="nav"
          logo={
            <div
              style={{
                display: "grid",
                placeItems: "center",
              }}
            >
              <img src="/logo.svg" />
            </div>
          }
        />
        <div className="child">
          <TopBar
            width={width}
            onClick={() => {
              setVisible(true);
            }}
          />
          <div /* nodeRef={nodeRef}  */ className="outlet_child">
            <Outlet />
          </div>
        </div>
      </DashboardLayoutStyle>
    </>
  );
}

const DashboardLayoutStyle = styled.div`
  display: grid;
  grid-template-columns: 310px 1fr;
  height: 100vh;
  overflow: hidden;

  .child {
    background-color: #f8f8f8;
    z-index: 1;
    .outlet_child {
      overflow: hidden;
      overflow-y: scroll;
      padding: 30px;
      height: 92vh;
    }
  }

  @media screen and (max-width: 1280px) {
    overflow: auto;
    .nav {
      display: none;
    }
    width: 100%;
    grid-template-columns: 1fr;
    .child {
      .outlet_child {
        overflow: auto;
        padding: 15px;
        height: fit-content;
      }
    }
  }
`;
